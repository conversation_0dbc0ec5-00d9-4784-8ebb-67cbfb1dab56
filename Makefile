# GEM-Fusion Accelerator Makefile
# Provides convenient commands for development and deployment

.PHONY: help build test clean run stop logs setup integration-test benchmark

# Default target
help:
	@echo "GEM-Fusion Accelerator - Available Commands:"
	@echo ""
	@echo "Development:"
	@echo "  setup           - Install dependencies and setup development environment"
	@echo "  build           - Build all components"
	@echo "  test            - Run all tests"
	@echo "  run             - Start services with Docker Compose"
	@echo "  run-dev         - Start services in development mode"
	@echo "  stop            - Stop all services"
	@echo "  clean           - Clean build artifacts and data"
	@echo ""
	@echo "Testing:"
	@echo "  test-go         - Run Go backend tests"
	@echo "  test-mojo       - Run Mojo compute engine tests"
	@echo "  integration-test - Run end-to-end integration tests"
	@echo "  benchmark       - Run performance benchmarks"
	@echo ""
	@echo "Deployment:"
	@echo "  build-prod      - Build production Docker images"
	@echo "  deploy-prod     - Deploy to production environment"
	@echo ""
	@echo "Utilities:"
	@echo "  logs            - Show service logs"
	@echo "  logs-follow     - Follow service logs"
	@echo "  health          - Check service health"
	@echo "  stats           - Show service statistics"

# Development setup
setup:
	@echo "Setting up development environment..."
	@echo "Checking Go installation..."
	@go version || (echo "Go not found. Please install Go 1.21+"; exit 1)
	@echo "Checking Mojo installation..."
	@mojo --version || (echo "Mojo not found. Please install Mojo SDK"; exit 1)
	@echo "Checking Docker installation..."
	@docker --version || (echo "Docker not found. Please install Docker"; exit 1)
	@echo "Installing Go dependencies..."
	@cd go-bioserv && go mod tidy
	@echo "Installing Python dependencies for Mojo..."
	@cd mojo-metabolic-core && pip install -r requirements.txt
	@echo "Creating data directories..."
	@mkdir -p data ipc
	@echo "Making scripts executable..."
	@chmod +x scripts/*.sh
	@echo "✓ Development environment setup complete!"

# Build targets
build:
	@echo "Building all components..."
	@$(MAKE) build-go
	@$(MAKE) build-mojo
	@echo "✓ Build complete!"

build-go:
	@echo "Building Go backend..."
	@cd go-bioserv && go build -o bin/server cmd/server/main.go
	@echo "✓ Go backend built"

build-mojo:
	@echo "Validating Mojo compute engine..."
	@cd mojo-metabolic-core && mojo run src/main.mojo test
	@echo "✓ Mojo compute engine validated"

build-prod:
	@echo "Building production Docker images..."
	@docker-compose build --no-cache
	@echo "✓ Production images built"

# Test targets
test: test-go test-mojo
	@echo "✓ All tests passed!"

test-go:
	@echo "Running Go backend tests..."
	@cd go-bioserv && go test ./... -v
	@echo "✓ Go tests passed"

test-mojo:
	@echo "Running Mojo compute engine tests..."
	@cd mojo-metabolic-core && mojo run tests/test_fba.mojo
	@echo "✓ Mojo tests passed"

integration-test:
	@echo "Running integration tests..."
	@chmod +x scripts/integration_test.sh
	@scripts/integration_test.sh
	@echo "✓ Integration tests passed"

benchmark:
	@echo "Running performance benchmarks..."
	@cd mojo-metabolic-core && mojo run src/main.mojo benchmark
	@echo "✓ Benchmarks complete"

# Runtime targets
run:
	@echo "Starting services with Docker Compose..."
	@docker-compose up --build

run-dev:
	@echo "Starting services in development mode..."
	@docker-compose -f docker-compose.yml -f docker-compose.dev.yml up --build

run-local:
	@echo "Starting services locally (without Docker)..."
	@echo "Starting Go backend..."
	@cd go-bioserv && go run cmd/server/main.go --mock &
	@echo "Services started. Go backend available at http://localhost:8080"
	@echo "Press Ctrl+C to stop"

stop:
	@echo "Stopping all services..."
	@docker-compose down
	@echo "✓ Services stopped"

# Monitoring and logs
logs:
	@docker-compose logs

logs-follow:
	@docker-compose logs -f

logs-go:
	@docker-compose logs go-bioserv

logs-mojo:
	@docker-compose logs mojo-compute

health:
	@echo "Checking service health..."
	@curl -s http://localhost:8080/health | jq '.' || echo "Service not responding"

stats:
	@echo "Service statistics:"
	@curl -s http://localhost:8080/stats | jq '.' || echo "Service not responding"

# Cleanup targets
clean:
	@echo "Cleaning build artifacts and data..."
	@rm -rf go-bioserv/bin/
	@rm -rf data/
	@rm -rf ipc/
	@docker-compose down -v --remove-orphans
	@docker system prune -f
	@echo "✓ Cleanup complete"

clean-data:
	@echo "Cleaning data directories..."
	@rm -rf data/ ipc/
	@mkdir -p data ipc
	@echo "✓ Data cleaned"

# Development utilities
format:
	@echo "Formatting code..."
	@cd go-bioserv && go fmt ./...
	@echo "✓ Code formatted"

lint:
	@echo "Running linters..."
	@cd go-bioserv && go vet ./...
	@echo "✓ Linting complete"

deps-update:
	@echo "Updating dependencies..."
	@cd go-bioserv && go get -u ./... && go mod tidy
	@cd mojo-metabolic-core && pip install -r requirements.txt --upgrade
	@echo "✓ Dependencies updated"

# Docker utilities
docker-build:
	@docker-compose build

docker-pull:
	@docker-compose pull

docker-clean:
	@docker system prune -af
	@docker volume prune -f

# Production deployment
deploy-prod:
	@echo "Deploying to production..."
	@docker-compose -f docker-compose.prod.yml up -d --build
	@echo "✓ Production deployment complete"

# Monitoring stack
monitoring-up:
	@echo "Starting monitoring stack..."
	@docker-compose --profile monitoring up -d
	@echo "✓ Monitoring stack started"
	@echo "Grafana: http://localhost:3000 (admin/admin)"
	@echo "Prometheus: http://localhost:9090"

monitoring-down:
	@echo "Stopping monitoring stack..."
	@docker-compose --profile monitoring down
	@echo "✓ Monitoring stack stopped"

# Database stack
db-up:
	@echo "Starting database stack..."
	@docker-compose --profile database up -d
	@echo "✓ Database stack started"
	@echo "PostgreSQL: localhost:5432 (gem_user/gem_password)"

db-down:
	@echo "Stopping database stack..."
	@docker-compose --profile database down
	@echo "✓ Database stack stopped"

# Example workflows
demo:
	@echo "Running demo workflow..."
	@$(MAKE) run &
	@sleep 10
	@$(MAKE) integration-test
	@$(MAKE) stop

quick-test:
	@echo "Running quick test cycle..."
	@$(MAKE) test
	@$(MAKE) build
	@echo "✓ Quick test cycle complete"

full-test:
	@echo "Running full test cycle..."
	@$(MAKE) clean
	@$(MAKE) setup
	@$(MAKE) test
	@$(MAKE) build
	@$(MAKE) run &
	@sleep 15
	@$(MAKE) integration-test
	@$(MAKE) stop
	@echo "✓ Full test cycle complete"

# Documentation
docs-serve:
	@echo "Serving documentation..."
	@python3 -m http.server 8000 --directory docs/
	@echo "Documentation available at http://localhost:8000"

# Version and release
version:
	@echo "GEM-Fusion Accelerator v1.0.0"
	@echo "Go version: $$(go version)"
	@echo "Mojo version: $$(mojo --version 2>/dev/null || echo 'Not available')"
	@echo "Docker version: $$(docker --version)"

# Help for specific components
help-go:
	@echo "Go Backend Commands:"
	@echo "  cd go-bioserv && go run cmd/server/main.go --help"

help-mojo:
	@echo "Mojo Compute Engine Commands:"
	@echo "  cd mojo-metabolic-core && mojo run src/main.mojo --help"

# Environment checks
check-env:
	@echo "Checking environment..."
	@echo "Go: $$(go version 2>/dev/null || echo 'Not installed')"
	@echo "Mojo: $$(mojo --version 2>/dev/null || echo 'Not installed')"
	@echo "Docker: $$(docker --version 2>/dev/null || echo 'Not installed')"
	@echo "Docker Compose: $$(docker-compose --version 2>/dev/null || echo 'Not installed')"
	@echo "Python: $$(python3 --version 2>/dev/null || echo 'Not installed')"
	@echo "curl: $$(curl --version 2>/dev/null | head -1 || echo 'Not installed')"
	@echo "jq: $$(jq --version 2>/dev/null || echo 'Not installed')"
