import React, { useState } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>,
  TextField,
  Button,
  Box,
  Alert,
  CircularProgress,
  Paper,
  Divider,
} from '@mui/material';
import {
  CloudUpload as CloudUploadIcon,
  CheckCircle as CheckCircleIcon,
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { uploadModel } from '../services/api';

const ModelUpload: React.FC = () => {
  const navigate = useNavigate();
  const [file, setFile] = useState<File | null>(null);
  const [name, setName] = useState('');
  const [description, setDescription] = useState('');
  const [uploading, setUploading] = useState(false);
  const [success, setSuccess] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = event.target.files?.[0];
    if (selectedFile) {
      setFile(selectedFile);
      if (!name) {
        // Auto-fill name from filename
        const fileName = selectedFile.name.replace(/\.[^/.]+$/, '');
        setName(fileName);
      }
    }
  };

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();
    
    if (!file || !name.trim()) {
      setError('Please select a file and provide a name');
      return;
    }

    try {
      setUploading(true);
      setError(null);
      
      const response = await uploadModel(file, name.trim(), description.trim());
      
      if (response.success) {
        setSuccess(true);
        setTimeout(() => {
          navigate('/models');
        }, 2000);
      } else {
        setError(response.error || 'Upload failed');
      }
    } catch (err) {
      setError('Failed to upload model. Please try again.');
    } finally {
      setUploading(false);
    }
  };

  const handleReset = () => {
    setFile(null);
    setName('');
    setDescription('');
    setError(null);
    setSuccess(false);
  };

  if (success) {
    return (
      <Box display="flex" flexDirection="column" alignItems="center" textAlign="center" mt={4}>
        <CheckCircleIcon sx={{ fontSize: 64, color: 'success.main', mb: 2 }} />
        <Typography variant="h5" gutterBottom>
          Model Uploaded Successfully!
        </Typography>
        <Typography variant="body1" color="textSecondary">
          Redirecting to models page...
        </Typography>
      </Box>
    );
  }

  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        Upload Metabolic Model
      </Typography>
      
      <Typography variant="body1" color="textSecondary" sx={{ mb: 3 }}>
        Upload a metabolic model file (JSON, SBML, or other supported formats) to start analyzing metabolic networks.
      </Typography>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      <Card>
        <CardContent>
          <form onSubmit={handleSubmit}>
            <Box sx={{ mb: 3 }}>
              <Typography variant="h6" gutterBottom>
                File Selection
              </Typography>
              
              <Paper
                variant="outlined"
                sx={{
                  p: 3,
                  textAlign: 'center',
                  border: '2px dashed',
                  borderColor: file ? 'success.main' : 'grey.300',
                  backgroundColor: file ? 'success.50' : 'grey.50',
                  cursor: 'pointer',
                  '&:hover': {
                    borderColor: 'primary.main',
                    backgroundColor: 'primary.50',
                  },
                }}
                onClick={() => document.getElementById('file-input')?.click()}
              >
                <input
                  id="file-input"
                  type="file"
                  accept=".json,.xml,.sbml,.txt"
                  onChange={handleFileChange}
                  style={{ display: 'none' }}
                />
                
                <CloudUploadIcon sx={{ fontSize: 48, color: 'grey.400', mb: 2 }} />
                
                {file ? (
                  <Box>
                    <Typography variant="h6" color="success.main">
                      {file.name}
                    </Typography>
                    <Typography variant="body2" color="textSecondary">
                      {(file.size / 1024).toFixed(1)} KB
                    </Typography>
                  </Box>
                ) : (
                  <Box>
                    <Typography variant="h6" gutterBottom>
                      Click to select a file
                    </Typography>
                    <Typography variant="body2" color="textSecondary">
                      Supported formats: JSON, SBML, XML, TXT
                    </Typography>
                  </Box>
                )}
              </Paper>
            </Box>

            <Divider sx={{ my: 3 }} />

            <Box sx={{ mb: 3 }}>
              <Typography variant="h6" gutterBottom>
                Model Information
              </Typography>
              
              <TextField
                fullWidth
                label="Model Name"
                value={name}
                onChange={(e) => setName(e.target.value)}
                required
                sx={{ mb: 2 }}
                placeholder="e.g., E. coli Core Model"
              />
              
              <TextField
                fullWidth
                label="Description"
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                multiline
                rows={3}
                placeholder="Optional description of the metabolic model..."
              />
            </Box>

            <Box display="flex" gap={2} justifyContent="flex-end">
              <Button
                variant="outlined"
                onClick={handleReset}
                disabled={uploading}
              >
                Reset
              </Button>
              
              <Button
                type="submit"
                variant="contained"
                disabled={!file || !name.trim() || uploading}
                startIcon={uploading ? <CircularProgress size={20} /> : <CloudUploadIcon />}
              >
                {uploading ? 'Uploading...' : 'Upload Model'}
              </Button>
            </Box>
          </form>
        </CardContent>
      </Card>
    </Box>
  );
};

export default ModelUpload;
