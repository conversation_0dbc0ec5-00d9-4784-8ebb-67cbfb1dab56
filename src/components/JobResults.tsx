import React, { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  Typography,
  Box,
  Alert,
  CircularProgress,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Button,
  Grid,
  Divider,
} from '@mui/material';
import {
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
  Refresh as RefreshIcon,
  Download as DownloadIcon,
} from '@mui/icons-material';
import { useParams, useNavigate } from 'react-router-dom';
import { getJobStatus, getJobResults, Job, JobResult } from '../services/api';

const JobResults: React.FC = () => {
  const { jobId } = useParams<{ jobId: string }>();
  const navigate = useNavigate();
  
  const [job, setJob] = useState<Job | null>(null);
  const [results, setResults] = useState<JobResult | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [polling, setPolling] = useState(false);

  useEffect(() => {
    if (jobId) {
      fetchJobData();
    }
  }, [jobId]);

  useEffect(() => {
    let interval: NodeJS.Timeout;
    
    if (job && (job.status === 'pending' || job.status === 'running')) {
      setPolling(true);
      interval = setInterval(() => {
        fetchJobData();
      }, 2000); // Poll every 2 seconds
    } else {
      setPolling(false);
    }

    return () => {
      if (interval) {
        clearInterval(interval);
      }
    };
  }, [job?.status]);

  const fetchJobData = async () => {
    if (!jobId) return;

    try {
      setLoading(true);
      setError(null);
      
      // Get job status
      const statusResponse = await getJobStatus(jobId);
      if (statusResponse.success && statusResponse.data) {
        setJob(statusResponse.data);
        
        // If job is completed, get results
        if (statusResponse.data.status === 'completed') {
          const resultsResponse = await getJobResults(jobId);
          if (resultsResponse.success && resultsResponse.data) {
            setResults(resultsResponse.data);
          }
        }
      } else {
        setError(statusResponse.error || 'Failed to load job data');
      }
    } catch (err) {
      setError('Failed to load job data');
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'success';
      case 'failed':
        return 'error';
      case 'running':
        return 'warning';
      case 'pending':
        return 'info';
      default:
        return 'default';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircleIcon />;
      case 'failed':
        return <ErrorIcon />;
      default:
        return null;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  const exportResults = () => {
    if (!results?.data) return;
    
    const dataStr = JSON.stringify(results.data, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `fba_results_${jobId}.json`;
    link.click();
    URL.revokeObjectURL(url);
  };

  if (loading && !job) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  if (!job) {
    return (
      <Alert severity="error">
        Job not found
      </Alert>
    );
  }

  return (
    <Box>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4">
          Job Results
        </Typography>
        <Button
          variant="outlined"
          onClick={() => navigate('/models')}
        >
          Back to Models
        </Button>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {/* Job Status Card */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
            <Typography variant="h6">Job Status</Typography>
            {polling && <CircularProgress size={20} />}
          </Box>
          
          <Grid container spacing={2}>
            <Grid item xs={12} sm={6} md={3}>
              <Typography variant="body2" color="textSecondary">Status:</Typography>
              <Chip
                icon={getStatusIcon(job.status)}
                label={job.status.toUpperCase()}
                color={getStatusColor(job.status) as any}
                sx={{ mt: 0.5 }}
              />
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Typography variant="body2" color="textSecondary">Job ID:</Typography>
              <Typography variant="body2" fontFamily="monospace">
                {job.id}
              </Typography>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Typography variant="body2" color="textSecondary">Created:</Typography>
              <Typography variant="body2">
                {formatDate(job.created_at)}
              </Typography>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Typography variant="body2" color="textSecondary">Updated:</Typography>
              <Typography variant="body2">
                {formatDate(job.updated_at)}
              </Typography>
            </Grid>
          </Grid>

          {(job.status === 'pending' || job.status === 'running') && (
            <Box mt={2}>
              <Button
                variant="outlined"
                startIcon={<RefreshIcon />}
                onClick={fetchJobData}
                disabled={loading}
              >
                Refresh
              </Button>
            </Box>
          )}
        </CardContent>
      </Card>

      {/* Results Card */}
      {job.status === 'completed' && results && (
        <Card>
          <CardContent>
            <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
              <Typography variant="h6">FBA Results</Typography>
              <Button
                variant="outlined"
                startIcon={<DownloadIcon />}
                onClick={exportResults}
              >
                Export Results
              </Button>
            </Box>

            {results.success && results.data ? (
              <Box>
                {/* Summary */}
                <Grid container spacing={3} sx={{ mb: 3 }}>
                  <Grid item xs={12} sm={6} md={3}>
                    <Paper variant="outlined" sx={{ p: 2, textAlign: 'center' }}>
                      <Typography variant="body2" color="textSecondary">
                        Objective Value
                      </Typography>
                      <Typography variant="h5" color="primary">
                        {results.data.objective_value.toFixed(6)}
                      </Typography>
                    </Paper>
                  </Grid>
                  <Grid item xs={12} sm={6} md={3}>
                    <Paper variant="outlined" sx={{ p: 2, textAlign: 'center' }}>
                      <Typography variant="body2" color="textSecondary">
                        Status
                      </Typography>
                      <Typography variant="h6">
                        {results.data.status}
                      </Typography>
                    </Paper>
                  </Grid>
                  <Grid item xs={12} sm={6} md={3}>
                    <Paper variant="outlined" sx={{ p: 2, textAlign: 'center' }}>
                      <Typography variant="body2" color="textSecondary">
                        Solver Time
                      </Typography>
                      <Typography variant="h6">
                        {results.data.solver_time.toFixed(3)}s
                      </Typography>
                    </Paper>
                  </Grid>
                  <Grid item xs={12} sm={6} md={3}>
                    <Paper variant="outlined" sx={{ p: 2, textAlign: 'center' }}>
                      <Typography variant="body2" color="textSecondary">
                        Flux Values
                      </Typography>
                      <Typography variant="h6">
                        {Object.keys(results.data.flux_values).length}
                      </Typography>
                    </Paper>
                  </Grid>
                </Grid>

                <Divider sx={{ my: 3 }} />

                {/* Flux Values Table */}
                <Typography variant="h6" gutterBottom>
                  Flux Values
                </Typography>
                
                <TableContainer component={Paper} variant="outlined">
                  <Table size="small">
                    <TableHead>
                      <TableRow>
                        <TableCell>Reaction ID</TableCell>
                        <TableCell align="right">Flux Value</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {Object.entries(results.data.flux_values)
                        .sort(([, a], [, b]) => Math.abs(b) - Math.abs(a))
                        .map(([reaction, flux]) => (
                          <TableRow key={reaction}>
                            <TableCell component="th" scope="row">
                              <Typography variant="body2" fontFamily="monospace">
                                {reaction}
                              </Typography>
                            </TableCell>
                            <TableCell align="right">
                              <Typography
                                variant="body2"
                                fontFamily="monospace"
                                color={flux === 0 ? 'textSecondary' : 'textPrimary'}
                              >
                                {flux.toFixed(6)}
                              </Typography>
                            </TableCell>
                          </TableRow>
                        ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              </Box>
            ) : (
              <Alert severity="error">
                Analysis failed: {results.error || 'Unknown error'}
              </Alert>
            )}
          </CardContent>
        </Card>
      )}

      {job.status === 'failed' && (
        <Alert severity="error">
          Job failed to complete. Please try running the analysis again.
        </Alert>
      )}
    </Box>
  );
};

export default JobResults;
