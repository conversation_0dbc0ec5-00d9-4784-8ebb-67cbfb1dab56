_cvxcore.cpython-313-darwin.so,sha256=yFoJ68AGbjIcKOHS4TNi3o8kIpj8NwGXAZy7KD0dUf0,1563560
_cvxpy_sparsecholesky.cpython-313-darwin.so,sha256=pa1mE9C9BfRufTDq6-khBRLfCpgfIqwR7Os2iROm6FU,1025312
cvxpy-1.6.5.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
cvxpy-1.6.5.dist-info/METADATA,sha256=W8L1GhTgPBjSRW5xnm4sm5cZUqIlXFI27_QPOrf7GgI,9288
cvxpy-1.6.5.dist-info/RECORD,,
cvxpy-1.6.5.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
cvxpy-1.6.5.dist-info/WHEEL,sha256=A6iggJuFsuu67bHdjxJADhwSEJmqwgO3xFoNCIwjOxc,115
cvxpy-1.6.5.dist-info/licenses/LICENSE,sha256=lNgcsoLag39Kzg3wi1L9HHLgfanvtS5aptVWpc6lKG8,11344
cvxpy-1.6.5.dist-info/top_level.txt,sha256=-LXmuPcF7l-IfbP5XfPj_xi_FjiZx5tzTY1HGh1GOdk,43
cvxpy/__init__.py,sha256=D3fHRPrmzep9OpoYfRiChfrExkaiqVCqUAFK_aVOm_M,3057
cvxpy/__pycache__/__init__.cpython-313.pyc,,
cvxpy/__pycache__/error.cpython-313.pyc,,
cvxpy/__pycache__/settings.cpython-313.pyc,,
cvxpy/__pycache__/version.cpython-313.pyc,,
cvxpy/atoms/__init__.py,sha256=WLoOORPymo05uGo5e3ezrmvn5Y-CT2QYnC9b6bp4h8k,5717
cvxpy/atoms/__pycache__/__init__.cpython-313.pyc,,
cvxpy/atoms/__pycache__/atom.cpython-313.pyc,,
cvxpy/atoms/__pycache__/axis_atom.cpython-313.pyc,,
cvxpy/atoms/__pycache__/condition_number.cpython-313.pyc,,
cvxpy/atoms/__pycache__/cummax.cpython-313.pyc,,
cvxpy/atoms/__pycache__/cumprod.cpython-313.pyc,,
cvxpy/atoms/__pycache__/cvar.cpython-313.pyc,,
cvxpy/atoms/__pycache__/dist_ratio.cpython-313.pyc,,
cvxpy/atoms/__pycache__/dotsort.cpython-313.pyc,,
cvxpy/atoms/__pycache__/errormsg.cpython-313.pyc,,
cvxpy/atoms/__pycache__/eye_minus_inv.cpython-313.pyc,,
cvxpy/atoms/__pycache__/gen_lambda_max.cpython-313.pyc,,
cvxpy/atoms/__pycache__/geo_mean.cpython-313.pyc,,
cvxpy/atoms/__pycache__/gmatmul.cpython-313.pyc,,
cvxpy/atoms/__pycache__/harmonic_mean.cpython-313.pyc,,
cvxpy/atoms/__pycache__/inv_prod.cpython-313.pyc,,
cvxpy/atoms/__pycache__/lambda_max.cpython-313.pyc,,
cvxpy/atoms/__pycache__/lambda_min.cpython-313.pyc,,
cvxpy/atoms/__pycache__/lambda_sum_largest.cpython-313.pyc,,
cvxpy/atoms/__pycache__/lambda_sum_smallest.cpython-313.pyc,,
cvxpy/atoms/__pycache__/length.cpython-313.pyc,,
cvxpy/atoms/__pycache__/log_det.cpython-313.pyc,,
cvxpy/atoms/__pycache__/log_sum_exp.cpython-313.pyc,,
cvxpy/atoms/__pycache__/matrix_frac.cpython-313.pyc,,
cvxpy/atoms/__pycache__/max.cpython-313.pyc,,
cvxpy/atoms/__pycache__/min.cpython-313.pyc,,
cvxpy/atoms/__pycache__/mixed_norm.cpython-313.pyc,,
cvxpy/atoms/__pycache__/norm.cpython-313.pyc,,
cvxpy/atoms/__pycache__/norm1.cpython-313.pyc,,
cvxpy/atoms/__pycache__/norm_inf.cpython-313.pyc,,
cvxpy/atoms/__pycache__/norm_nuc.cpython-313.pyc,,
cvxpy/atoms/__pycache__/one_minus_pos.cpython-313.pyc,,
cvxpy/atoms/__pycache__/perspective.cpython-313.pyc,,
cvxpy/atoms/__pycache__/pf_eigenvalue.cpython-313.pyc,,
cvxpy/atoms/__pycache__/pnorm.cpython-313.pyc,,
cvxpy/atoms/__pycache__/prod.cpython-313.pyc,,
cvxpy/atoms/__pycache__/ptp.cpython-313.pyc,,
cvxpy/atoms/__pycache__/quad_form.cpython-313.pyc,,
cvxpy/atoms/__pycache__/quad_over_lin.cpython-313.pyc,,
cvxpy/atoms/__pycache__/quantum_cond_entr.cpython-313.pyc,,
cvxpy/atoms/__pycache__/quantum_rel_entr.cpython-313.pyc,,
cvxpy/atoms/__pycache__/sigma_max.cpython-313.pyc,,
cvxpy/atoms/__pycache__/sign.cpython-313.pyc,,
cvxpy/atoms/__pycache__/stats.cpython-313.pyc,,
cvxpy/atoms/__pycache__/sum_largest.cpython-313.pyc,,
cvxpy/atoms/__pycache__/sum_smallest.cpython-313.pyc,,
cvxpy/atoms/__pycache__/sum_squares.cpython-313.pyc,,
cvxpy/atoms/__pycache__/suppfunc.cpython-313.pyc,,
cvxpy/atoms/__pycache__/total_variation.cpython-313.pyc,,
cvxpy/atoms/__pycache__/tr_inv.cpython-313.pyc,,
cvxpy/atoms/__pycache__/von_neumann_entr.cpython-313.pyc,,
cvxpy/atoms/affine/__init__.py,sha256=JDqz7falKze_sCFsDtUj_bq9Ra-w6qU5PEJM1LwZA7o,563
cvxpy/atoms/affine/__pycache__/__init__.cpython-313.pyc,,
cvxpy/atoms/affine/__pycache__/add_expr.cpython-313.pyc,,
cvxpy/atoms/affine/__pycache__/affine_atom.cpython-313.pyc,,
cvxpy/atoms/affine/__pycache__/binary_operators.cpython-313.pyc,,
cvxpy/atoms/affine/__pycache__/bmat.cpython-313.pyc,,
cvxpy/atoms/affine/__pycache__/broadcast_to.cpython-313.pyc,,
cvxpy/atoms/affine/__pycache__/concatenate.cpython-313.pyc,,
cvxpy/atoms/affine/__pycache__/conj.cpython-313.pyc,,
cvxpy/atoms/affine/__pycache__/conv.cpython-313.pyc,,
cvxpy/atoms/affine/__pycache__/cumsum.cpython-313.pyc,,
cvxpy/atoms/affine/__pycache__/diag.cpython-313.pyc,,
cvxpy/atoms/affine/__pycache__/diff.cpython-313.pyc,,
cvxpy/atoms/affine/__pycache__/hstack.cpython-313.pyc,,
cvxpy/atoms/affine/__pycache__/imag.cpython-313.pyc,,
cvxpy/atoms/affine/__pycache__/index.cpython-313.pyc,,
cvxpy/atoms/affine/__pycache__/kron.cpython-313.pyc,,
cvxpy/atoms/affine/__pycache__/partial_trace.cpython-313.pyc,,
cvxpy/atoms/affine/__pycache__/partial_transpose.cpython-313.pyc,,
cvxpy/atoms/affine/__pycache__/promote.cpython-313.pyc,,
cvxpy/atoms/affine/__pycache__/real.cpython-313.pyc,,
cvxpy/atoms/affine/__pycache__/reshape.cpython-313.pyc,,
cvxpy/atoms/affine/__pycache__/sum.cpython-313.pyc,,
cvxpy/atoms/affine/__pycache__/trace.cpython-313.pyc,,
cvxpy/atoms/affine/__pycache__/transpose.cpython-313.pyc,,
cvxpy/atoms/affine/__pycache__/unary_operators.cpython-313.pyc,,
cvxpy/atoms/affine/__pycache__/upper_tri.cpython-313.pyc,,
cvxpy/atoms/affine/__pycache__/vec.cpython-313.pyc,,
cvxpy/atoms/affine/__pycache__/vstack.cpython-313.pyc,,
cvxpy/atoms/affine/__pycache__/wraps.cpython-313.pyc,,
cvxpy/atoms/affine/add_expr.py,sha256=t6bWiafUlx6ag2QPy3gqIkdVbBEleaN7FNOH4UCrt-4,4262
cvxpy/atoms/affine/affine_atom.py,sha256=LOGyK4TY3AC4BLfWl2r4-7Z3Zmgpk6nle83dNTLob7Q,5676
cvxpy/atoms/affine/binary_operators.py,sha256=otwSk3FdNctiOAFLEzrzyfGiSgW77_ONHNg1dZS1idk,15761
cvxpy/atoms/affine/bmat.py,sha256=jizoC5h7oYsQPtYtYVUFceoPKADmaqev0jvkb37Nhls,1137
cvxpy/atoms/affine/broadcast_to.py,sha256=Om2GwYwz9glc5yLlMATN_JSYI-e7pjBFRgA_1elPAYw,2340
cvxpy/atoms/affine/concatenate.py,sha256=JylMNKwO3niUaYUAgNMMtnfmQozN2EnK4EV47K2zFnY,2988
cvxpy/atoms/affine/conj.py,sha256=gioSoP4fo2OppbfArHA6yexVLtLaDnPKGJmFsmwMUc4,2313
cvxpy/atoms/affine/conv.py,sha256=phpQ-YNNK6Q9NmwG-wL9mXgWebYjYJk_rTdFGDi4_dg,7238
cvxpy/atoms/affine/cumsum.py,sha256=5LbK-xhiEkHsUerGPaLW9i242jozSNjbY0VEI4tLuKM,4030
cvxpy/atoms/affine/diag.py,sha256=rvNZaIGCuixoTZdHgGvQWIsOHnWW9SuVAlvvDE6E6U4,5368
cvxpy/atoms/affine/diff.py,sha256=uouxKOz_rDm_9gMibCHAcwKwKZqh7O3qU0QSj-yafzY,1644
cvxpy/atoms/affine/hstack.py,sha256=unO6VBmMUAJ0VGuvXN_TgORYw_TIACEO5kVEkC31kkQ,2986
cvxpy/atoms/affine/imag.py,sha256=9lpJNnPU6I4GOALle9HutBEydQTCnfXjCoTt4Ujsuy0,1459
cvxpy/atoms/affine/index.py,sha256=K_BPEtJa-Lohrj3nAptrYjluPHPCViFcoXGlwpccGt4,6554
cvxpy/atoms/affine/kron.py,sha256=-CJ5u3s8vnzfzoeSRjs-vyMpznGMghGUijQ2c4akuTA,4379
cvxpy/atoms/affine/partial_trace.py,sha256=gP9x6jG-RwQlT-VwdbNJp4O-bKCGgFG07eSloAJE9ZY,3310
cvxpy/atoms/affine/partial_transpose.py,sha256=QhRf8my6YY2DMQZo09hvj-7OPyNcknpLBhxZno5CVzE,3370
cvxpy/atoms/affine/promote.py,sha256=uxpbrbx4Xst5WIS8GQCgzMM_kzbBl13n8vMPr07UZMQ,3203
cvxpy/atoms/affine/real.py,sha256=vAlzYajZCbqbhDw0qmH2fVK6Wk18RvFtJch86S9h_zE,1486
cvxpy/atoms/affine/reshape.py,sha256=7yawcCpZu63ytzYDmfUPw2YYWAWsg2e3ZcR1AyhXaak,5741
cvxpy/atoms/affine/sum.py,sha256=Se_IXTnqObSO65yyQa7GxN4UaZ4n46JRxoHc0cFgtEc,4534
cvxpy/atoms/affine/trace.py,sha256=y4f4LCnJeyE5d7zaikECICZRtLQINrWAtJJ--HD101g,2937
cvxpy/atoms/affine/transpose.py,sha256=H_01K2izNH57fGQIoDpOraPsU7VskDA_w2_1wziZlXY,2771
cvxpy/atoms/affine/unary_operators.py,sha256=vUKWzqdlBvBo5nsZ0RpzaYyob7wBk5Im2RKWE5I2OI0,2752
cvxpy/atoms/affine/upper_tri.py,sha256=6-IETfhGYJAMmDU-UISnwsFmvSasSh8bCQWMKn9QeuI,5290
cvxpy/atoms/affine/vec.py,sha256=b5xRUFNFqHiP33bNS0r3961C6xVyuoNKfg0nOxU0rS0,1388
cvxpy/atoms/affine/vstack.py,sha256=V9s_ysxxDOZ8ZQsVgD5E2mY10RPgOgwR1vNSTEq5OTM,3033
cvxpy/atoms/affine/wraps.py,sha256=AjnbDgqzyaQvRDITvXsb_lLf4lNnx812sFt20a-FKb8,4182
cvxpy/atoms/atom.py,sha256=F8918Mr9R6Tdfbmtq6kwqUuBVdU0X1FQEbn9bKTDMZk,16173
cvxpy/atoms/axis_atom.py,sha256=We4Y8lHkJvHm43lGVoxX8dAp4w8ymoI8vJcZv0Z5j0o,4800
cvxpy/atoms/condition_number.py,sha256=O9Fr_nMOYsC6rsGSBti_pQtQLv5t7yq04CNA0aYLjUw,3148
cvxpy/atoms/cummax.py,sha256=d9X5Jn7Xy-wDEP8ZHviav8YLM68ra8-S_62isDBiwlo,2943
cvxpy/atoms/cumprod.py,sha256=lPKln3zMqqajO6U7_EwthSKynCKZn69SnjDQxF4xLEY,2399
cvxpy/atoms/cvar.py,sha256=6uvKgrN94mZiYAX_WHleXKQb2waMNTIOOpM_4OZUbKM,1997
cvxpy/atoms/dist_ratio.py,sha256=MWXSCgWYX-Fl3sMeS92kmt7dAEVbmTz61w7ilpkV3Js,2423
cvxpy/atoms/dotsort.py,sha256=HsmWL8weDbff1L-iz7OSVNp2Bkdox_yIFX49ZayRT-w,4928
cvxpy/atoms/elementwise/__init__.py,sha256=JDqz7falKze_sCFsDtUj_bq9Ra-w6qU5PEJM1LwZA7o,563
cvxpy/atoms/elementwise/__pycache__/__init__.cpython-313.pyc,,
cvxpy/atoms/elementwise/__pycache__/abs.cpython-313.pyc,,
cvxpy/atoms/elementwise/__pycache__/ceil.cpython-313.pyc,,
cvxpy/atoms/elementwise/__pycache__/elementwise.cpython-313.pyc,,
cvxpy/atoms/elementwise/__pycache__/entr.cpython-313.pyc,,
cvxpy/atoms/elementwise/__pycache__/exp.cpython-313.pyc,,
cvxpy/atoms/elementwise/__pycache__/huber.cpython-313.pyc,,
cvxpy/atoms/elementwise/__pycache__/inv_pos.cpython-313.pyc,,
cvxpy/atoms/elementwise/__pycache__/kl_div.cpython-313.pyc,,
cvxpy/atoms/elementwise/__pycache__/log.cpython-313.pyc,,
cvxpy/atoms/elementwise/__pycache__/log1p.cpython-313.pyc,,
cvxpy/atoms/elementwise/__pycache__/log_normcdf.cpython-313.pyc,,
cvxpy/atoms/elementwise/__pycache__/loggamma.cpython-313.pyc,,
cvxpy/atoms/elementwise/__pycache__/logistic.cpython-313.pyc,,
cvxpy/atoms/elementwise/__pycache__/maximum.cpython-313.pyc,,
cvxpy/atoms/elementwise/__pycache__/minimum.cpython-313.pyc,,
cvxpy/atoms/elementwise/__pycache__/neg.cpython-313.pyc,,
cvxpy/atoms/elementwise/__pycache__/pos.cpython-313.pyc,,
cvxpy/atoms/elementwise/__pycache__/power.cpython-313.pyc,,
cvxpy/atoms/elementwise/__pycache__/rel_entr.cpython-313.pyc,,
cvxpy/atoms/elementwise/__pycache__/scalene.cpython-313.pyc,,
cvxpy/atoms/elementwise/__pycache__/sqrt.cpython-313.pyc,,
cvxpy/atoms/elementwise/__pycache__/square.cpython-313.pyc,,
cvxpy/atoms/elementwise/__pycache__/xexp.cpython-313.pyc,,
cvxpy/atoms/elementwise/abs.py,sha256=SNBnY3vaQFYN3izNx11mDRfv1ilQbs9P8lbneKaOU_o,2457
cvxpy/atoms/elementwise/ceil.py,sha256=Ck7UULqv_8c7ygR8DO0rz2cBkrv-zxYyFcaqE_uUK8Y,4790
cvxpy/atoms/elementwise/elementwise.py,sha256=7fVtATZf7lOjSTXrcvVs-6t_epTxIiywjIuw5i0dTkE,2371
cvxpy/atoms/elementwise/entr.py,sha256=7l_XymU4oPkld_yq2b_qqlzxKjdNY2acaWQxLP6s5iU,2746
cvxpy/atoms/elementwise/exp.py,sha256=xOJ7MNpmNSAU0K6Clh_WwiwnEywSPT6mc-VEPaDJiZU,2358
cvxpy/atoms/elementwise/huber.py,sha256=8s6wKilO1gu9D11DKWfpJw3w8SzuGkR0Ow9pxePOpKk,3600
cvxpy/atoms/elementwise/inv_pos.py,sha256=vL1Dz3I6wg8ixe05ZmBFlzCdmhXXmyk-8ro8gHgJYQM,703
cvxpy/atoms/elementwise/kl_div.py,sha256=_6Kd2GR1wS1dRBNTdDGjyySs5MT0f9WQh60Aa1f9nRw,2976
cvxpy/atoms/elementwise/log.py,sha256=3keW0L7KODqPTtiqAq3vS-8Y8POJsyAoy0gJHmgoEG0,2757
cvxpy/atoms/elementwise/log1p.py,sha256=K-LRy68mmyvb3PzPoJZt9qMXHtDh_Aie2fRU7BbpEYQ,2046
cvxpy/atoms/elementwise/log_normcdf.py,sha256=cqOgsjNiujDYNpmpWBhklUbZvhzMcKULRV6XX6RTtgg,2058
cvxpy/atoms/elementwise/loggamma.py,sha256=oJgws7AvlC4XjRWG6NlJCCDTniG7b5XrbJ0IbExT2KI,1417
cvxpy/atoms/elementwise/logistic.py,sha256=Z3U6okvGA1bvgWkdMQ1fZZmqpnPDBjJPfmuk5_St5O8,2345
cvxpy/atoms/elementwise/maximum.py,sha256=42yHO01-4zr2Qyfzxh8QAKqaZytBrk9bN0Pyp1uPHHQ,3472
cvxpy/atoms/elementwise/minimum.py,sha256=ZynrXmkVen10usjEJ3oERFmdr8RFm4sd9CfNRpxwvac,3175
cvxpy/atoms/elementwise/neg.py,sha256=5CkqQsE01tLjDvg18r0UapJb6qUCdTExf1wMNNxWSSc,699
cvxpy/atoms/elementwise/pos.py,sha256=4YGpXzFWVqPTOjm_eBVN6y7Zq2x-3fz6yBMUNND2ZsU,696
cvxpy/atoms/elementwise/power.py,sha256=zde2gzzx6zAk4C13VZ7Se6JijqCgqs0kPqZKO7nb6gs,14794
cvxpy/atoms/elementwise/rel_entr.py,sha256=zwtJ2U7RWzMPFVtciJzC-5ZHarDbglrDBklW-jsONr0,2955
cvxpy/atoms/elementwise/scalene.py,sha256=qmhYZBK7DTuQUZbbrTuHGxRhzrz1U7G0IvS8Xo1pQBg,779
cvxpy/atoms/elementwise/sqrt.py,sha256=cDeXRydbE5ZfOdcsp5lomQIyNwUH6iZWZNXpwOR_fxk,739
cvxpy/atoms/elementwise/square.py,sha256=0XGn2oe75uCaQPWThEGUgnlrPb73lsNvefxvYBChPro,691
cvxpy/atoms/elementwise/xexp.py,sha256=4G8oYkaOYtvRtBbRD9uSqL3U9-G_A1pMYhGxf_GhNdM,2682
cvxpy/atoms/errormsg.py,sha256=Fq0eL1NdoXV4rLT_imjeG9m9bhhlycwECc_mGKoNvTU,243
cvxpy/atoms/eye_minus_inv.py,sha256=0Smd1o91aPWbBdVDEMlIMG1T9I4luA9IkU6uyLNQo6w,3300
cvxpy/atoms/gen_lambda_max.py,sha256=OpND711QZFnJLpRrAfxkN4UNuDWP-vxi1-AaUNIQEy4,3526
cvxpy/atoms/geo_mean.py,sha256=9c3MPAM3dz3ozKQPqur0Rc2qhD0a2JtN__ut6TsXxyQ,12387
cvxpy/atoms/gmatmul.py,sha256=l1NIZjVww_emzZqYcoKA1zJ1ZDVNW8svWkj5VMcnAdc,4909
cvxpy/atoms/harmonic_mean.py,sha256=JtViW3jR3D2lnglvdU6IDMv5ClapOa6dtVM2Sm4HuOI,1292
cvxpy/atoms/inv_prod.py,sha256=Y-ZJlmx8CnOVC8zPaKj0hMYE8AUKtwHdsUe-yM9TjUg,1221
cvxpy/atoms/lambda_max.py,sha256=j382A1Dz2jYfmv3XCprNdNAGZqoD0dKdBCWibc3jGHY,3299
cvxpy/atoms/lambda_min.py,sha256=cC3W-GYeg5GhaM3UvGaWm7Bp4bZmi3n2cFxZ1T-msYU,811
cvxpy/atoms/lambda_sum_largest.py,sha256=o-0e66cdCgKU7384PGMzd-r-vfHFx5EXQGLOzAD30rY,2318
cvxpy/atoms/lambda_sum_smallest.py,sha256=npR5zp-nLGMXXivjtm0n2aFW5euZAHt1qRnq8sOb-Ms,834
cvxpy/atoms/length.py,sha256=XFCcKKxCEPU3CCmZPV-NLoNUGn_pNpS_c1H2oQWoMMA,2324
cvxpy/atoms/log_det.py,sha256=c9UqAy53MiYWbGnBarBuBHkDmWXDWnzk8pElay0URBw,3645
cvxpy/atoms/log_sum_exp.py,sha256=gokib9QRSaWaAoDYIx1v1__oqinQ33qoWRsJnLklh3o,2690
cvxpy/atoms/matrix_frac.py,sha256=fgm-oH22gUOzMZCLLbFIZQf2-e_r09CJHwa65zHOggw,4538
cvxpy/atoms/max.py,sha256=hry4-BC_ubq-z_W2MydlNPyz2wYe5irEs_wY5GKJO_0,3632
cvxpy/atoms/min.py,sha256=ZESQb9PTolqvQCcHhmdsZA7RGiJpEKc6rKWI5L1EBMY,3632
cvxpy/atoms/mixed_norm.py,sha256=6XKQkn8VoPZzvQLQdmIhCafPF6pYwPnpe9sik-Spsjw,1304
cvxpy/atoms/norm.py,sha256=lwhou7l7mZL3KGy4cexDcYeEe4zqrYU3IVCerkbgQdM,3385
cvxpy/atoms/norm1.py,sha256=7-dR5HyXtudxjiYnL0PxbaTWROApl4xR572XFuq6Prw,3212
cvxpy/atoms/norm_inf.py,sha256=55I7ymfcIRq0d5BEiutHaQqXlTxgqcQSW3qZFbKbn-k,3374
cvxpy/atoms/norm_nuc.py,sha256=HZMAuZSc1UxdJf_j27pdltpZs88Atb6TNqAXirq3kIo,2292
cvxpy/atoms/one_minus_pos.py,sha256=YCyNrdjdDuaF2CljowSMjA90qMyfbfNgtu0IDqnO4Fg,2801
cvxpy/atoms/perspective.py,sha256=PReoakYe7bPZAYbrANAAxmjhWcGEdCVcBcudGukbYAQ,5125
cvxpy/atoms/pf_eigenvalue.py,sha256=koQbpyaxnWs7thrcHfVhuShuE8XnjvuPmi3NrFA13Vo,2694
cvxpy/atoms/pnorm.py,sha256=hjEggDjR5UgT0y0aTeVpSIBrW3VWrnrLxKlZAv315Fs,8451
cvxpy/atoms/prod.py,sha256=ceFUX9M9YLLlJroY8Wmf252p2N02uNgM9pwihHgafY4,4913
cvxpy/atoms/ptp.py,sha256=azmc0qZsnTsD10ARHYpuUQrZmwDhavCCdVWesqHmcSM,975
cvxpy/atoms/quad_form.py,sha256=hDkgM2gcZDTgdhQAwqSgFt0Lkxu9E-Erc6DUTrGrQiY,8129
cvxpy/atoms/quad_over_lin.py,sha256=6cl2ka3z68goYYSJ-v3dTkZ6ANYSWD12C5oZh_LR6Bs,4611
cvxpy/atoms/quantum_cond_entr.py,sha256=bZfPQOUXQTFVhypsBG_7KnlVX_VvkN5NCI3oFzVWglk,774
cvxpy/atoms/quantum_rel_entr.py,sha256=u9qM_IfEF0Tg-farcli0POtLeYlE91ESEhSUhdRYkzk,3385
cvxpy/atoms/sigma_max.py,sha256=1o2Jg9GofuQTHDirUl3pJaPJS8KNqodXKld9lP0Z7XU,2381
cvxpy/atoms/sign.py,sha256=uf9lZGWSED6OhGlmyOJhQlC5qfPpSO4AeMf7u84q80I,2121
cvxpy/atoms/stats.py,sha256=nEgAYrdasRFFX_8Kiq5PsSVdIfx57GoJXtT-2Uvya3g,2188
cvxpy/atoms/sum_largest.py,sha256=lJufphLJT4Benp6t1aZxkJbAzjn2dxT03r0hR-dlql4,3191
cvxpy/atoms/sum_smallest.py,sha256=ZdDLLfHMy7Z8wOEj8DaAiqpvx8dLZTx52rUG5wzZsDI,802
cvxpy/atoms/sum_squares.py,sha256=qvotGaxKQANBinsO_MwP36ydXTl9yLjl_E4ks_lED94,930
cvxpy/atoms/suppfunc.py,sha256=BLCcZOFETATbI3H8291LfUXdLIzWXGSAAwxdodrsNSs,4057
cvxpy/atoms/total_variation.py,sha256=jg8BKgLiYjNBHdzxuYCg8yGV9k3qQusSGx78_AzeVuM,2112
cvxpy/atoms/tr_inv.py,sha256=gHqzoadVVmFDRUY0mzl0HqLouyuwhfAIEUqk8i7rluE,3832
cvxpy/atoms/von_neumann_entr.py,sha256=LD2xzxrkt3OCezn27HkH_zfR8djIVUV78giQFm9Aho4,3821
cvxpy/constraints/__init__.py,sha256=1nWFo4v_WKbju8TfYSALk2SzKcNxT8F355hRlCK43xs,1122
cvxpy/constraints/__pycache__/__init__.cpython-313.pyc,,
cvxpy/constraints/__pycache__/cones.cpython-313.pyc,,
cvxpy/constraints/__pycache__/constraint.cpython-313.pyc,,
cvxpy/constraints/__pycache__/exponential.cpython-313.pyc,,
cvxpy/constraints/__pycache__/finite_set.cpython-313.pyc,,
cvxpy/constraints/__pycache__/nonpos.cpython-313.pyc,,
cvxpy/constraints/__pycache__/power.cpython-313.pyc,,
cvxpy/constraints/__pycache__/psd.cpython-313.pyc,,
cvxpy/constraints/__pycache__/second_order.cpython-313.pyc,,
cvxpy/constraints/__pycache__/utilities.cpython-313.pyc,,
cvxpy/constraints/__pycache__/zero.cpython-313.pyc,,
cvxpy/constraints/cones.py,sha256=3gWEJ5oS9vwGq-DPTsrmmaltGIJHm34PYlz1PlRKoX8,2258
cvxpy/constraints/constraint.py,sha256=_8DtHkqjqXOyDMiIQ4D0CZv9MHj9upRsXdyPvK7p0H0,7147
cvxpy/constraints/exponential.py,sha256=QrtDcJGU3BzzYZ_ZyKIhhyVb1Qc00ej9iXEucPd-qtQ,13810
cvxpy/constraints/finite_set.py,sha256=CqUzkLzNHYzKGxo1FHTqlcZ2R7q5heZyKrW1Or7Awt4,4594
cvxpy/constraints/nonpos.py,sha256=H1itYUwtLJYwxw1tYxEQszcsJIq6DiT46p_9kqH1N2A,7905
cvxpy/constraints/power.py,sha256=ZSNh0Y7SIsSXBkguhTY50J-lJMi3hvjJzklT9ArJ4Hc,11693
cvxpy/constraints/psd.py,sha256=IfQo7Nqz78MxTwGGI-Jt0GeuFBFpZYgpdZrVm2Qh-r8,3242
cvxpy/constraints/second_order.py,sha256=EaQ61c6UeDuGws8DGTEkmuZR1mTgmB_5C_eK6hQ2Z8M,6030
cvxpy/constraints/utilities.py,sha256=GAXx6pb5ktDgvP0US1kvw7E1-Y_gNBcr1oKndgm8Qj0,3831
cvxpy/constraints/zero.py,sha256=T8nlGcGoFd9c-5jOGuQ-_GpF3fARssZVP0wcOzXwbzA,5264
cvxpy/cvxcore/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
cvxpy/cvxcore/__pycache__/__init__.cpython-313.pyc,,
cvxpy/cvxcore/python/__init__.py,sha256=BIJkU4_tXZmUi5LMG51c9-lU5SHqh8Ic1U6H5OZjXGM,348
cvxpy/cvxcore/python/__pycache__/__init__.cpython-313.pyc,,
cvxpy/cvxcore/python/__pycache__/canonInterface.cpython-313.pyc,,
cvxpy/cvxcore/python/__pycache__/cppbackend.cpython-313.pyc,,
cvxpy/cvxcore/python/__pycache__/cvxcore.cpython-313.pyc,,
cvxpy/cvxcore/python/canonInterface.py,sha256=hEggfBIpVDzfZMwRCK10I5Dddk_jd76D-PdRdpzajDk,12170
cvxpy/cvxcore/python/cppbackend.py,sha256=hPQ6fk8ZFMXkdx65t2I0bZNNwkZozLqCyK8KnSR5kOY,8255
cvxpy/cvxcore/python/cvxcore.py,sha256=e1zuWmeNg3mgie7oqrxym8isUHLXaMVD1o26CGzCINo,28678
cvxpy/error.py,sha256=0tgOEYkFgss1v7abAlk-lRlRyxxieFph8Ktli8Rq3Os,1299
cvxpy/expressions/__init__.py,sha256=30XOGvlVQtLEBLIlK3AjOqXzUvBCzI3mcDkJkT0hrz0,590
cvxpy/expressions/__pycache__/__init__.cpython-313.pyc,,
cvxpy/expressions/__pycache__/cvxtypes.cpython-313.pyc,,
cvxpy/expressions/__pycache__/expression.cpython-313.pyc,,
cvxpy/expressions/__pycache__/leaf.cpython-313.pyc,,
cvxpy/expressions/__pycache__/variable.cpython-313.pyc,,
cvxpy/expressions/constants/__init__.py,sha256=SsuTSAw1m4C3N7pCNtZ_SF-PbENkbM7YJteUc4ofdvs,670
cvxpy/expressions/constants/__pycache__/__init__.cpython-313.pyc,,
cvxpy/expressions/constants/__pycache__/callback_param.cpython-313.pyc,,
cvxpy/expressions/constants/__pycache__/constant.cpython-313.pyc,,
cvxpy/expressions/constants/__pycache__/parameter.cpython-313.pyc,,
cvxpy/expressions/constants/callback_param.py,sha256=K8LVv4qYvOdkAfwPdXrZ8EhLcoYc1KEXYIBXeax1YzA,1729
cvxpy/expressions/constants/constant.py,sha256=_s3VNANtbaojSOHLfl3JlKGSAYNarQH1WdEtbgyDOAQ,8535
cvxpy/expressions/constants/parameter.py,sha256=Z0zm92ZxJ36weWdy3Gw_0Dqs02MCVJI1b5XpA-oEV-U,3454
cvxpy/expressions/cvxtypes.py,sha256=nG4uvbaqV1WT32Gkm7Wnknn1rNnhNdNuqbsdiVt4aRY,3421
cvxpy/expressions/expression.py,sha256=eaWNNOeE92_SouAc0bNT6czQog6DpPsRN4K13Tk-ook,30610
cvxpy/expressions/leaf.py,sha256=SfjHmt89EuJdCDRuv0sUZO3CAKKmzhf13ITKPfdMpQc,26415
cvxpy/expressions/variable.py,sha256=kSHe0u5TxILyP3PLrkOTc-twBM2OfaFETQ0cxBlJAD4,3195
cvxpy/interface/__init__.py,sha256=IOweLW6dP-2Z0JH9BrC4Fzax4Yawl13ePcF2Fylz74o,611
cvxpy/interface/__pycache__/__init__.cpython-313.pyc,,
cvxpy/interface/__pycache__/base_matrix_interface.cpython-313.pyc,,
cvxpy/interface/__pycache__/matrix_utilities.cpython-313.pyc,,
cvxpy/interface/base_matrix_interface.py,sha256=Qjlrq3hiZQqGfjuFvTaT4vr9npNKKDVz-vNbz0EBvyY,5162
cvxpy/interface/matrix_utilities.py,sha256=ihMQXgPnTx02UBakn41PQpSU5I3mdkmpfqDDy2K9fBg,10925
cvxpy/interface/numpy_interface/__init__.py,sha256=mzrhJiHrWcnuw1m5zySZ0ZzwqjbnDn2m98B04ZpBh6E,818
cvxpy/interface/numpy_interface/__pycache__/__init__.cpython-313.pyc,,
cvxpy/interface/numpy_interface/__pycache__/matrix_interface.cpython-313.pyc,,
cvxpy/interface/numpy_interface/__pycache__/ndarray_interface.cpython-313.pyc,,
cvxpy/interface/numpy_interface/__pycache__/sparse_matrix_interface.cpython-313.pyc,,
cvxpy/interface/numpy_interface/matrix_interface.py,sha256=1g25dnDzyiXIl4jgOZO1xm08lCglJ_Syu8BV1zLhhzQ,2058
cvxpy/interface/numpy_interface/ndarray_interface.py,sha256=Rnev9obN94vhnKSf5h3mH8QPMNue_EHNLdl5ajsiZsw,2548
cvxpy/interface/numpy_interface/sparse_matrix_interface.py,sha256=PgvRVxdzjZLXaRA3UJiqXAcYFFS8uThgjETKrYjU5y4,3446
cvxpy/lin_ops/__init__.py,sha256=tEiVjvYPkDJxyC1Iwdx6CU48LxUJraor88Vr5g1-yX8,684
cvxpy/lin_ops/__pycache__/__init__.cpython-313.pyc,,
cvxpy/lin_ops/__pycache__/canon_backend.cpython-313.pyc,,
cvxpy/lin_ops/__pycache__/lin_constraints.cpython-313.pyc,,
cvxpy/lin_ops/__pycache__/lin_op.cpython-313.pyc,,
cvxpy/lin_ops/__pycache__/lin_utils.cpython-313.pyc,,
cvxpy/lin_ops/__pycache__/tree_mat.cpython-313.pyc,,
cvxpy/lin_ops/canon_backend.py,sha256=lKZP83uxbXkPdObzsRpfrjKhiJDGr61SiWIIIo72aIQ,80921
cvxpy/lin_ops/lin_constraints.py,sha256=UV7x30NA5VYQqMFzJyOOB6I0N309g3mXMk_qQPJex9o,1126
cvxpy/lin_ops/lin_op.py,sha256=kIKXiv7drGNwuweXWLhZgQ1EmhlC7Qq2_jaaPeZXoVs,3567
cvxpy/lin_ops/lin_utils.py,sha256=wjgh8j67NE6gWPCgFE_8oLU_E7KTmgcRJ0mOkLOfx0U,19350
cvxpy/lin_ops/tree_mat.py,sha256=TDpogLfg-KU9AK4__BdTzlh-wcqE6OtCACItRbZrpmM,12127
cvxpy/problems/__init__.py,sha256=JDqz7falKze_sCFsDtUj_bq9Ra-w6qU5PEJM1LwZA7o,563
cvxpy/problems/__pycache__/__init__.cpython-313.pyc,,
cvxpy/problems/__pycache__/iterative.cpython-313.pyc,,
cvxpy/problems/__pycache__/objective.cpython-313.pyc,,
cvxpy/problems/__pycache__/param_prob.cpython-313.pyc,,
cvxpy/problems/__pycache__/problem.cpython-313.pyc,,
cvxpy/problems/iterative.py,sha256=ZtVl6FEAWashaDcVE6Fwyo8yengZWHXwqbuJz0wAomM,4958
cvxpy/problems/objective.py,sha256=Fuct6BWEK1xFUN5rWXyRqc4SV7cr-b_Nwxuxdl38UaA,7412
cvxpy/problems/param_prob.py,sha256=Zr8pngDAjvYqKWkMxo4vb6aoBvh-GSo_L1vhuoItNh0,1602
cvxpy/problems/problem.py,sha256=4FzsTzQ-6zgoZDKN7U7i5lK1bcIH_RVDWBsvuF32IXE,67790
cvxpy/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
cvxpy/reductions/__init__.py,sha256=p_KCbwa0oqE1agVZFY9XPF_oasagd6GuVYTXD1w7lX4,1452
cvxpy/reductions/__pycache__/__init__.cpython-313.pyc,,
cvxpy/reductions/__pycache__/canonicalization.cpython-313.pyc,,
cvxpy/reductions/__pycache__/chain.cpython-313.pyc,,
cvxpy/reductions/__pycache__/cvx_attr2constr.cpython-313.pyc,,
cvxpy/reductions/__pycache__/eval_params.cpython-313.pyc,,
cvxpy/reductions/__pycache__/flip_objective.cpython-313.pyc,,
cvxpy/reductions/__pycache__/inverse_data.cpython-313.pyc,,
cvxpy/reductions/__pycache__/matrix_stuffing.cpython-313.pyc,,
cvxpy/reductions/__pycache__/reduction.cpython-313.pyc,,
cvxpy/reductions/__pycache__/solution.cpython-313.pyc,,
cvxpy/reductions/__pycache__/utilities.cpython-313.pyc,,
cvxpy/reductions/canonicalization.py,sha256=x25nxx_pHi_ROAh25WHXo_5lJ6iWlH08J1N7qduJmlw,6180
cvxpy/reductions/chain.py,sha256=xtKNHxV64XPJRQH8FxAmEh-LtHNHv7bzVD69jXQWows,2667
cvxpy/reductions/complex2real/__init__.py,sha256=bYcHRT2P3ANqtdnM4KD51vnwM0o7Dcix5FZCxWMChiE,566
cvxpy/reductions/complex2real/__pycache__/__init__.cpython-313.pyc,,
cvxpy/reductions/complex2real/__pycache__/complex2real.cpython-313.pyc,,
cvxpy/reductions/complex2real/canonicalizers/__init__.py,sha256=JSSwZCdq7VVhv47SFPRDN4UgXUH1nOUfYgKJrELmYHU,4774
cvxpy/reductions/complex2real/canonicalizers/__pycache__/__init__.cpython-313.pyc,,
cvxpy/reductions/complex2real/canonicalizers/__pycache__/abs_canon.cpython-313.pyc,,
cvxpy/reductions/complex2real/canonicalizers/__pycache__/aff_canon.cpython-313.pyc,,
cvxpy/reductions/complex2real/canonicalizers/__pycache__/constant_canon.cpython-313.pyc,,
cvxpy/reductions/complex2real/canonicalizers/__pycache__/equality_canon.cpython-313.pyc,,
cvxpy/reductions/complex2real/canonicalizers/__pycache__/inequality_canon.cpython-313.pyc,,
cvxpy/reductions/complex2real/canonicalizers/__pycache__/matrix_canon.cpython-313.pyc,,
cvxpy/reductions/complex2real/canonicalizers/__pycache__/param_canon.cpython-313.pyc,,
cvxpy/reductions/complex2real/canonicalizers/__pycache__/pnorm_canon.cpython-313.pyc,,
cvxpy/reductions/complex2real/canonicalizers/__pycache__/psd_canon.cpython-313.pyc,,
cvxpy/reductions/complex2real/canonicalizers/__pycache__/soc_canon.cpython-313.pyc,,
cvxpy/reductions/complex2real/canonicalizers/__pycache__/variable_canon.cpython-313.pyc,,
cvxpy/reductions/complex2real/canonicalizers/abs_canon.py,sha256=62pCEKNTuWpJtTAdznLobFjHZcE1mWI_Z0zJWPxAAjY,1087
cvxpy/reductions/complex2real/canonicalizers/aff_canon.py,sha256=emeEDZo26b_P13cZD8QjmtH3wbRpbIsgzV2Mr4gsjyU,3462
cvxpy/reductions/complex2real/canonicalizers/constant_canon.py,sha256=SgkHmiOfrc4FJdufVe3Frgdv_xpHyfUCNrS-tDwigGE,912
cvxpy/reductions/complex2real/canonicalizers/equality_canon.py,sha256=oAS_vepycoQHuLBgNVgSxGZY8lQySAunreY5w2UtyrE,1780
cvxpy/reductions/complex2real/canonicalizers/inequality_canon.py,sha256=Js9os5EkO3ll586zvUvTSxGnjbUTuvDSowTnut1ABNY,1460
cvxpy/reductions/complex2real/canonicalizers/matrix_canon.py,sha256=GtOLxtuegUWWurusJgvZL1gPTiWWzGNQpch54a2r6XU,8196
cvxpy/reductions/complex2real/canonicalizers/param_canon.py,sha256=lFdg769t_wxjvrD5noGaL6if-tVOl_9p4mgRahup-vc,860
cvxpy/reductions/complex2real/canonicalizers/pnorm_canon.py,sha256=177h224mgmMg547IKNYeLJaKIQ_NaB0dhLIvx-OKInU,815
cvxpy/reductions/complex2real/canonicalizers/psd_canon.py,sha256=Ctz-a0sLvp_dj8UpCmnLlVPtiQEb6bAfV9XzbYz1ikE,1042
cvxpy/reductions/complex2real/canonicalizers/soc_canon.py,sha256=x7eId3_yXeMDiWd4q0x9fYpLb5C7Xb0pUh1GRwXAN7g,1583
cvxpy/reductions/complex2real/canonicalizers/variable_canon.py,sha256=6t6kjG1uaKeKJG3AlOTOxZmA8rGr7uS8BLFIP6mK040,1720
cvxpy/reductions/complex2real/complex2real.py,sha256=Fp2uyLWbmkk44DP6YjprOaZXqBXG2XBiG4zg66gdOUQ,8221
cvxpy/reductions/cone2cone/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
cvxpy/reductions/cone2cone/__pycache__/__init__.cpython-313.pyc,,
cvxpy/reductions/cone2cone/__pycache__/affine2direct.cpython-313.pyc,,
cvxpy/reductions/cone2cone/__pycache__/approximations.cpython-313.pyc,,
cvxpy/reductions/cone2cone/__pycache__/exotic2common.cpython-313.pyc,,
cvxpy/reductions/cone2cone/__pycache__/soc2psd.cpython-313.pyc,,
cvxpy/reductions/cone2cone/affine2direct.py,sha256=6gVGRq8gcKmLWm-uxBrnu9Tm-KZzQ4HHuABAvx9rifQ,17759
cvxpy/reductions/cone2cone/approximations.py,sha256=2cWqPo78UC1l0xiK-foE8TeD6qt_PbT_UL1vGqsvQSQ,7052
cvxpy/reductions/cone2cone/exotic2common.py,sha256=nYTX74DgNTxbxhEIbnyHkX9-bHMM8mpohb8GX-7TlQw,4764
cvxpy/reductions/cone2cone/soc2psd.py,sha256=JWcTZv7DAEtwgOGSi80J5HadJOAPcnDsOIQjW2Wvf_s,6536
cvxpy/reductions/cvx_attr2constr.py,sha256=hm3vSC1CgcUVPo0Xhj2qhUM_gtRwCiWXGYV5P6Qz_Ww,7834
cvxpy/reductions/dcp2cone/__init__.py,sha256=gJC172nRSE1HpNmH8q20u1qeuLgXQ3PJDNIE0Iph6aQ,564
cvxpy/reductions/dcp2cone/__pycache__/__init__.cpython-313.pyc,,
cvxpy/reductions/dcp2cone/__pycache__/cone_matrix_stuffing.cpython-313.pyc,,
cvxpy/reductions/dcp2cone/__pycache__/dcp2cone.cpython-313.pyc,,
cvxpy/reductions/dcp2cone/canonicalizers/__init__.py,sha256=yjkGYZcyGcLkd4Xgd1bTCxL6GjPYhv-bdIoqwcgo7ko,4835
cvxpy/reductions/dcp2cone/canonicalizers/__pycache__/__init__.cpython-313.pyc,,
cvxpy/reductions/dcp2cone/canonicalizers/__pycache__/entr_canon.cpython-313.pyc,,
cvxpy/reductions/dcp2cone/canonicalizers/__pycache__/exp_canon.cpython-313.pyc,,
cvxpy/reductions/dcp2cone/canonicalizers/__pycache__/geo_mean_canon.cpython-313.pyc,,
cvxpy/reductions/dcp2cone/canonicalizers/__pycache__/huber_canon.cpython-313.pyc,,
cvxpy/reductions/dcp2cone/canonicalizers/__pycache__/indicator_canon.cpython-313.pyc,,
cvxpy/reductions/dcp2cone/canonicalizers/__pycache__/kl_div_canon.cpython-313.pyc,,
cvxpy/reductions/dcp2cone/canonicalizers/__pycache__/lambda_max_canon.cpython-313.pyc,,
cvxpy/reductions/dcp2cone/canonicalizers/__pycache__/lambda_sum_largest_canon.cpython-313.pyc,,
cvxpy/reductions/dcp2cone/canonicalizers/__pycache__/log1p_canon.cpython-313.pyc,,
cvxpy/reductions/dcp2cone/canonicalizers/__pycache__/log_canon.cpython-313.pyc,,
cvxpy/reductions/dcp2cone/canonicalizers/__pycache__/log_det_canon.cpython-313.pyc,,
cvxpy/reductions/dcp2cone/canonicalizers/__pycache__/log_sum_exp_canon.cpython-313.pyc,,
cvxpy/reductions/dcp2cone/canonicalizers/__pycache__/logistic_canon.cpython-313.pyc,,
cvxpy/reductions/dcp2cone/canonicalizers/__pycache__/matrix_frac_canon.cpython-313.pyc,,
cvxpy/reductions/dcp2cone/canonicalizers/__pycache__/mul_canon.cpython-313.pyc,,
cvxpy/reductions/dcp2cone/canonicalizers/__pycache__/normNuc_canon.cpython-313.pyc,,
cvxpy/reductions/dcp2cone/canonicalizers/__pycache__/perspective_canon.cpython-313.pyc,,
cvxpy/reductions/dcp2cone/canonicalizers/__pycache__/pnorm_canon.cpython-313.pyc,,
cvxpy/reductions/dcp2cone/canonicalizers/__pycache__/power_canon.cpython-313.pyc,,
cvxpy/reductions/dcp2cone/canonicalizers/__pycache__/quad_form_canon.cpython-313.pyc,,
cvxpy/reductions/dcp2cone/canonicalizers/__pycache__/quad_over_lin_canon.cpython-313.pyc,,
cvxpy/reductions/dcp2cone/canonicalizers/__pycache__/quantum_rel_entr_canon.cpython-313.pyc,,
cvxpy/reductions/dcp2cone/canonicalizers/__pycache__/rel_entr_canon.cpython-313.pyc,,
cvxpy/reductions/dcp2cone/canonicalizers/__pycache__/sigma_max_canon.cpython-313.pyc,,
cvxpy/reductions/dcp2cone/canonicalizers/__pycache__/suppfunc_canon.cpython-313.pyc,,
cvxpy/reductions/dcp2cone/canonicalizers/__pycache__/tr_inv_canon.cpython-313.pyc,,
cvxpy/reductions/dcp2cone/canonicalizers/__pycache__/von_neumann_entr_canon.cpython-313.pyc,,
cvxpy/reductions/dcp2cone/canonicalizers/__pycache__/xexp_canon.cpython-313.pyc,,
cvxpy/reductions/dcp2cone/canonicalizers/entr_canon.py,sha256=E4QX2VfJ04z1j3is3uO7pH_V4IfyrOdJLSLHKWmXZdA,1091
cvxpy/reductions/dcp2cone/canonicalizers/exp_canon.py,sha256=MT9Jfu3MyM7HvzpAuyH-s9XiO1HGTjH-Nibs3gbdUa4,965
cvxpy/reductions/dcp2cone/canonicalizers/geo_mean_canon.py,sha256=XBrasof6EVb_KyWb9krzaURP68MwEXWYhRbWuiwrBmI,1124
cvxpy/reductions/dcp2cone/canonicalizers/huber_canon.py,sha256=zWVS1zZpL1JU231mS6jAC1sRDJm_R34edNlvZ1uT1LU,1486
cvxpy/reductions/dcp2cone/canonicalizers/indicator_canon.py,sha256=5SrQNXJ4DMMuDNs5USRs4l8uEfD_eICrQfoFTnPVOCE,677
cvxpy/reductions/dcp2cone/canonicalizers/kl_div_canon.py,sha256=uYL6FGQFk3uxwGeMEn1OE2fwxQqNdHavhROoVn8Ts9I,937
cvxpy/reductions/dcp2cone/canonicalizers/lambda_max_canon.py,sha256=H6z9Fo9KEFrA9D7g9fWsj3WwDuGyag5GFlpsbxBpv3o,1194
cvxpy/reductions/dcp2cone/canonicalizers/lambda_sum_largest_canon.py,sha256=AewXDsWQQDwr8p92-LTGR-0D8tS9JC8AF9V3pUg0Ffo,1490
cvxpy/reductions/dcp2cone/canonicalizers/log1p_canon.py,sha256=b6Emu-xBZSthKDOpgnxlRc6FNFl3Lli0g_xCmRYoRCM,710
cvxpy/reductions/dcp2cone/canonicalizers/log_canon.py,sha256=dXp9HDzOxxoIJzfFAoSWKNwWewKsgEgosBOxLdAWORE,1049
cvxpy/reductions/dcp2cone/canonicalizers/log_det_canon.py,sha256=Fu09d9bgxyUI6jdpToRMHzeKViyCutsTlmY3r6kR3dI,2282
cvxpy/reductions/dcp2cone/canonicalizers/log_sum_exp_canon.py,sha256=dOo99BHtVr9Sc1ihHu9RQUZX87TU-D9qhoPAzDm0dls,1595
cvxpy/reductions/dcp2cone/canonicalizers/logistic_canon.py,sha256=A47-zTBnWgo6Y8XEczdUUF9CMG26qkhunJta6oPDXbQ,1113
cvxpy/reductions/dcp2cone/canonicalizers/matrix_frac_canon.py,sha256=uAxy-6zLqbuA7Ivba5B_RH_h7tsKGnKUIlzB1H66bqQ,1231
cvxpy/reductions/dcp2cone/canonicalizers/mul_canon.py,sha256=T5YbL0Xrnbw58J_IOZ7gHpteL7X1_l0L_y5Vvb7jHpM,1211
cvxpy/reductions/dcp2cone/canonicalizers/normNuc_canon.py,sha256=AsL6cCAgyYFKrjB2hP2_7OrPQJj-O1_sDO3Ba2OsaeY,1310
cvxpy/reductions/dcp2cone/canonicalizers/perspective_canon.py,sha256=pBJMICh1LHF2rlu-E5W7kvcbeEGkk7dcjPg1-s1NuoU,3672
cvxpy/reductions/dcp2cone/canonicalizers/pnorm_canon.py,sha256=qL0jYuhO1rMsPfzNFroOkmMQDZX-IJ1ZaXHrSB0YewQ,2411
cvxpy/reductions/dcp2cone/canonicalizers/power_canon.py,sha256=Y7n1-eSXj-WWKdnATL-jIDi0wGDW1fqQ2oGFwtYf6Ig,1427
cvxpy/reductions/dcp2cone/canonicalizers/quad_form_canon.py,sha256=YuBPD6TBHQYfm7AcJ4K0yC9CYsux9jpFSH1FDPFn414,1277
cvxpy/reductions/dcp2cone/canonicalizers/quad_over_lin_canon.py,sha256=5bR0DPd77Va-6XQzUJ3O5V23NqA-nIgba_3rNcZj_HQ,1133
cvxpy/reductions/dcp2cone/canonicalizers/quantum_rel_entr_canon.py,sha256=gFPwg1NvxEnqwAaCmWweVZV6SDxq3gKDhdy1pzV4Tv0,1038
cvxpy/reductions/dcp2cone/canonicalizers/rel_entr_canon.py,sha256=slWhSDoxeosPlgfZ03Yu9rPgbKtEBxjaa8XgsZJiVKk,938
cvxpy/reductions/dcp2cone/canonicalizers/sigma_max_canon.py,sha256=oz0iqhKNwfuPcQlWsAQmFF_8OMsTrFhix1_zEKiMFkE,1124
cvxpy/reductions/dcp2cone/canonicalizers/suppfunc_canon.py,sha256=_WLw44pe-SIKvdeAUK--asnfYGGvEi_EpRq6lLGC1Ig,2143
cvxpy/reductions/dcp2cone/canonicalizers/tr_inv_canon.py,sha256=T-lhrnt40CHgCaynr_RVj9xXxqS6rHIxijTUk8JCA2M,1683
cvxpy/reductions/dcp2cone/canonicalizers/von_neumann_entr_canon.py,sha256=aiJPf_w9ZzfrC1J5r8WlGquwP_oB3cOYC8LghlqK27U,1962
cvxpy/reductions/dcp2cone/canonicalizers/xexp_canon.py,sha256=3g51nRgHFhN5YunPUDpNRByKBYBj-NePGwZLaW3uTFs,1149
cvxpy/reductions/dcp2cone/cone_matrix_stuffing.py,sha256=PJlS-_pmVfkz2-TSHJousURqLBrIvS6BH2g_eTuQp_M,17710
cvxpy/reductions/dcp2cone/dcp2cone.py,sha256=ypync2sd0IYjkIuEjEMU1SgEYO--FOMWfqtBSl9UM-4,5611
cvxpy/reductions/dgp2dcp/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
cvxpy/reductions/dgp2dcp/__pycache__/__init__.cpython-313.pyc,,
cvxpy/reductions/dgp2dcp/__pycache__/dgp2dcp.cpython-313.pyc,,
cvxpy/reductions/dgp2dcp/__pycache__/util.cpython-313.pyc,,
cvxpy/reductions/dgp2dcp/canonicalizers/__init__.py,sha256=KBa11_Ld2BfizOqSDYWnLiKN5U8rrWbRYS9mxPH7XNg,5894
cvxpy/reductions/dgp2dcp/canonicalizers/__pycache__/__init__.cpython-313.pyc,,
cvxpy/reductions/dgp2dcp/canonicalizers/__pycache__/add_canon.cpython-313.pyc,,
cvxpy/reductions/dgp2dcp/canonicalizers/__pycache__/constant_canon.cpython-313.pyc,,
cvxpy/reductions/dgp2dcp/canonicalizers/__pycache__/cumprod_canon.cpython-313.pyc,,
cvxpy/reductions/dgp2dcp/canonicalizers/__pycache__/div_canon.cpython-313.pyc,,
cvxpy/reductions/dgp2dcp/canonicalizers/__pycache__/exp_canon.cpython-313.pyc,,
cvxpy/reductions/dgp2dcp/canonicalizers/__pycache__/eye_minus_inv_canon.cpython-313.pyc,,
cvxpy/reductions/dgp2dcp/canonicalizers/__pycache__/finite_set_canon.cpython-313.pyc,,
cvxpy/reductions/dgp2dcp/canonicalizers/__pycache__/geo_mean_canon.cpython-313.pyc,,
cvxpy/reductions/dgp2dcp/canonicalizers/__pycache__/gmatmul_canon.cpython-313.pyc,,
cvxpy/reductions/dgp2dcp/canonicalizers/__pycache__/log_canon.cpython-313.pyc,,
cvxpy/reductions/dgp2dcp/canonicalizers/__pycache__/mul_canon.cpython-313.pyc,,
cvxpy/reductions/dgp2dcp/canonicalizers/__pycache__/mulexpression_canon.cpython-313.pyc,,
cvxpy/reductions/dgp2dcp/canonicalizers/__pycache__/nonpos_constr_canon.cpython-313.pyc,,
cvxpy/reductions/dgp2dcp/canonicalizers/__pycache__/norm1_canon.cpython-313.pyc,,
cvxpy/reductions/dgp2dcp/canonicalizers/__pycache__/norm_inf_canon.cpython-313.pyc,,
cvxpy/reductions/dgp2dcp/canonicalizers/__pycache__/one_minus_pos_canon.cpython-313.pyc,,
cvxpy/reductions/dgp2dcp/canonicalizers/__pycache__/parameter_canon.cpython-313.pyc,,
cvxpy/reductions/dgp2dcp/canonicalizers/__pycache__/pf_eigenvalue_canon.cpython-313.pyc,,
cvxpy/reductions/dgp2dcp/canonicalizers/__pycache__/pnorm_canon.cpython-313.pyc,,
cvxpy/reductions/dgp2dcp/canonicalizers/__pycache__/power_canon.cpython-313.pyc,,
cvxpy/reductions/dgp2dcp/canonicalizers/__pycache__/prod_canon.cpython-313.pyc,,
cvxpy/reductions/dgp2dcp/canonicalizers/__pycache__/quad_form_canon.cpython-313.pyc,,
cvxpy/reductions/dgp2dcp/canonicalizers/__pycache__/quad_over_lin_canon.cpython-313.pyc,,
cvxpy/reductions/dgp2dcp/canonicalizers/__pycache__/sum_canon.cpython-313.pyc,,
cvxpy/reductions/dgp2dcp/canonicalizers/__pycache__/trace_canon.cpython-313.pyc,,
cvxpy/reductions/dgp2dcp/canonicalizers/__pycache__/xexp_canon.cpython-313.pyc,,
cvxpy/reductions/dgp2dcp/canonicalizers/__pycache__/zero_constr_canon.cpython-313.pyc,,
cvxpy/reductions/dgp2dcp/canonicalizers/add_canon.py,sha256=wXKwAB11TXssvXnXvgQfxnfpF7MMohA66bfECyUuqhw,1587
cvxpy/reductions/dgp2dcp/canonicalizers/constant_canon.py,sha256=6Cp_8uI5TsxluVk4epuVbefgLhSa7ZQzPQFcKF189Pg,732
cvxpy/reductions/dgp2dcp/canonicalizers/cumprod_canon.py,sha256=ZD1kM-dXsKH04x0eoinQ6HhN-wCZtJqKn9Cxjhj_xg8,694
cvxpy/reductions/dgp2dcp/canonicalizers/div_canon.py,sha256=iZfcG2jfDFxb-TzhrNvBb_sxwf-D0U41vJKSJMtyV5U,101
cvxpy/reductions/dgp2dcp/canonicalizers/exp_canon.py,sha256=WCzSCc4lQrM4HjsJ3CSEdNdtNdlmiOTcXrg8_LlxgdM,114
cvxpy/reductions/dgp2dcp/canonicalizers/eye_minus_inv_canon.py,sha256=F6WVQvK0nWiaCxYQzN65fJCfKf_dZSyw-4UN9zOcEIU,1434
cvxpy/reductions/dgp2dcp/canonicalizers/finite_set_canon.py,sha256=Iz36vMO5omGP13-8J23uHtQLV8jP2TW7y1M78xoZkxY,241
cvxpy/reductions/dgp2dcp/canonicalizers/geo_mean_canon.py,sha256=n5Kc6C2SHg3AxaGLYFShHOH27c3PCIclC0S4_14ZGNA,152
cvxpy/reductions/dgp2dcp/canonicalizers/gmatmul_canon.py,sha256=nLeeb6Bl7EyxA0dxv9O3rrlf_vuPuMcnwEptT71bKDE,63
cvxpy/reductions/dgp2dcp/canonicalizers/log_canon.py,sha256=YMYVsEcSGUuy4J4tuqn0-i3v46CgdjJEcpJfyRzWlgk,101
cvxpy/reductions/dgp2dcp/canonicalizers/mul_canon.py,sha256=LZKG63wbQ58PMyZlkZ4zUWqKz4q_h4Y10L6g4sJ1fFw,126
cvxpy/reductions/dgp2dcp/canonicalizers/mulexpression_canon.py,sha256=TCTY5pTLubw6OIpdmVZLrMYtRkV3Q1VeX7bdpwJ6SWM,1458
cvxpy/reductions/dgp2dcp/canonicalizers/nonpos_constr_canon.py,sha256=9Pc63vQwgdTcCRqkhDaCdhhS8uGSdGEIS70DI71SRHE,169
cvxpy/reductions/dgp2dcp/canonicalizers/norm1_canon.py,sha256=41WansFPnzfjHQwxRoxWtAFWUXyAS74PbZpYRKzdrj0,253
cvxpy/reductions/dgp2dcp/canonicalizers/norm_inf_canon.py,sha256=cs9VLde-5UWA0F6qsF6utxYURH77q7gC9s265i6BWOM,255
cvxpy/reductions/dgp2dcp/canonicalizers/one_minus_pos_canon.py,sha256=TcBqDIQvS11kMplwX77e3ou_wMdJPzbAMF-IBBCfHD8,173
cvxpy/reductions/dgp2dcp/canonicalizers/parameter_canon.py,sha256=IB3a2_-8JGJWKNiI5lcw2rAmqYpHjyC3QkRKU0Xt_a8,1015
cvxpy/reductions/dgp2dcp/canonicalizers/pf_eigenvalue_canon.py,sha256=qjxL39SNBQ2e39G-eWCyvfJmf-jAoPGNagaKhm4T5v4,684
cvxpy/reductions/dgp2dcp/canonicalizers/pnorm_canon.py,sha256=CoraxRUOS2-alGA7AuMBTABzmw1cY5dLHCd6T6_-R10,708
cvxpy/reductions/dgp2dcp/canonicalizers/power_canon.py,sha256=zWANFMhKHdNOLdNWjspJnPvHAoB3HeH4FBxNyjX3KFo,122
cvxpy/reductions/dgp2dcp/canonicalizers/prod_canon.py,sha256=RUQYglpIxeBIhP_VI2v4xQrdvmfZpLZa8XERyyPJR8g,154
cvxpy/reductions/dgp2dcp/canonicalizers/quad_form_canon.py,sha256=62WVI597u92ZaDEKv087RrjRhfFGWLQnRiZo_T-r-5Q,326
cvxpy/reductions/dgp2dcp/canonicalizers/quad_over_lin_canon.py,sha256=qaQNdkONkSB36k4SDl3n_0Skj6I7zTSNo3-RXYRQx9E,290
cvxpy/reductions/dgp2dcp/canonicalizers/sum_canon.py,sha256=66sKce6PIrJcmKWYs8xyA3WiaGYjP5N-NO52juzasBE,1296
cvxpy/reductions/dgp2dcp/canonicalizers/trace_canon.py,sha256=waCNk1dt3UaQwFPJai9zZPs0Fm5ttaGTwrTIZDqE5Ec,288
cvxpy/reductions/dgp2dcp/canonicalizers/xexp_canon.py,sha256=QAuqpyWUkwLpNDuUpnQhEQtS_RARb_A6AgItI0GHWRY,125
cvxpy/reductions/dgp2dcp/canonicalizers/zero_constr_canon.py,sha256=MiVn4pl_LhI8aH25zp4FHXckAQLM8u0Y0xvbRDwfegI,161
cvxpy/reductions/dgp2dcp/dgp2dcp.py,sha256=MDgsTpMQ0gO06cqRGCUgwCa1ff-1fkYJnBUof5UAPMw,3839
cvxpy/reductions/dgp2dcp/util.py,sha256=frC_e075-zZsHlIQGA1b6nCHjw8NX9V-KFDIpHI48C0,851
cvxpy/reductions/discrete2mixedint/__init__.py,sha256=AbpHGcgLb-kRsJGnwFEktk7uzpZOCcBY74-YBdrKVGs,1
cvxpy/reductions/discrete2mixedint/__pycache__/__init__.cpython-313.pyc,,
cvxpy/reductions/discrete2mixedint/__pycache__/valinvec2mixedint.cpython-313.pyc,,
cvxpy/reductions/discrete2mixedint/valinvec2mixedint.py,sha256=ZPctZqWwd4IzsHLWzsVJ4SmHMkMf2KZzTx1ZItlZOUQ,2728
cvxpy/reductions/dqcp2dcp/__init__.py,sha256=WruJUluIFa7FwY__fmmvEJom76dmiLsV-b7uVmreBHc,562
cvxpy/reductions/dqcp2dcp/__pycache__/__init__.cpython-313.pyc,,
cvxpy/reductions/dqcp2dcp/__pycache__/dqcp2dcp.cpython-313.pyc,,
cvxpy/reductions/dqcp2dcp/__pycache__/inverse.cpython-313.pyc,,
cvxpy/reductions/dqcp2dcp/__pycache__/sets.cpython-313.pyc,,
cvxpy/reductions/dqcp2dcp/__pycache__/tighten.cpython-313.pyc,,
cvxpy/reductions/dqcp2dcp/dqcp2dcp.py,sha256=C-_ctquv8m8dHadzp7Ojxhfb9pXa2nnlUKu6sRenA58,9583
cvxpy/reductions/dqcp2dcp/inverse.py,sha256=fbwMEa1mv62GZ-jA4Jzz0R-cfG8xJy_N3R5pwYjDVM8,3453
cvxpy/reductions/dqcp2dcp/sets.py,sha256=aGptsJ7wpV2u0kCi4iGUexIkCJRz3K78_pIZQDGRsDY,5154
cvxpy/reductions/dqcp2dcp/tighten.py,sha256=T27ts3DEHkK0JhIycvRiyOYImiYj6jcKcevz-SITwts,1154
cvxpy/reductions/eliminate_pwl/__init__.py,sha256=bYcHRT2P3ANqtdnM4KD51vnwM0o7Dcix5FZCxWMChiE,566
cvxpy/reductions/eliminate_pwl/__pycache__/__init__.cpython-313.pyc,,
cvxpy/reductions/eliminate_pwl/__pycache__/eliminate_pwl.cpython-313.pyc,,
cvxpy/reductions/eliminate_pwl/canonicalizers/__init__.py,sha256=05vXNp1Af-UaH33xva3UIAde0PynV7P6Wgewywr0WgU,2000
cvxpy/reductions/eliminate_pwl/canonicalizers/__pycache__/__init__.cpython-313.pyc,,
cvxpy/reductions/eliminate_pwl/canonicalizers/__pycache__/abs_canon.cpython-313.pyc,,
cvxpy/reductions/eliminate_pwl/canonicalizers/__pycache__/cummax_canon.cpython-313.pyc,,
cvxpy/reductions/eliminate_pwl/canonicalizers/__pycache__/cumsum_canon.cpython-313.pyc,,
cvxpy/reductions/eliminate_pwl/canonicalizers/__pycache__/dotsort_canon.cpython-313.pyc,,
cvxpy/reductions/eliminate_pwl/canonicalizers/__pycache__/max_canon.cpython-313.pyc,,
cvxpy/reductions/eliminate_pwl/canonicalizers/__pycache__/maximum_canon.cpython-313.pyc,,
cvxpy/reductions/eliminate_pwl/canonicalizers/__pycache__/min_canon.cpython-313.pyc,,
cvxpy/reductions/eliminate_pwl/canonicalizers/__pycache__/minimum_canon.cpython-313.pyc,,
cvxpy/reductions/eliminate_pwl/canonicalizers/__pycache__/norm1_canon.cpython-313.pyc,,
cvxpy/reductions/eliminate_pwl/canonicalizers/__pycache__/norm_inf_canon.cpython-313.pyc,,
cvxpy/reductions/eliminate_pwl/canonicalizers/__pycache__/sum_largest_canon.cpython-313.pyc,,
cvxpy/reductions/eliminate_pwl/canonicalizers/abs_canon.py,sha256=RlxKqhjqTSiinldfIxRGl4fohTdnPE1MpriOPNCUAoY,748
cvxpy/reductions/eliminate_pwl/canonicalizers/cummax_canon.py,sha256=naP4UMOQXL2p8QmDoZ-60u-a8ZLpYxYzbV8lVi1EuWY,1106
cvxpy/reductions/eliminate_pwl/canonicalizers/cumsum_canon.py,sha256=ysa8HDfe0sjN0R1w8nRjHSsFNpuvifrrFaMVJNeEB9s,1133
cvxpy/reductions/eliminate_pwl/canonicalizers/dotsort_canon.py,sha256=uPHXu89x8dFzdQdbxOr3vF1Ug7YBbBq8bcYUaQ6fKAM,1451
cvxpy/reductions/eliminate_pwl/canonicalizers/max_canon.py,sha256=LJHJt2tarQ1OKyZn0n52arLiVStT6gX4E1vEk9bDyVI,1239
cvxpy/reductions/eliminate_pwl/canonicalizers/maximum_canon.py,sha256=i0YLUHQI-ux-H_qWi_ym8szu34etjZnh-Z3S7PmguyQ,941
cvxpy/reductions/eliminate_pwl/canonicalizers/min_canon.py,sha256=vGeuVAkgV0-RuzrBOSkra5BANh5vdds3cNvKgZlMLnw,917
cvxpy/reductions/eliminate_pwl/canonicalizers/minimum_canon.py,sha256=L-AYNvzICY2TelFV1_gsVW4alZI5JH3VEqlcGOuGnu8,875
cvxpy/reductions/eliminate_pwl/canonicalizers/norm1_canon.py,sha256=puRswIklm5HH9JeyMFb_PoK8TbvCWx2lRkDZKCk7ArI,1173
cvxpy/reductions/eliminate_pwl/canonicalizers/norm_inf_canon.py,sha256=ESBy24dphE1wY6mPDPajFTk99jbO3NFq9r42FOXwW-U,1235
cvxpy/reductions/eliminate_pwl/canonicalizers/sum_largest_canon.py,sha256=BoIOErXICefNYkuGGwFxFMYiyMvQjMPrcNKGxw33QIA,917
cvxpy/reductions/eliminate_pwl/eliminate_pwl.py,sha256=QC4dUHqKi2ZIF3U_hvPVIHODHXIVGTwU685Zhj88WMo,1451
cvxpy/reductions/eval_params.py,sha256=V9wcXR38RLsr6GE9HXxa-GB6Rht-WkBHyNiuzUELxKY,2689
cvxpy/reductions/flip_objective.py,sha256=k8JS3HQ8BycG2De3UFhX53oO528K3ETW18wZq3jOEkM,2051
cvxpy/reductions/inverse_data.py,sha256=ldKW1omGmmrU94Mgh7G25Bv61zuV-lQHeP3jGr9VYnc,1944
cvxpy/reductions/matrix_stuffing.py,sha256=sLhgqUrPXwFPw7TLzweWojoIqlMvyMC0O5RsxhJzI2g,4252
cvxpy/reductions/qp2quad_form/__init__.py,sha256=gJC172nRSE1HpNmH8q20u1qeuLgXQ3PJDNIE0Iph6aQ,564
cvxpy/reductions/qp2quad_form/__pycache__/__init__.cpython-313.pyc,,
cvxpy/reductions/qp2quad_form/__pycache__/qp2symbolic_qp.cpython-313.pyc,,
cvxpy/reductions/qp2quad_form/__pycache__/qp_matrix_stuffing.cpython-313.pyc,,
cvxpy/reductions/qp2quad_form/canonicalizers/__init__.py,sha256=Bf69-QF7kK2Pnl0Juw5jMDuI0GXQ_gwMIAfcXKErbXk,2072
cvxpy/reductions/qp2quad_form/canonicalizers/__pycache__/__init__.cpython-313.pyc,,
cvxpy/reductions/qp2quad_form/canonicalizers/__pycache__/huber_canon.cpython-313.pyc,,
cvxpy/reductions/qp2quad_form/canonicalizers/__pycache__/power_canon.cpython-313.pyc,,
cvxpy/reductions/qp2quad_form/canonicalizers/__pycache__/quad_form_canon.cpython-313.pyc,,
cvxpy/reductions/qp2quad_form/canonicalizers/__pycache__/quad_over_lin_canon.cpython-313.pyc,,
cvxpy/reductions/qp2quad_form/canonicalizers/huber_canon.py,sha256=huyjs-pMDXv1L5LbnOq_t-s1HtKkGUG3yRM7bPuEqTs,1475
cvxpy/reductions/qp2quad_form/canonicalizers/power_canon.py,sha256=yFZFjPbUrkOn-Y7Brr2tgLnzu4rqvmNzFeBXusxjhC8,1431
cvxpy/reductions/qp2quad_form/canonicalizers/quad_form_canon.py,sha256=VP22P6MS0zR5TvOoBizBD8S2Q9KXAMYRCNGTrsDjDKY,967
cvxpy/reductions/qp2quad_form/canonicalizers/quad_over_lin_canon.py,sha256=RX2FbDP7aVPyHR9HdgO7_d9SWBHWxmh55qON5Za2BQs,1298
cvxpy/reductions/qp2quad_form/qp2symbolic_qp.py,sha256=nZyycXTpn1DGXucfL5zP_2sydz9_YLNfwRF7Ym1XwoE,2379
cvxpy/reductions/qp2quad_form/qp_matrix_stuffing.py,sha256=P7ZHSrtQmN5g0yRwN1zgemJazm4Dw3oQ3NxRL9rfxd0,12844
cvxpy/reductions/reduction.py,sha256=o0g7lyrWlyDTXTWt3-ykS421KxpwqPB8Gqt8L3XOLjg,5040
cvxpy/reductions/solution.py,sha256=avmqGHhzMnUpga0NsFe6UJFBRPohsbb_Fy_fwjckc08,3020
cvxpy/reductions/solvers/__init__.py,sha256=JDqz7falKze_sCFsDtUj_bq9Ra-w6qU5PEJM1LwZA7o,563
cvxpy/reductions/solvers/__pycache__/__init__.cpython-313.pyc,,
cvxpy/reductions/solvers/__pycache__/bisection.cpython-313.pyc,,
cvxpy/reductions/solvers/__pycache__/compr_matrix.cpython-313.pyc,,
cvxpy/reductions/solvers/__pycache__/constant_solver.cpython-313.pyc,,
cvxpy/reductions/solvers/__pycache__/defines.cpython-313.pyc,,
cvxpy/reductions/solvers/__pycache__/intermediate_chain.cpython-313.pyc,,
cvxpy/reductions/solvers/__pycache__/kktsolver.cpython-313.pyc,,
cvxpy/reductions/solvers/__pycache__/solver.cpython-313.pyc,,
cvxpy/reductions/solvers/__pycache__/solving_chain.cpython-313.pyc,,
cvxpy/reductions/solvers/__pycache__/solving_chain_utils.cpython-313.pyc,,
cvxpy/reductions/solvers/__pycache__/utilities.cpython-313.pyc,,
cvxpy/reductions/solvers/bisection.py,sha256=TdMcX7LYZbAfT1zsP_QlYqDyB1JyMOudY9EbECb4_l0,7382
cvxpy/reductions/solvers/compr_matrix.py,sha256=H-9expnYDz-VNrYu8Uo9gktURuy10ZCwxYGmwYGalJY,3636
cvxpy/reductions/solvers/conic_solvers/__init__.py,sha256=YGCBjC0-r6ZCHyW0yAqFCBMoDSzijp18IHUt6yfrVxU,1204
cvxpy/reductions/solvers/conic_solvers/__pycache__/__init__.cpython-313.pyc,,
cvxpy/reductions/solvers/conic_solvers/__pycache__/cbc_conif.cpython-313.pyc,,
cvxpy/reductions/solvers/conic_solvers/__pycache__/clarabel_conif.cpython-313.pyc,,
cvxpy/reductions/solvers/conic_solvers/__pycache__/conic_solver.cpython-313.pyc,,
cvxpy/reductions/solvers/conic_solvers/__pycache__/copt_conif.cpython-313.pyc,,
cvxpy/reductions/solvers/conic_solvers/__pycache__/cplex_conif.cpython-313.pyc,,
cvxpy/reductions/solvers/conic_solvers/__pycache__/cvxopt_conif.cpython-313.pyc,,
cvxpy/reductions/solvers/conic_solvers/__pycache__/diffcp_conif.cpython-313.pyc,,
cvxpy/reductions/solvers/conic_solvers/__pycache__/ecos_bb_conif.cpython-313.pyc,,
cvxpy/reductions/solvers/conic_solvers/__pycache__/ecos_conif.cpython-313.pyc,,
cvxpy/reductions/solvers/conic_solvers/__pycache__/glop_conif.cpython-313.pyc,,
cvxpy/reductions/solvers/conic_solvers/__pycache__/glpk_conif.cpython-313.pyc,,
cvxpy/reductions/solvers/conic_solvers/__pycache__/glpk_mi_conif.cpython-313.pyc,,
cvxpy/reductions/solvers/conic_solvers/__pycache__/gurobi_conif.cpython-313.pyc,,
cvxpy/reductions/solvers/conic_solvers/__pycache__/highs_conif.cpython-313.pyc,,
cvxpy/reductions/solvers/conic_solvers/__pycache__/mosek_conif.cpython-313.pyc,,
cvxpy/reductions/solvers/conic_solvers/__pycache__/nag_conif.cpython-313.pyc,,
cvxpy/reductions/solvers/conic_solvers/__pycache__/pdlp_conif.cpython-313.pyc,,
cvxpy/reductions/solvers/conic_solvers/__pycache__/qoco_conif.cpython-313.pyc,,
cvxpy/reductions/solvers/conic_solvers/__pycache__/scip_conif.cpython-313.pyc,,
cvxpy/reductions/solvers/conic_solvers/__pycache__/scipy_conif.cpython-313.pyc,,
cvxpy/reductions/solvers/conic_solvers/__pycache__/scs_conif.cpython-313.pyc,,
cvxpy/reductions/solvers/conic_solvers/__pycache__/sdpa_conif.cpython-313.pyc,,
cvxpy/reductions/solvers/conic_solvers/__pycache__/xpress_conif.cpython-313.pyc,,
cvxpy/reductions/solvers/conic_solvers/cbc_conif.py,sha256=eh-MkEnE1aDsBpsdM8je83JR77b4QMEfvZ_RVj1lFB0,7745
cvxpy/reductions/solvers/conic_solvers/clarabel_conif.py,sha256=JKxwgal_pEE-emsis63-WXvRMuCuoZvBdMbROIdsjVk,11801
cvxpy/reductions/solvers/conic_solvers/conic_solver.py,sha256=8TvVeutHXAWAV89R2iXCRKO1W1y4u8NghmcyDo46YUs,14418
cvxpy/reductions/solvers/conic_solvers/copt_conif.py,sha256=VqUSOBTtmGEh57qJ2jf-v_P9LXIgo2SaeGaszXIj_AA,14772
cvxpy/reductions/solvers/conic_solvers/cplex_conif.py,sha256=Rb86BE1eNB7pGHmksAuV3QbUlieg6WEQzq8BJls7kc8,18657
cvxpy/reductions/solvers/conic_solvers/cvxopt_conif.py,sha256=MDW_KzSGNxtNa70Ic0NzN372tZo23d67RJ0O3nrq2UM,13587
cvxpy/reductions/solvers/conic_solvers/diffcp_conif.py,sha256=vzgI9hfW0S3Lz-rRE2EifLgyq_QqyM_csOTRrZa-82o,6921
cvxpy/reductions/solvers/conic_solvers/ecos_bb_conif.py,sha256=JcSUv_1qVl7AZz30_PC77U0NPedkwQHUqX1-XuSPUKo,5058
cvxpy/reductions/solvers/conic_solvers/ecos_conif.py,sha256=GSY05REB8KHRfjrfWnRSQa3gzJVhOg-q9lJx9FtNVps,6676
cvxpy/reductions/solvers/conic_solvers/glop_conif.py,sha256=nM0UFF6qjZ29exCNsK5bjYdWzHDpFlxCQytsWKT6l20,8906
cvxpy/reductions/solvers/conic_solvers/glpk_conif.py,sha256=6_8QsiyeOVC3UB0YBFAS6s7IDCdqDqkyDqa8J8iI4fY,3939
cvxpy/reductions/solvers/conic_solvers/glpk_mi_conif.py,sha256=o7mlHgWbJDBXomAQpd3faMA8Tb3iZYdV4TNx7rhWTZk,4477
cvxpy/reductions/solvers/conic_solvers/gurobi_conif.py,sha256=Bw3JA_j2X1-XFUOMZfanPDF-TV1F8xm_30VUjx88ST8,14570
cvxpy/reductions/solvers/conic_solvers/highs_conif.py,sha256=jRgReVEEqpU-p2h8aSH2WXwwBS7UZwUXpj-92xevhAw,8212
cvxpy/reductions/solvers/conic_solvers/mosek_conif.py,sha256=7f2kC9LvVBHqaHPwDaNfhFp1k4wGwrvWKQkX2DTFglE,32337
cvxpy/reductions/solvers/conic_solvers/nag_conif.py,sha256=JEGDoxgmCWItQKyjKInhakkOXcBFR3c-se7RJ_btLXo,10426
cvxpy/reductions/solvers/conic_solvers/pdlp_conif.py,sha256=pLs4Z-Z_3VPq3YztTJvLhcBB9zhX79eWH1AoF76rvdc,8449
cvxpy/reductions/solvers/conic_solvers/qoco_conif.py,sha256=sLrXzAXA6UUGBLIhPNzasf7edJ-rdq5Lwn4Vl4gnTLE,5827
cvxpy/reductions/solvers/conic_solvers/scip_conif.py,sha256=ZqQ18UVKaZ94JYqnNk7KPsGPCxF2pgtDDpurtRd7aSI,15848
cvxpy/reductions/solvers/conic_solvers/scipy_conif.py,sha256=rROcO9sRklPrioSq-mgiLZTMLaTjUHCsgVaTRNN3wNM,11397
cvxpy/reductions/solvers/conic_solvers/scs_conif.py,sha256=ampj7kOjUVJkSvf2-DoS9VsA5h-5_UyLUX_B4im6DbQ,13211
cvxpy/reductions/solvers/conic_solvers/sdpa_conif.py,sha256=5txTXA7G1qniwR1PI8DaSbF6XrKTLuf61GsB-zd5Un8,6584
cvxpy/reductions/solvers/conic_solvers/xpress_conif.py,sha256=gHQOLKrodg5y-6duJ0XKP0HCP9MZnI6Jr4n0izBnhxg,15671
cvxpy/reductions/solvers/constant_solver.py,sha256=-Jzydejw3OXU_LklGlyt9fA8uIOYRTjiGVK7e-9ZdiU,1085
cvxpy/reductions/solvers/defines.py,sha256=ci8VMgQWQGaIN7Pm-j4kpQhw1yu_mOZRnOzjN6M6WXk,5840
cvxpy/reductions/solvers/intermediate_chain.py,sha256=FXVeC5oNsIc4aNuFgokBRtV92kAk75s5XfKe6bOapDc,4177
cvxpy/reductions/solvers/kktsolver.py,sha256=jJpEBaADAid_8taYobQIiUe444zBuEoiilYy6NMOvcc,4372
cvxpy/reductions/solvers/lp_solvers/__init__.py,sha256=bYcHRT2P3ANqtdnM4KD51vnwM0o7Dcix5FZCxWMChiE,566
cvxpy/reductions/solvers/lp_solvers/__pycache__/__init__.cpython-313.pyc,,
cvxpy/reductions/solvers/qp_solvers/__init__.py,sha256=JDqz7falKze_sCFsDtUj_bq9Ra-w6qU5PEJM1LwZA7o,563
cvxpy/reductions/solvers/qp_solvers/__pycache__/__init__.cpython-313.pyc,,
cvxpy/reductions/solvers/qp_solvers/__pycache__/copt_qpif.cpython-313.pyc,,
cvxpy/reductions/solvers/qp_solvers/__pycache__/cplex_qpif.cpython-313.pyc,,
cvxpy/reductions/solvers/qp_solvers/__pycache__/daqp_qpif.cpython-313.pyc,,
cvxpy/reductions/solvers/qp_solvers/__pycache__/gurobi_qpif.cpython-313.pyc,,
cvxpy/reductions/solvers/qp_solvers/__pycache__/highs_qpif.cpython-313.pyc,,
cvxpy/reductions/solvers/qp_solvers/__pycache__/osqp_qpif.cpython-313.pyc,,
cvxpy/reductions/solvers/qp_solvers/__pycache__/piqp_qpif.cpython-313.pyc,,
cvxpy/reductions/solvers/qp_solvers/__pycache__/proxqp_qpif.cpython-313.pyc,,
cvxpy/reductions/solvers/qp_solvers/__pycache__/qp_solver.cpython-313.pyc,,
cvxpy/reductions/solvers/qp_solvers/__pycache__/xpress_qpif.cpython-313.pyc,,
cvxpy/reductions/solvers/qp_solvers/copt_qpif.py,sha256=ehX46HP68fExrZoOfBCRH7wAAZoWQw7R7Sc5q4mD6vA,6257
cvxpy/reductions/solvers/qp_solvers/cplex_qpif.py,sha256=t2Q8cmKEf9P-VwhoYPVSRYbWCKzlKtb7Paz2Jre6kUE,5766
cvxpy/reductions/solvers/qp_solvers/daqp_qpif.py,sha256=vI5ifrNEqAxsaBd-R1dIXoS6f0OWQvVL6QlTmQ6NlMA,6319
cvxpy/reductions/solvers/qp_solvers/gurobi_qpif.py,sha256=5IOkm3ykgw_Te72rCBfZrC5TLrH8RjXDJFiEmjWLQzo,7932
cvxpy/reductions/solvers/qp_solvers/highs_qpif.py,sha256=2QLw4yKQjaId064MYcnevWUyy-qnevplRVhgEOtu0lc,7478
cvxpy/reductions/solvers/qp_solvers/osqp_qpif.py,sha256=6Yqwe-L-ZgHAGAxxRpPuThvzgLN3uJna6p9s6WqeRmA,5194
cvxpy/reductions/solvers/qp_solvers/piqp_qpif.py,sha256=gDxaBPKBNvl6yqBAJh_y__bFWuNCFz3eG66CHVVrE0w,3773
cvxpy/reductions/solvers/qp_solvers/proxqp_qpif.py,sha256=bKG3ifOvVjwXmsu3rE1x8YbjREabVH5UrVLuGNGUsAQ,5723
cvxpy/reductions/solvers/qp_solvers/qp_solver.py,sha256=hUN_GlgOz8UXPQ_4HucwiUAu_izr5im41avnak_-Tn0,3661
cvxpy/reductions/solvers/qp_solvers/xpress_qpif.py,sha256=z5HOuRW66fm8zqfNAugtVG8AlPeX8pksRKkwq3uFLzA,9439
cvxpy/reductions/solvers/solver.py,sha256=hCjzna9av_qjkvs_lHZWy6vkvb6uLinVKGWDPkmZ5Ek,2492
cvxpy/reductions/solvers/solving_chain.py,sha256=DXxWp6n0kvmWeaezu0rwxiqn1IhjGh8VV9V2nW0JCfc,18865
cvxpy/reductions/solvers/solving_chain_utils.py,sha256=NDa2Snxuhm3PETz4wm8GfsfMe13sENwzFW52AVuqcu4,2117
cvxpy/reductions/solvers/utilities.py,sha256=oPeFnSoAr66ZoUGXxjc4Br4f5G5VsB0OvG0ZkLWEasE,2830
cvxpy/reductions/utilities.py,sha256=DoHAN6kvpV6g3stZOOsNUBUlTnqL6K2pnK9GKkfz-2M,6144
cvxpy/settings.py,sha256=vqXbwA9Ha4cESv0_ixYdHLe76dNxayiC9Pzqcm7k9zo,5521
cvxpy/tests/__init__.py,sha256=JDqz7falKze_sCFsDtUj_bq9Ra-w6qU5PEJM1LwZA7o,563
cvxpy/tests/__pycache__/__init__.cpython-313.pyc,,
cvxpy/tests/__pycache__/base_test.cpython-313.pyc,,
cvxpy/tests/__pycache__/ram_limited.cpython-313.pyc,,
cvxpy/tests/__pycache__/solver_test_helpers.cpython-313.pyc,,
cvxpy/tests/__pycache__/test_KKT.cpython-313.pyc,,
cvxpy/tests/__pycache__/test_atoms.cpython-313.pyc,,
cvxpy/tests/__pycache__/test_attributes.cpython-313.pyc,,
cvxpy/tests/__pycache__/test_base_classes.cpython-313.pyc,,
cvxpy/tests/__pycache__/test_canon_sign.cpython-313.pyc,,
cvxpy/tests/__pycache__/test_coeff_extractor.cpython-313.pyc,,
cvxpy/tests/__pycache__/test_complex.cpython-313.pyc,,
cvxpy/tests/__pycache__/test_cone2cone.cpython-313.pyc,,
cvxpy/tests/__pycache__/test_conic_solvers.cpython-313.pyc,,
cvxpy/tests/__pycache__/test_constant.cpython-313.pyc,,
cvxpy/tests/__pycache__/test_constant_atoms.cpython-313.pyc,,
cvxpy/tests/__pycache__/test_constraints.cpython-313.pyc,,
cvxpy/tests/__pycache__/test_convolution.cpython-313.pyc,,
cvxpy/tests/__pycache__/test_copt_write.cpython-313.pyc,,
cvxpy/tests/__pycache__/test_copy.cpython-313.pyc,,
cvxpy/tests/__pycache__/test_curvature.cpython-313.pyc,,
cvxpy/tests/__pycache__/test_custom_solver.cpython-313.pyc,,
cvxpy/tests/__pycache__/test_derivative.cpython-313.pyc,,
cvxpy/tests/__pycache__/test_dgp.cpython-313.pyc,,
cvxpy/tests/__pycache__/test_dgp2dcp.cpython-313.pyc,,
cvxpy/tests/__pycache__/test_domain.cpython-313.pyc,,
cvxpy/tests/__pycache__/test_dpp.cpython-313.pyc,,
cvxpy/tests/__pycache__/test_dqcp.cpython-313.pyc,,
cvxpy/tests/__pycache__/test_errors.cpython-313.pyc,,
cvxpy/tests/__pycache__/test_examples.cpython-313.pyc,,
cvxpy/tests/__pycache__/test_expression_methods.cpython-313.pyc,,
cvxpy/tests/__pycache__/test_expressions.cpython-313.pyc,,
cvxpy/tests/__pycache__/test_grad.cpython-313.pyc,,
cvxpy/tests/__pycache__/test_gurobi_write.cpython-313.pyc,,
cvxpy/tests/__pycache__/test_interfaces.cpython-313.pyc,,
cvxpy/tests/__pycache__/test_kron_canon.cpython-313.pyc,,
cvxpy/tests/__pycache__/test_lin_ops.cpython-313.pyc,,
cvxpy/tests/__pycache__/test_linalg_utils.cpython-313.pyc,,
cvxpy/tests/__pycache__/test_linear_cone.cpython-313.pyc,,
cvxpy/tests/__pycache__/test_matrices.cpython-313.pyc,,
cvxpy/tests/__pycache__/test_mip_vars.cpython-313.pyc,,
cvxpy/tests/__pycache__/test_monotonicity.cpython-313.pyc,,
cvxpy/tests/__pycache__/test_nonlinear_atoms.cpython-313.pyc,,
cvxpy/tests/__pycache__/test_objectives.cpython-313.pyc,,
cvxpy/tests/__pycache__/test_param_cone_prog.cpython-313.pyc,,
cvxpy/tests/__pycache__/test_param_quad_prog.cpython-313.pyc,,
cvxpy/tests/__pycache__/test_perspective.cpython-313.pyc,,
cvxpy/tests/__pycache__/test_power_tools.cpython-313.pyc,,
cvxpy/tests/__pycache__/test_problem.cpython-313.pyc,,
cvxpy/tests/__pycache__/test_python_backends.cpython-313.pyc,,
cvxpy/tests/__pycache__/test_qp_solvers.cpython-313.pyc,,
cvxpy/tests/__pycache__/test_quad_form.cpython-313.pyc,,
cvxpy/tests/__pycache__/test_quadratic.cpython-313.pyc,,
cvxpy/tests/__pycache__/test_quantum_rel_entr.cpython-313.pyc,,
cvxpy/tests/__pycache__/test_scalarize.cpython-313.pyc,,
cvxpy/tests/__pycache__/test_semidefinite_vars.cpython-313.pyc,,
cvxpy/tests/__pycache__/test_shape.cpython-313.pyc,,
cvxpy/tests/__pycache__/test_sign.cpython-313.pyc,,
cvxpy/tests/__pycache__/test_suppfunc.cpython-313.pyc,,
cvxpy/tests/__pycache__/test_valinvec2mixedint.cpython-313.pyc,,
cvxpy/tests/__pycache__/test_versioning.cpython-313.pyc,,
cvxpy/tests/__pycache__/test_von_neumann_entr.cpython-313.pyc,,
cvxpy/tests/base_test.py,sha256=sq6g8SL_UeKOc4pebT5bXpnJNN7xTINVZ8HGaYPrcTk,1497
cvxpy/tests/ram_limited.py,sha256=SqP-qMWcxtwp7GZVRW8nE6R7DddB2g7mC94IZ6uwxzQ,1963
cvxpy/tests/solver_test_helpers.py,sha256=mF3CVJ9W9F7HBTLnHCWrd2jx1m6k5zf5HMDq-ZNt08I,51584
cvxpy/tests/test_KKT.py,sha256=gvkC7-L_u7O0aLwH5p0ib0xeublRH-E1m-AP9X6wonY,14087
cvxpy/tests/test_atoms.py,sha256=RFCDng_ARgTgYxuuoUY8MCcAuZSwDq5SQt81xHjxcF4,81646
cvxpy/tests/test_attributes.py,sha256=HfemZKQjB91BdHUmWn3Jm3ONfsJsQtEwZzmBOLcnpTc,9250
cvxpy/tests/test_base_classes.py,sha256=2CE__U5UHZL9BIsn3Gnhum6Ez_Rt1nBMSCQVEoKwgYY,874
cvxpy/tests/test_canon_sign.py,sha256=3TiWR4jbw3-a6bL02sjfeWS6I0IyefeT5HmbgW7jpXQ,2055
cvxpy/tests/test_coeff_extractor.py,sha256=IQu8k629V4y0tNpGPYrWQmxI2hovfr86GPDqItZ2Vxw,6910
cvxpy/tests/test_complex.py,sha256=I9VnEGWPc-c2oYINprNeNkamlyl4J4rPGFoa6cqFekw,29610
cvxpy/tests/test_cone2cone.py,sha256=PapkUaKjVPmyL9XyZxfg4jYojO82IcSZ7VhUCe_Jfeo,29962
cvxpy/tests/test_conic_solvers.py,sha256=VlVDHPol40zkl9NNg5aD9GCxl7NbXOS40MPzhD-nV4A,90929
cvxpy/tests/test_constant.py,sha256=rsyVN8iB2HSFw8FOIzUzDzkCJG-TVKKet30zjlq5ZjI,2924
cvxpy/tests/test_constant_atoms.py,sha256=OVcZhPfV-wKeVviXHtnUrDPGMBbdFU_AJumd6gWogCs,19098
cvxpy/tests/test_constraints.py,sha256=UtKfrZdTzoXvgLY_b_vd6xXHAWQHlIVofcuwV1rUh_4,22596
cvxpy/tests/test_convolution.py,sha256=v2qLt1tCOSGOoJ25MevVOPCO5IBcLTPxl6kQLxCLTfs,6719
cvxpy/tests/test_copt_write.py,sha256=DsssEKK4dYU_MvirS8ofqQawLH4JM9WZuj7YekZuRHE,1244
cvxpy/tests/test_copy.py,sha256=JGOL0FplgB14MJkjMA8dwm-SEWfDTHMOaZhvQe-kK7g,2983
cvxpy/tests/test_curvature.py,sha256=lqkNbgfO97rzHCEucLDu3sB8X8KwJbdci3pfjr7Whv8,3491
cvxpy/tests/test_custom_solver.py,sha256=-rFNwAz2bMMMSIha46JFJTDAr7kGB8RRbkn28tKHneU,3939
cvxpy/tests/test_derivative.py,sha256=TG6BMs1XlQ-4kaNqF0GHRfpVB6uojd-WfrS1eVANWWA,25498
cvxpy/tests/test_dgp.py,sha256=gZhSrNF6x67EgbvSbniKC62urFkhgujyzs6fTW6wGAs,9023
cvxpy/tests/test_dgp2dcp.py,sha256=_xJIR_jCHGlktff4xfZ2xlkQBjbGACdkxf1uULXJt2o,27374
cvxpy/tests/test_domain.py,sha256=hW1hQH88tBArqUQoYcqZJHSUcA-rETFHiF420GNnWQU,7205
cvxpy/tests/test_dpp.py,sha256=2wKhLlvoDkXQQd8N2cacvVHa3KtrANhcdREccAs3Dho,34619
cvxpy/tests/test_dqcp.py,sha256=EeDCvt3iMIx4khxHeVr12Ap4OlJ3YbG7rgrmG_DPjuc,28004
cvxpy/tests/test_errors.py,sha256=578y6rJbP_BFe02sd0ajP2KyEUiMLAewH8jAu-gwRYg,4296
cvxpy/tests/test_examples.py,sha256=i0oB_BU1M5RQZCMkh3z_8OKoRLat7Blr6c72xJJThvM,20364
cvxpy/tests/test_expression_methods.py,sha256=OMDnhcufteYcbshBYuF59NTGIkyFi8B3Mopcqdnq0MI,14776
cvxpy/tests/test_expressions.py,sha256=wqbzq9-yXleX1k1xOMa10L-7NEuOj8aA_L-R3l659yE,65798
cvxpy/tests/test_grad.py,sha256=JtQZ6R5htsfzODD8M1UVt9lW9Ii65Rv35e0YKs-i5Yg,33272
cvxpy/tests/test_gurobi_write.py,sha256=xLo9GWRW496agIWW5Q5lLsii8QEBcskYrRjRSVlNBLo,1252
cvxpy/tests/test_interfaces.py,sha256=b9OS20fsPpla91p7XCT9B8yAJOBdcn7Yo-I9XwxcmK4,7545
cvxpy/tests/test_kron_canon.py,sha256=c8z0PC0dpogsA4UZC4BppFKFP_6pdZQJ2IhuVuaW-KA,7605
cvxpy/tests/test_lin_ops.py,sha256=vLM8rXIR5K1ncntSrO1DXDa9ZisSMFBPGfZLdbxW2oA,4949
cvxpy/tests/test_linalg_utils.py,sha256=DhIYLuEytGXNz75E2L-kNYjC0kmlcniNjsZSVuyYpkQ,3165
cvxpy/tests/test_linear_cone.py,sha256=jH8pqt7P2IQiuqKuCXPxgBjoVMdKkYzV4r7AgKsoZ3E,16635
cvxpy/tests/test_matrices.py,sha256=NIY85Elk8d61PzS1xDNB3KdbQu8MktOg6q54C5HNfRU,5068
cvxpy/tests/test_mip_vars.py,sha256=Z8OXfx_z0eKD41nrQThO6P6ZIx8RYU08b-dySVn6HyU,4090
cvxpy/tests/test_monotonicity.py,sha256=PNsl8malgFwyVF2qJCbI4JSDfINlSFZfT41B-du1rVU,2948
cvxpy/tests/test_nonlinear_atoms.py,sha256=1Pq3Y0lQaCkWKFROe0VoMaI2lJCfmKXtSgqNZqMa-Xk,6482
cvxpy/tests/test_objectives.py,sha256=GspsuXH0zNqbB0sQNUy0iCCHvgmcNSsHYtDpZv585RU,4985
cvxpy/tests/test_param_cone_prog.py,sha256=UgXx0HKAxz654QBPkEdEGbGcJXrZktIUcmH0OjayxXk,5803
cvxpy/tests/test_param_quad_prog.py,sha256=TEabnCpi5o-T-xd8q0tHdzBfYh8I1je1LCZs7z2o30o,6766
cvxpy/tests/test_perspective.py,sha256=KmqDV6vespIBebQYiR-HeeBcCSg8nf2EWIBUWq-4VlQ,13349
cvxpy/tests/test_power_tools.py,sha256=cFyZW8WPPRdfc6m4R_oHscbFpCKeaV8YF4ZXEWa3Efc,4341
cvxpy/tests/test_problem.py,sha256=_BFV9BifxuCk0LWrTzzSBL37P-8xUNC2l9q6dbIPOAY,88519
cvxpy/tests/test_python_backends.py,sha256=zmUWTo1JjYl-elGyv0iKDKrKWv66BQh5jmjBvUvakms,92529
cvxpy/tests/test_qp_solvers.py,sha256=riX4x6rQUMEne5btzo9JRO1RkxcBFgDNuN_J-6iQ49g,23360
cvxpy/tests/test_quad_form.py,sha256=gAvrgpccRTCRWqP-_NtcXyoN3hNQOo6azAllaG8QNBk,7765
cvxpy/tests/test_quadratic.py,sha256=53gZK2dVwBS9IuV7RdcgkpsU9hiYOm8br0HfgWaQ0NU,7086
cvxpy/tests/test_quantum_rel_entr.py,sha256=WbFdrn67UZ7_KdmepneUfV1Ep-JPa1Y7PPfQJrUH-t0,6207
cvxpy/tests/test_scalarize.py,sha256=aHTLHsIPhc0GP3_i9LFaRFCK7qGocDv-mZ0UaUq8BSY,5931
cvxpy/tests/test_semidefinite_vars.py,sha256=Kx4NzPiefOcrxAE61GOuONK2pzyYFF67cCjQNE80hZw,3230
cvxpy/tests/test_shape.py,sha256=7B4uk5vqoMwB4gvFH14G_f8aY7ic7Qatx9JVtJ03WGA,2114
cvxpy/tests/test_sign.py,sha256=walrWPnnwxf8HKCSwWwyJGMxdZ9k-xfsVuqsrSZ4djE,2694
cvxpy/tests/test_suppfunc.py,sha256=KK8Q7kDia42KICs-tAEAJd6ypo8KouJufGUC0N9DSbE,7817
cvxpy/tests/test_valinvec2mixedint.py,sha256=C0Zzu2afS4HYUsg8exunRfWMlxIAaHfFxGzfwh1mgN8,14232
cvxpy/tests/test_versioning.py,sha256=INb2hrJvHJJwuk_e5zwBzIWM4n3JKsZo7zWELaeVo-U,1901
cvxpy/tests/test_von_neumann_entr.py,sha256=_RyuxZjVZpVwVcvI98XELaW4BGbeaA8tjvXWhhkbsiI,10184
cvxpy/transforms/__init__.py,sha256=z-0zOWyYpDnSLBel98iswgDsa9eSmst-mqU-xrjy8ok,996
cvxpy/transforms/__pycache__/__init__.cpython-313.pyc,,
cvxpy/transforms/__pycache__/indicator.cpython-313.pyc,,
cvxpy/transforms/__pycache__/linearize.cpython-313.pyc,,
cvxpy/transforms/__pycache__/partial_optimize.cpython-313.pyc,,
cvxpy/transforms/__pycache__/scalarize.cpython-313.pyc,,
cvxpy/transforms/__pycache__/suppfunc.cpython-313.pyc,,
cvxpy/transforms/indicator.py,sha256=b0uZgMrgSGFMdU5afL2AytOfU3ikJQYFZNaxPHh89t0,3825
cvxpy/transforms/linearize.py,sha256=_oRsqicwkT60hNoxY8-zFSzOieNhoXcUshBHENsK5oE,2110
cvxpy/transforms/partial_optimize.py,sha256=dRkAGmymk_zvIPDyw7alWOGsQMZUJ-X4ljh6Gw3_GHM,10918
cvxpy/transforms/scalarize.py,sha256=h4Ziauy0fPAaqMV47MItVu1FVjCPMfGyAJxuIAeZRsM,5056
cvxpy/transforms/suppfunc.py,sha256=IIl4i8JAPfEl-JutoXOG6n5TeFxQtTA9tpAx_q0fXPQ,6761
cvxpy/utilities/__init__.py,sha256=GroXjJhegOrRU7N8FjNTuRRy0vCaPlWXDLuDzLXNAso,629
cvxpy/utilities/__pycache__/__init__.cpython-313.pyc,,
cvxpy/utilities/__pycache__/canonical.cpython-313.pyc,,
cvxpy/utilities/__pycache__/coeff_extractor.cpython-313.pyc,,
cvxpy/utilities/__pycache__/coo_array_compat.cpython-313.pyc,,
cvxpy/utilities/__pycache__/cvxpy_upgrade.cpython-313.pyc,,
cvxpy/utilities/__pycache__/debug_tools.cpython-313.pyc,,
cvxpy/utilities/__pycache__/deterministic.cpython-313.pyc,,
cvxpy/utilities/__pycache__/grad.cpython-313.pyc,,
cvxpy/utilities/__pycache__/key_utils.cpython-313.pyc,,
cvxpy/utilities/__pycache__/linalg.cpython-313.pyc,,
cvxpy/utilities/__pycache__/performance_utils.cpython-313.pyc,,
cvxpy/utilities/__pycache__/perspective_utils.cpython-313.pyc,,
cvxpy/utilities/__pycache__/power_tools.cpython-313.pyc,,
cvxpy/utilities/__pycache__/replace_quad_forms.cpython-313.pyc,,
cvxpy/utilities/__pycache__/scopes.cpython-313.pyc,,
cvxpy/utilities/__pycache__/shape.cpython-313.pyc,,
cvxpy/utilities/__pycache__/sign.cpython-313.pyc,,
cvxpy/utilities/__pycache__/versioning.cpython-313.pyc,,
cvxpy/utilities/canonical.py,sha256=PH0sUjL7N15bexWI2MYqGp1DwS3YSdZWl7j5y7sUIc8,6797
cvxpy/utilities/coeff_extractor.py,sha256=xzqsBU48iCq0FOUMYc9BxcTGt4Fx6hBsmU3GeEnCLGY,13651
cvxpy/utilities/coo_array_compat.py,sha256=LKhAlkRmjzDe-FARWP5hgVK0b2fhIc-TIHbj_x5aBZY,770
cvxpy/utilities/cpp/__init__.py,sha256=AbpHGcgLb-kRsJGnwFEktk7uzpZOCcBY74-YBdrKVGs,1
cvxpy/utilities/cpp/__pycache__/__init__.cpython-313.pyc,,
cvxpy/utilities/cpp/sparsecholesky/__init__.py,sha256=vPyhC-GXvTjZ_Pxypw2bJAobR4lmzkgArxyMbfn7-LA,36
cvxpy/utilities/cpp/sparsecholesky/__pycache__/__init__.cpython-313.pyc,,
cvxpy/utilities/cvxpy_upgrade.py,sha256=HEyylxvSg8PmyG9hvg7sjzMX5e_jpPMPL3UtjhcR4vI,2968
cvxpy/utilities/debug_tools.py,sha256=iZrkVN6WoR5JTM2R93Qr1ODVaY-8x6hPYgSEaq3CDUQ,3264
cvxpy/utilities/deterministic.py,sha256=v7_284sDnHhYz_xINW52rJteKHqCPYz9l6O1vlk63G8,245
cvxpy/utilities/grad.py,sha256=022jaNExKMUHZCttWRKAHrgZXEtD8tKHnt1r0bDBNzg,1424
cvxpy/utilities/key_utils.py,sha256=vQhsAKmNgYhyfjOFvJm649QpZfwvO9gEGY6A0HsAon0,6835
cvxpy/utilities/linalg.py,sha256=CLEqTs670KDS48Y-yLKk8jlhCNv8Yhtrw3K-qg73dPE,8928
cvxpy/utilities/performance_utils.py,sha256=QcQ9CQ3cqxQ16RiQ2NDCTO4nBeCJ73lIFwpCju0Px8g,2437
cvxpy/utilities/perspective_utils.py,sha256=0IZ-ROaWRZzvV2PTyNFXFP2uxBCJWCrjwGqtMKy2fp4,2189
cvxpy/utilities/power_tools.py,sha256=5WJ1r3Toq03vz1e1lKztyBsxRCOS4IllOmbQkFGr9so,19585
cvxpy/utilities/replace_quad_forms.py,sha256=MwkwObTI60aHdfY4qw8AXVbodM6SQ7N-7vZyE0-nUaA,1552
cvxpy/utilities/scopes.py,sha256=ifgxUU5GpDn_zpcV4FrE1b7T_Um5pGi12V5uQzmgIpg,1399
cvxpy/utilities/shape.py,sha256=v2wU7tWirIQo2FgQA_SS8vXvT9RrZmgIXKReDR2c8SE,4486
cvxpy/utilities/sign.py,sha256=r5vs1Y1FXCpsurHtrrx3m7kKX8qtbwMpBV9jPbiBmyo,1968
cvxpy/utilities/versioning.py,sha256=xG6R0xIbWR0Wp2a7Q1N0XFNWGbYhIOOSzZNIuZrJhZk,1825
cvxpy/version.py,sha256=7QK6R_Mx22rxIpCnATnmIjuUOMGWGwcSvbACaXowUJI,213
setup/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
setup/__pycache__/__init__.cpython-313.pyc,,
setup/__pycache__/build_meta.cpython-313.pyc,,
setup/__pycache__/extensions.cpython-313.pyc,,
setup/__pycache__/versioning.cpython-313.pyc,,
setup/build_meta.py,sha256=XuMneM-wuBtoOxQS9Oo2wxJSMo5Dk17cwmbHEq9oJ8k,164
setup/extensions.py,sha256=05-jUIuu8eGP15LidBfxZW_1lNIxNczqqhrWnzDPlRk,1810
setup/versioning.py,sha256=X56mZTpgxjhTFEBnY1MP_oFnonALdUpJ6UPk4spF54Q,4833
