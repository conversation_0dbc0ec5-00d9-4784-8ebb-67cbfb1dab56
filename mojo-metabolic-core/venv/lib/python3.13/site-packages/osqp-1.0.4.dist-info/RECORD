osqp-1.0.4.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
osqp-1.0.4.dist-info/METADATA,sha256=POTle0Eb4wRV8K3AZjiQNw29q7FuAwotzmgBU5kBDcI,2114
osqp-1.0.4.dist-info/RECORD,,
osqp-1.0.4.dist-info/WHEEL,sha256=DnAwpGG1S4u0EAbNhx5jb1HGFeGgk34cfw23WmZPsVg,141
osqp-1.0.4.dist-info/licenses/LICENSE,sha256=tAkwu8-AdEyGxGoSvJ2gVmQdcicWw3j1ZZueVV74M-E,11357
osqp/__init__.py,sha256=qRhobFk0fhAcMatRIjpzQc-TKFy_UmxuobnrDzFHpDc,341
osqp/__pycache__/__init__.cpython-313.pyc,,
osqp/__pycache__/_version.cpython-313.pyc,,
osqp/__pycache__/builtin.cpython-313.pyc,,
osqp/__pycache__/cuda.cpython-313.pyc,,
osqp/__pycache__/interface.cpython-313.pyc,,
osqp/__pycache__/mkl.cpython-313.pyc,,
osqp/_version.py,sha256=rXTOeD0YpRo_kJ2LqUiMnTKEFf43sO_PBvJHDh0SQUA,511
osqp/builtin.py,sha256=dwl0IKtYVBbeWafzX0hoef3L28fm_LTPUuMn1tdn-Ss,174
osqp/codegen/__init__.py,sha256=GylwfHzO9h44HCd1twU2XNowwIkzfP-KbdSXzMYXl8Q,72
osqp/codegen/__pycache__/__init__.cpython-313.pyc,,
osqp/codegen/codegen_src/Makefile,sha256=_1L14tGGFbnmlrmgeYuzQbRVeGpR1Z-TbMwvfJmcolQ,1024
osqp/codegen/codegen_src/inc/private/algebra_impl.h,sha256=mVZAds-z_HrVPJdUc_FpJl4AIViivmcP_CCAzi76MxA,997
osqp/codegen/codegen_src/inc/private/algebra_matrix.h,sha256=sbTsZ5yXDJi_kDV6lwvTxD7LfYRyKv4I9AdlLsiXXM0,4523
osqp/codegen/codegen_src/inc/private/algebra_vector.h,sha256=3S_rp7Q3iMC7_Zbv6eAHk1pzTXAQ8Wcs68eD-5z1fhA,10745
osqp/codegen/codegen_src/inc/private/auxil.h,sha256=Jv7ybvyEK92tJoDfTRY2EagOuBRqlhDA7vVaJ6OZTXI,5072
osqp/codegen/codegen_src/inc/private/csc_math.h,sha256=WdyWV3hMh_l-e1sDM5TsVgXnLx8tDaIeb_3-7zEIG04,2977
osqp/codegen/codegen_src/inc/private/csc_utils.h,sha256=uApckEeG81OOS0v0KvxQZE4WmAJKcCZx2Cy61R-9Q24,6414
osqp/codegen/codegen_src/inc/private/error.h,sha256=COaX62UyKIz699PFeOEHFkg0zbb06NOGf2gI1W5TXn0,1292
osqp/codegen/codegen_src/inc/private/glob_opts.h,sha256=GyGMrw03SYBAGWgujx-7QCVjogN8f7wRUUtMctO3lsc,1676
osqp/codegen/codegen_src/inc/private/kkt.h,sha256=jFCgumwangd-bp2U9SLZZbdW9tSyKbCsiWAw8fnF5IU,3455
osqp/codegen/codegen_src/inc/private/lin_alg.h,sha256=_YnqbMS_-8XRy7FEJk-upBObPed2NPnBXjxhUtWaFX4,2746
osqp/codegen/codegen_src/inc/private/printing.h,sha256=uv7VuDScshljbuCb_GpmtRGe8aP3HdNIym9R62TVuk4,1349
osqp/codegen/codegen_src/inc/private/profilers.h,sha256=e76F81qN_06Dyecfv3Iah5DegkYj_Cgxyc4FKwxPc4g,3861
osqp/codegen/codegen_src/inc/private/qdldl.h,sha256=_g0D1lF-iiYzmuncv5eOPwW4gkZyqzQcJ3mOD_QSz2U,7622
osqp/codegen/codegen_src/inc/private/qdldl_interface.h,sha256=NAb-8HExXmsRc4Mz6MI_qSmRifd_sMlDBtUFquYDQ5Y,7004
osqp/codegen/codegen_src/inc/private/qdldl_types.h,sha256=esOiNaAgFAKhjUHpXKaxuDASSB02QH1oR7vIoN_1GWs,802
osqp/codegen/codegen_src/inc/private/qdldl_version.h,sha256=FE24-Ys-X1IUreJ7fRSRwZZiD_nkHUr2oKS0Q_hyyfQ,1172
osqp/codegen/codegen_src/inc/private/scaling.h,sha256=cecVg4OOBdEmseBGTvcWmsLRnhDCRguZ5-F1Ju62me4,1141
osqp/codegen/codegen_src/inc/private/timing.h,sha256=KrvvzTqXOg38YRzqba9i_BR11L5CBWJj9hVLePOPVG4,660
osqp/codegen/codegen_src/inc/private/types.h,sha256=jXbCz9ow6StKUxwL5TOE6F9EamyZAfGXkQnmDXwHnew,8850
osqp/codegen/codegen_src/inc/private/util.h,sha256=kHWroA19DsGW30Z_SdQu_zx5HgZFldc196i6rqHUdSc,2884
osqp/codegen/codegen_src/inc/private/version.h,sha256=4S5_6w3bJsW2cLsYTJyq-HC8cQr7VhxcRUBD11YdKu0,182
osqp/codegen/codegen_src/inc/public/osqp.h,sha256=BaJajxy3z-w8uugzE3FMU_71i4zusK3ywiiE51wauDQ,236
osqp/codegen/codegen_src/inc/public/osqp_api_constants.h,sha256=sQMYxChNCOnErRzQeLt9ChUrCOB4eKGwmLsqc6ZSpNk,7354
osqp/codegen/codegen_src/inc/public/osqp_api_functions.h,sha256=dIya5fMf9Xa7q9m1fPmcvyH1q1BYf8uhc4iW_mcJLtc,17081
osqp/codegen/codegen_src/inc/public/osqp_api_types.h,sha256=xlV3ucloKqYR8LW-7QCdUvG0XL3c-x1Nr1N5hgrHvps,7933
osqp/codegen/codegen_src/inc/public/osqp_export_define.h,sha256=o5GYe6Yt0M91P3JBwImyt4aFy5uSIQpdFL83j5zI4PM,713
osqp/codegen/codegen_src/src/algebra_libs.c,sha256=__wGd2O_YTU437GlKO5IC68N0TVFypgNbQIR8zv2Y6w,2829
osqp/codegen/codegen_src/src/auxil.c,sha256=toLj-6g9wqun050IwaDzBZ1TjXSBTkZM_776ajXMVZE,36984
osqp/codegen/codegen_src/src/csc_math.c,sha256=eoHvE55bPfWhThQs1_F1cMQj9-dH3v6bA99W9p00f4Q,9226
osqp/codegen/codegen_src/src/csc_utils.c,sha256=A4nxlR8vU2yYq64eKBM0zfV31pEUtcKpCoarPTTvanE,17524
osqp/codegen/codegen_src/src/error.c,sha256=zfA0xgBO7PzehCIhY3siC1pCuCTEU0SnPqpJJPy7Fxg,1513
osqp/codegen/codegen_src/src/kkt.c,sha256=h70NNQPaTfO5yLQCJ-4O4d7nSdd6St_EhPGJ4IpQ4zg,12727
osqp/codegen/codegen_src/src/matrix.c,sha256=neLDPK5IIrTz-d2DVjl-6gd4Ho7-uGeaSTnFJ2ZFdEo,6691
osqp/codegen/codegen_src/src/osqp_api.c,sha256=9ZIfeUFYEzX0RiKQLtIbm2gT22nRnGh5SE0IpQm40UM,52373
osqp/codegen/codegen_src/src/qdldl.c,sha256=7LwuExrFt2uK_l5eokS7lmsb7cEK4SsIfTnOOpgoRKc,9432
osqp/codegen/codegen_src/src/qdldl_interface.c,sha256=QZy_P4Th1a49LSPfLSxYANDmV102Y_o7o8wAqc0aEao,27392
osqp/codegen/codegen_src/src/scaling.c,sha256=ZBU8XhBt4QraZ6J3vQLqT8sZ92S8SuYYJrEz_lrfrgI,6052
osqp/codegen/codegen_src/src/util.c,sha256=kRVEO-O4yuPWX2W2Uii9PReGvgkeUsVRnL_qOTEEzOM,13336
osqp/codegen/codegen_src/src/vector.c,sha256=gwoXhaF08VxTItf8DbcBI9Ah0vZFzEnE1GnDstiRK2M,22196
osqp/codegen/pywrapper/CMakeLists.txt.jinja,sha256=rY8tTJkk9w4o5Z9-YcqL-BGUdacZ9JDlanqGMoEV2oE,804
osqp/codegen/pywrapper/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
osqp/codegen/pywrapper/__pycache__/__init__.cpython-313.pyc,,
osqp/codegen/pywrapper/bindings.cpp.jinja,sha256=lbSg7YovMo33NLugaiIEFv1wnhzSmNkLWuF9g9lqAYg,2997
osqp/codegen/pywrapper/setup.py.jinja,sha256=d06g1rCqTRPqcoUqVwMLpCVIPRD4hwAiFPwnQmT7PsA,2194
osqp/cuda.py,sha256=4il2wfrhkEfNfNa9X-_LCMT0gCADTztYRqdxH4D1v6s,171
osqp/ext_builtin.cpython-313-darwin.so,sha256=5NwNrLCG1utVy1Day8mmMICcB6S46fEpJPi0usqIgBU,396800
osqp/interface.py,sha256=ncl5LqLzN0NbHb69OiGA8oJquwfNa_g8UF8-DW3n-n4,21081
osqp/mkl.py,sha256=85j6rEWgCZ7mqtmqtf_68LS9Y3vq8nLqWHznSE1VN_Y,170
osqp/nn/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
osqp/nn/__pycache__/__init__.cpython-313.pyc,,
osqp/nn/__pycache__/torch.cpython-313.pyc,,
osqp/nn/torch.py,sha256=frtR8jtQP6RSIh3wYYJY5jg38kxK_JIACnj9NQ2bC4I,10388
osqp/tests/__pycache__/basic_test.cpython-313.pyc,,
osqp/tests/__pycache__/codegen_matrices_test.cpython-313.pyc,,
osqp/tests/__pycache__/codegen_vectors_test.cpython-313.pyc,,
osqp/tests/__pycache__/conftest.cpython-313.pyc,,
osqp/tests/__pycache__/derivative_test.cpython-313.pyc,,
osqp/tests/__pycache__/dual_infeasibility_test.cpython-313.pyc,,
osqp/tests/__pycache__/feasibility_test.cpython-313.pyc,,
osqp/tests/__pycache__/multithread_test.cpython-313.pyc,,
osqp/tests/__pycache__/nn_test.cpython-313.pyc,,
osqp/tests/__pycache__/non_convex_test.cpython-313.pyc,,
osqp/tests/__pycache__/polishing_test.cpython-313.pyc,,
osqp/tests/__pycache__/primal_infeasibility_test.cpython-313.pyc,,
osqp/tests/__pycache__/unconstrained_test.cpython-313.pyc,,
osqp/tests/__pycache__/update_matrices_test.cpython-313.pyc,,
osqp/tests/__pycache__/utils.cpython-313.pyc,,
osqp/tests/__pycache__/warm_start_test.cpython-313.pyc,,
osqp/tests/basic_test.py,sha256=sOmuUXwMEABX3M8-cQJC5AiyB3lOq3CKRK85TSbqofM,5100
osqp/tests/codegen_matrices_test.py,sha256=7lksjogN9mXzsjucINuQM57Ea5ChQHf2ka-dJEZhX-4,7570
osqp/tests/codegen_vectors_test.py,sha256=dwnq6lruD-9GAAaQwOqbZbD1Rc4GONhcm2GjnqTXlZM,4271
osqp/tests/conftest.py,sha256=Pc8znPn896gOPv149SaM2Ui1mbUQLxiuYbHJjFuRKPo,1116
osqp/tests/derivative_test.py,sha256=HWQgbcAViTQcIQ-PVjXdiCHrJBzxqhJ1QEpGCuIwxsU,21292
osqp/tests/dual_infeasibility_test.py,sha256=YACT9N-WTXu4zu_HuVu-XTVEdBKDVcD1Ezx8yWWo8Gg,3107
osqp/tests/feasibility_test.py,sha256=21Bh757pGKAKGMIepZIkno5s2ppWB_LlNwGQ2cvwtQw,1612
osqp/tests/multithread_test.py,sha256=FUq722EDj-SB_IRHYp9qnTC0gSRNf-G5wyjZpRb452E,1516
osqp/tests/nn_test.py,sha256=jl7DrTvy6SEilYDbucu9FxpxqfyfGrX3Ed2oWXaIWBc,6909
osqp/tests/non_convex_test.py,sha256=MPseuuVz3Igm1hH0S7Vn2-odvNmHDZBISaD7uQCWKhA,1661
osqp/tests/polishing_test.py,sha256=WmUNk-fGS9ve9kAGVI939JXACm_wc0JuR-raRi_wDy4,3161
osqp/tests/primal_infeasibility_test.py,sha256=gyyg-2KkTYrTvsmdr5yCuFyGSq8CiKANZ18S_HkUV4g,2412
osqp/tests/solutions/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
osqp/tests/solutions/__pycache__/__init__.cpython-313.pyc,,
osqp/tests/solutions/test_basic_QP.npz,sha256=NLw2dlO_fVJnXSMhJw-bkbNKR9eiIJnL4b3PPC8V5UY,808
osqp/tests/solutions/test_dual_infeasibility.npz,sha256=u2njUZLvi0KhlD71VA_Pr0McmqCQ0s05xhXayIQK43Q,536
osqp/tests/solutions/test_feasibility_problem.npz,sha256=RFtIiZhSHdjxs4NKWvqmTue3_6sH0DortC4Qix-oSQY,1232
osqp/tests/solutions/test_polish_random.npz,sha256=iyDYTYWTplmdKwag5jRHi_i2_9aMJYIrt_rrtK45dwE,1392
osqp/tests/solutions/test_polish_simple.npz,sha256=3Ru4HPlabWkR1l7cuhejnnEZ2byoDlpfZMl6WXUEYB4,808
osqp/tests/solutions/test_polish_unconstrained.npz,sha256=l5vFjjIXttxWW0EaX3Zq3mhisAr1Cpj722gNAC95Vrw,992
osqp/tests/solutions/test_primal_infeasibility.npz,sha256=4bKj1kvEX100SvVdSvo7CE5DPAaZnR_atLNvtE3f2po,343
osqp/tests/solutions/test_solve.npz,sha256=hM9JkjiuVuwczV7Co9Tbrg3DZJb1vFHWMm50v1nuxO8,856
osqp/tests/solutions/test_unconstrained_problem.npz,sha256=l5vFjjIXttxWW0EaX3Zq3mhisAr1Cpj722gNAC95Vrw,992
osqp/tests/solutions/test_update_A.npz,sha256=PSsEczGv8I-F-TYxudLhS9xSxV1ildoGUfpGogAmioI,856
osqp/tests/solutions/test_update_A_allind.npz,sha256=PSsEczGv8I-F-TYxudLhS9xSxV1ildoGUfpGogAmioI,856
osqp/tests/solutions/test_update_P.npz,sha256=2_ZmkMUjn3rdh8tb19-aB4cAZevcG4Cf32Z9QGkrKTg,856
osqp/tests/solutions/test_update_P_A_allind.npz,sha256=-u2-rNk4NXOULP2No2kO4Pir56RJOs3-03JJ0v8Qsec,856
osqp/tests/solutions/test_update_P_A_indA.npz,sha256=-u2-rNk4NXOULP2No2kO4Pir56RJOs3-03JJ0v8Qsec,856
osqp/tests/solutions/test_update_P_A_indP.npz,sha256=-u2-rNk4NXOULP2No2kO4Pir56RJOs3-03JJ0v8Qsec,856
osqp/tests/solutions/test_update_P_A_indP_indA.npz,sha256=-u2-rNk4NXOULP2No2kO4Pir56RJOs3-03JJ0v8Qsec,856
osqp/tests/solutions/test_update_P_allind.npz,sha256=2_ZmkMUjn3rdh8tb19-aB4cAZevcG4Cf32Z9QGkrKTg,856
osqp/tests/solutions/test_update_bounds.npz,sha256=Tw4ibEcj8X701-2WHpXYgOl8QCwOOfHYPDoE6ZkHQ7E,808
osqp/tests/solutions/test_update_l.npz,sha256=YdnLVdWcb77FRWOYC50I4s_EE8cD0KyL3kfoklchbRs,808
osqp/tests/solutions/test_update_q.npz,sha256=dIYbZ6uRAcFWkD1-qqvLCm928IjezoIWqRtOEOqsjYI,808
osqp/tests/solutions/test_update_u.npz,sha256=XXcF_Y3aKdW1ESEGh42vz8qTJTzb0uCAgLfLx2TVvgk,808
osqp/tests/unconstrained_test.py,sha256=hu6U8YSjiRuaPkM0CDoIyiJ8zHXsKJofab07NOzVS3w,1247
osqp/tests/update_matrices_test.py,sha256=ZOiPrWDjGhp_zkrauFQ0CppkiIojJqn2Jm_h0dvNJrk,6021
osqp/tests/utils.py,sha256=teDUJ28vcJ4Qb2tjB4olAksC6vSyjzMF0IWD-b65Xio,243
osqp/tests/warm_start_test.py,sha256=KJN-KRDJ5Kii8DiweHfzZJM9ZUnzKR78JktvZqHvAFY,1601
