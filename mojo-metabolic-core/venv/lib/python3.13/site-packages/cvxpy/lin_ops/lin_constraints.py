"""
Copyright 2013 <PERSON>

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

THIS FILE IS DEPRECATED AND MAY BE REMOVED WITHOUT WARNING!
DO NOT CALL THESE FUNCTIONS IN YOUR CODE!
"""
from collections import namedtuple

# Constraints with linear expressions.
# constr_id is used to recover dual variables.
# expr == 0
LinEqConstr = namedtuple("LinEqConstr", ["expr",
                                         "constr_id",
                                         "shape"])
# expr <= 0
LinLeqConstr = namedtuple("LinLeqConstr", ["expr",
                                           "constr_id",
                                           "shape"])
