"""
Copyright 2018 <PERSON><PERSON><PERSON>

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
"""

from cvxpy.atoms.max import max
from cvxpy.reductions.eliminate_pwl.canonicalizers.max_canon import max_canon


def min_canon(expr, args):
    axis = expr.axis
    keepdims = expr.keepdims
    del expr
    assert len(args) == 1
    tmp = max(-args[0], axis=axis, keepdims=keepdims)
    canon, constr = max_canon(tmp, tmp.args)
    return -canon, constr
