# Python dependencies for Mojo metabolic core
# These are used via Python interop for scientific computing

# Core scientific computing
numpy>=1.21.0
scipy>=1.7.0

# SBML parsing (optional, fallback to simple parser if not available)
python-libsbml>=5.19.0

# Linear programming solvers
cvxpy>=1.2.0

# Additional optimization solvers (optional)
# glpk  # Install via system package manager
# gurobi  # Commercial solver (requires license)
# cplex  # Commercial solver (requires license)

# Utilities
setuptools>=60.0.0
wheel>=0.37.0
