#!/usr/bin/env python3
"""
Python-based FBA solver for the Mojo Metabolic Core
This serves as a fallback implementation while Mojo syntax is being refined.
"""

import json
import sys
import os
import time
import numpy as np
from typing import Dict, List, Any, Optional

try:
    from scipy.optimize import linprog
    SCIPY_AVAILABLE = True
except ImportError:
    SCIPY_AVAILABLE = False
    print("Warning: SciPy not available, using mock solver")


class MetabolicModel:
    """Simple metabolic model representation."""
    
    def __init__(self, model_id: str, name: str = ""):
        self.id = model_id
        self.name = name or model_id
        self.reactions = {}
        self.metabolites = {}
        self.reaction_ids = []
        self.metabolite_ids = []
        self.stoichiometric_matrix = None
        self.bounds_lower = []
        self.bounds_upper = []
        self.objective = []
    
    def add_reaction(self, reaction_id: str, name: str = "", lower_bound: float = -1000.0, 
                    upper_bound: float = 1000.0, objective_coeff: float = 0.0):
        """Add a reaction to the model."""
        self.reactions[reaction_id] = {
            'id': reaction_id,
            'name': name or reaction_id,
            'lower_bound': lower_bound,
            'upper_bound': upper_bound,
            'objective_coefficient': objective_coeff,
            'stoichiometry': {}
        }
        self.reaction_ids.append(reaction_id)
        self.bounds_lower.append(lower_bound)
        self.bounds_upper.append(upper_bound)
        self.objective.append(objective_coeff)
    
    def add_metabolite(self, metabolite_id: str, name: str = "", compartment: str = "c"):
        """Add a metabolite to the model."""
        self.metabolites[metabolite_id] = {
            'id': metabolite_id,
            'name': name or metabolite_id,
            'compartment': compartment
        }
        self.metabolite_ids.append(metabolite_id)
    
    def add_stoichiometry(self, reaction_id: str, metabolite_id: str, coefficient: float):
        """Add stoichiometric coefficient."""
        if reaction_id in self.reactions:
            self.reactions[reaction_id]['stoichiometry'][metabolite_id] = coefficient
    
    def build_stoichiometric_matrix(self):
        """Build the stoichiometric matrix."""
        n_metabolites = len(self.metabolite_ids)
        n_reactions = len(self.reaction_ids)
        
        self.stoichiometric_matrix = np.zeros((n_metabolites, n_reactions))
        
        for j, reaction_id in enumerate(self.reaction_ids):
            reaction = self.reactions[reaction_id]
            for metabolite_id, coeff in reaction['stoichiometry'].items():
                if metabolite_id in self.metabolite_ids:
                    i = self.metabolite_ids.index(metabolite_id)
                    self.stoichiometric_matrix[i, j] = coeff
    
    def set_objective(self, reaction_id: str, coefficient: float = 1.0):
        """Set objective function."""
        # Reset all objectives
        self.objective = [0.0] * len(self.reaction_ids)
        
        # Set specific objective
        if reaction_id in self.reaction_ids:
            idx = self.reaction_ids.index(reaction_id)
            self.objective[idx] = coefficient
            self.reactions[reaction_id]['objective_coefficient'] = coefficient


def create_demo_model() -> MetabolicModel:
    """Create a simple working demo model."""
    model = MetabolicModel("demo_simple", "Simple Demo Model")

    # Add metabolites
    metabolites = [
        ("glc_e", "Glucose external", "e"),
        ("glc_c", "Glucose cytoplasm", "c"),
        ("biomass", "Biomass", "c"),
    ]

    for met_id, name, compartment in metabolites:
        model.add_metabolite(met_id, name, compartment)

    # Add reactions
    reactions = [
        ("EX_glc", "Glucose exchange", -10.0, 0.0, 0.0),  # Only uptake
        ("GLCt", "Glucose transport", 0.0, 1000.0, 0.0),
        ("BIOMASS", "Biomass production", 0.0, 10.0, 1.0),  # Limited upper bound
    ]

    for rxn_id, name, lb, ub, obj in reactions:
        model.add_reaction(rxn_id, name, lb, ub, obj)

    # Add stoichiometry
    stoichiometry = [
        ("EX_glc", "glc_e", -1.0),
        ("GLCt", "glc_e", -1.0),
        ("GLCt", "glc_c", 1.0),
        ("BIOMASS", "glc_c", -1.0),
        ("BIOMASS", "biomass", 1.0),
    ]

    for rxn_id, met_id, coeff in stoichiometry:
        model.add_stoichiometry(rxn_id, met_id, coeff)

    model.build_stoichiometric_matrix()
    return model


def solve_fba(model: MetabolicModel, objective_reaction: str = "") -> Dict[str, Any]:
    """Solve FBA using linear programming."""
    start_time = time.time()
    
    if not SCIPY_AVAILABLE:
        # Mock solution for simple model
        result = {
            "objective_value": 5.0,
            "flux_values": {
                "BIOMASS": 5.0,
                "EX_glc": -5.0,
                "GLCt": 5.0
            },
            "status": "optimal",
            "solver_time": time.time() - start_time
        }
        return result
    
    # Set objective
    if objective_reaction and objective_reaction in model.reaction_ids:
        model.set_objective(objective_reaction, 1.0)
    
    # Prepare for scipy.optimize.linprog
    n_reactions = len(model.reaction_ids)
    n_metabolites = len(model.metabolite_ids)
    
    # Objective vector (minimize negative for maximization)
    c = [-obj for obj in model.objective]
    
    # Equality constraints (Sv = 0)
    A_eq = model.stoichiometric_matrix
    b_eq = np.zeros(n_metabolites)
    
    # Bounds
    bounds = [(model.bounds_lower[i], model.bounds_upper[i]) for i in range(n_reactions)]
    
    # Solve
    try:
        result = linprog(c, A_eq=A_eq, b_eq=b_eq, bounds=bounds, method='highs')
        
        if result.success:
            flux_values = {}
            for i, reaction_id in enumerate(model.reaction_ids):
                flux_values[reaction_id] = float(result.x[i])
            
            obj_value = -result.fun if result.fun is not None else 0.0
            # Handle negative zero
            if obj_value == -0.0:
                obj_value = 0.0
            return {
                "objective_value": obj_value,  # Convert back from minimization
                "flux_values": flux_values,
                "status": "optimal",
                "solver_time": time.time() - start_time
            }
        else:
            return {
                "objective_value": 0.0,
                "flux_values": {},
                "status": "infeasible",
                "solver_time": time.time() - start_time
            }
    except Exception as e:
        return {
            "objective_value": 0.0,
            "flux_values": {},
            "status": f"error: {str(e)}",
            "solver_time": time.time() - start_time
        }


def process_fba_request(request_file: str, response_file: str) -> bool:
    """Process FBA request from file."""
    try:
        # Read request
        with open(request_file, 'r') as f:
            request_data = json.load(f)
        
        print(f"Processing FBA request: {request_data.get('request_id', 'unknown')}")
        
        # For now, use demo model (in real implementation, load from model_path)
        model = create_demo_model()
        
        # Get objective
        objective = request_data.get('objective', 'BIOMASS')
        
        # Apply constraints if provided
        constraints = request_data.get('constraints', {})
        for reaction_id, value in constraints.items():
            if reaction_id in model.reaction_ids:
                idx = model.reaction_ids.index(reaction_id)
                model.bounds_lower[idx] = value
                model.bounds_upper[idx] = value
        
        # Solve FBA
        fba_result = solve_fba(model, objective)
        
        # Prepare response
        response = {
            "success": True,
            "error": None,
            "result": fba_result
        }
        
        # Write response
        with open(response_file, 'w') as f:
            json.dump(response, f, indent=2)
        
        print(f"FBA completed: {fba_result['status']}, objective: {fba_result['objective_value']}")
        return True
        
    except Exception as e:
        # Write error response
        error_response = {
            "success": False,
            "error": str(e),
            "result": None
        }
        
        with open(response_file, 'w') as f:
            json.dump(error_response, f, indent=2)
        
        print(f"Error processing FBA request: {e}")
        return False


def main():
    """Main entry point."""
    if len(sys.argv) < 2:
        print("Usage:")
        print("  python3 python_fba_solver.py test")
        print("  python3 python_fba_solver.py <request_file> <response_file>")
        sys.exit(1)
    
    if sys.argv[1] == "test":
        print("Running FBA test...")
        model = create_demo_model()
        print(f"Model has {len(model.reaction_ids)} reactions and {len(model.metabolite_ids)} metabolites")
        print(f"Objective coefficients: {model.objective}")
        result = solve_fba(model, "BIOMASS")
        print(f"Test result: {result['status']}")
        print(f"Objective value: {result['objective_value']}")
        print(f"Flux values: {result['flux_values']}")
        print("Test completed successfully!")
        return
    
    if len(sys.argv) < 3:
        print("Error: Both request_file and response_file required")
        sys.exit(1)
    
    request_file = sys.argv[1]
    response_file = sys.argv[2]
    
    success = process_fba_request(request_file, response_file)
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
