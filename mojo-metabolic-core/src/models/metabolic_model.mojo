from collections import Dict, List
from python import Python
from memory import memset_zero, memcpy
from algorithm import vectorize
import math

# Core data structures for metabolic models

struct Reaction:
    """Represents a metabolic reaction with stoichiometry and bounds."""
    var id: String
    var name: String
    var lower_bound: Float64
    var upper_bound: Float64
    var objective_coefficient: Float64
    var stoichiometry: Dict[String, Float64]
    
    fn __init__(inout self, id: String, name: String = "", 
                lower_bound: Float64 = -1000.0, upper_bound: Float64 = 1000.0,
                objective_coefficient: Float64 = 0.0):
        self.id = id
        self.name = name if name else id
        self.lower_bound = lower_bound
        self.upper_bound = upper_bound
        self.objective_coefficient = objective_coefficient
        self.stoichiometry = Dict[String, Float64]()
    
    fn add_metabolite(inout self, metabolite_id: String, coefficient: Float64):
        """Add a metabolite with its stoichiometric coefficient."""
        self.stoichiometry[metabolite_id] = coefficient
    
    fn is_reversible(self) -> Bool:
        """Check if reaction is reversible."""
        return self.lower_bound < 0.0
    
    fn is_exchange(self) -> Bool:
        """Check if reaction is an exchange reaction."""
        return self.id.startswith("EX_") or self.id.startswith("DM_")

struct Metabolite:
    """Represents a metabolite in the metabolic network."""
    var id: String
    var name: String
    var formula: String
    var compartment: String
    var charge: Int
    
    fn __init__(inout self, id: String, name: String = "", 
                formula: String = "", compartment: String = "c", charge: Int = 0):
        self.id = id
        self.name = name if name else id
        self.formula = formula
        self.compartment = compartment
        self.charge = charge

struct SparseMatrix:
    """Sparse matrix representation for stoichiometric matrix."""
    var rows: Int
    var cols: Int
    var values: List[Float64]
    var row_indices: List[Int]
    var col_indices: List[Int]
    var nnz: Int  # Number of non-zero elements
    
    fn __init__(inout self, rows: Int, cols: Int):
        self.rows = rows
        self.cols = cols
        self.values = List[Float64]()
        self.row_indices = List[Int]()
        self.col_indices = List[Int]()
        self.nnz = 0
    
    fn add_entry(inout self, row: Int, col: Int, value: Float64):
        """Add a non-zero entry to the sparse matrix."""
        if abs(value) > 1e-12:  # Only store significant values
            self.values.append(value)
            self.row_indices.append(row)
            self.col_indices.append(col)
            self.nnz += 1
    
    fn get_value(self, row: Int, col: Int) -> Float64:
        """Get value at specific position (returns 0.0 if not found)."""
        for i in range(self.nnz):
            if self.row_indices[i] == row and self.col_indices[i] == col:
                return self.values[i]
        return 0.0

struct MetabolicModel:
    """Complete metabolic model representation."""
    var id: String
    var name: String
    var reactions: Dict[String, Reaction]
    var metabolites: Dict[String, Metabolite]
    var stoichiometric_matrix: SparseMatrix
    var reaction_ids: List[String]
    var metabolite_ids: List[String]
    var bounds_lower: List[Float64]
    var bounds_upper: List[Float64]
    var objective: List[Float64]
    
    fn __init__(inout self, id: String, name: String = ""):
        self.id = id
        self.name = name if name else id
        self.reactions = Dict[String, Reaction]()
        self.metabolites = Dict[String, Metabolite]()
        self.reaction_ids = List[String]()
        self.metabolite_ids = List[String]()
        self.bounds_lower = List[Float64]()
        self.bounds_upper = List[Float64]()
        self.objective = List[Float64]()
        self.stoichiometric_matrix = SparseMatrix(0, 0)
    
    fn add_reaction(inout self, reaction: Reaction):
        """Add a reaction to the model."""
        self.reactions[reaction.id] = reaction
        self.reaction_ids.append(reaction.id)
        self.bounds_lower.append(reaction.lower_bound)
        self.bounds_upper.append(reaction.upper_bound)
        self.objective.append(reaction.objective_coefficient)
    
    fn add_metabolite(inout self, metabolite: Metabolite):
        """Add a metabolite to the model."""
        self.metabolites[metabolite.id] = metabolite
        self.metabolite_ids.append(metabolite.id)
    
    fn build_stoichiometric_matrix(inout self):
        """Build the sparse stoichiometric matrix from reactions."""
        let n_metabolites = len(self.metabolite_ids)
        let n_reactions = len(self.reaction_ids)
        
        self.stoichiometric_matrix = SparseMatrix(n_metabolites, n_reactions)
        
        # Create metabolite index mapping
        var metabolite_index = Dict[String, Int]()
        for i in range(n_metabolites):
            metabolite_index[self.metabolite_ids[i]] = i
        
        # Fill matrix with stoichiometric coefficients
        for j in range(n_reactions):
            let reaction_id = self.reaction_ids[j]
            let reaction = self.reactions[reaction_id]
            
            for metabolite_id in reaction.stoichiometry:
                let coefficient = reaction.stoichiometry[metabolite_id]
                if metabolite_id in metabolite_index:
                    let i = metabolite_index[metabolite_id]
                    self.stoichiometric_matrix.add_entry(i, j, coefficient)
    
    fn get_reaction_count(self) -> Int:
        """Get number of reactions in the model."""
        return len(self.reaction_ids)
    
    fn get_metabolite_count(self) -> Int:
        """Get number of metabolites in the model."""
        return len(self.metabolite_ids)
    
    fn get_exchange_reactions(self) -> List[String]:
        """Get list of exchange reaction IDs."""
        var exchange_reactions = List[String]()
        for reaction_id in self.reaction_ids:
            let reaction = self.reactions[reaction_id]
            if reaction.is_exchange():
                exchange_reactions.append(reaction_id)
        return exchange_reactions
    
    fn set_objective(inout self, reaction_id: String, coefficient: Float64 = 1.0):
        """Set objective function for a specific reaction."""
        # Reset all objective coefficients
        for i in range(len(self.objective)):
            self.objective[i] = 0.0
        
        # Set objective for specified reaction
        for i in range(len(self.reaction_ids)):
            if self.reaction_ids[i] == reaction_id:
                self.objective[i] = coefficient
                self.reactions[reaction_id].objective_coefficient = coefficient
                break
    
    fn validate(self) -> Bool:
        """Validate model consistency."""
        # Check if all metabolites in reactions exist
        for reaction_id in self.reaction_ids:
            let reaction = self.reactions[reaction_id]
            for metabolite_id in reaction.stoichiometry:
                if metabolite_id not in self.metabolites:
                    return False
        
        # Check bounds consistency
        for i in range(len(self.bounds_lower)):
            if self.bounds_lower[i] > self.bounds_upper[i]:
                return False
        
        return True

# Utility functions for model manipulation

fn create_biomass_reaction(biomass_metabolites: Dict[String, Float64]) -> Reaction:
    """Create a biomass reaction from metabolite coefficients."""
    var biomass = Reaction("BIOMASS", "Biomass production", 0.0, 1000.0, 1.0)
    
    for metabolite_id in biomass_metabolites:
        let coefficient = biomass_metabolites[metabolite_id]
        biomass.add_metabolite(metabolite_id, coefficient)
    
    return biomass

fn create_exchange_reaction(metabolite_id: String, lower_bound: Float64 = -1000.0) -> Reaction:
    """Create an exchange reaction for a metabolite."""
    let exchange_id = "EX_" + metabolite_id
    var exchange = Reaction(exchange_id, "Exchange for " + metabolite_id, 
                           lower_bound, 1000.0, 0.0)
    exchange.add_metabolite(metabolite_id, -1.0)
    return exchange
