from python import Python
from collections import Dict
from .models.metabolic_model import MetabolicModel
from .parsers.sbml_parser import parse_sbml_file
from .solvers.fba_solver import FBASolver, FBAResult
import json
import sys
import os
import time

struct IPCServer:
    """IPC server for handling requests from Go backend."""
    var solver: FBASolver
    var verbose: Bool
    
    fn __init__(inout self, verbose: Bool = False):
        self.solver = FBASolver("glpk", 1e-9, 10000, verbose)
        self.verbose = verbose
    
    fn process_request(self, request_file: String, response_file: String) -> Bool:
        """Process a single FBA request from file."""
        try:
            if self.verbose:
                print("Processing request from:", request_file)
            
            # Read request
            let request_data = self._read_json_file(request_file)
            
            # Process based on request type
            let result = self._handle_fba_request(request_data)
            
            # Write response
            self._write_json_file(response_file, result)
            
            if self.verbose:
                print("Response written to:", response_file)
            
            return True
            
        except e:
            # Write error response
            let error_response = {
                "success": False,
                "error": str(e),
                "result": None
            }
            self._write_json_file(response_file, error_response)
            return False
    
    fn _handle_fba_request(self, request_data: Dict) -> Dict:
        """Handle FBA request and return result."""
        try:
            # Extract request parameters
            let model_path = str(request_data["model_path"])
            let objective = str(request_data["objective"])
            let constraints = request_data.get("constraints", {})
            let options = request_data.get("options", {})
            
            if self.verbose:
                print("Loading model from:", model_path)
                print("Objective reaction:", objective)
            
            # Load model
            var model = self._load_model(model_path)
            
            # Apply constraints if provided
            if constraints:
                self._apply_constraints(model, constraints)
            
            # Set objective
            if objective:
                model.set_objective(objective, 1.0)
            
            # Solve FBA
            let start_time = time.time()
            let fba_result = self.solver.solve(model, objective)
            let solve_time = time.time() - start_time
            
            if self.verbose:
                print("FBA solved in", solve_time, "seconds")
                print("Status:", fba_result.status)
                print("Objective value:", fba_result.objective_value)
            
            # Prepare response
            let response = {
                "success": True,
                "error": None,
                "result": {
                    "objective_value": fba_result.objective_value,
                    "flux_values": self._dict_to_python(fba_result.flux_values),
                    "status": fba_result.status,
                    "solver_time": solve_time
                }
            }
            
            return response
            
        except e:
            return {
                "success": False,
                "error": str(e),
                "result": None
            }
    
    fn _load_model(self, model_path: String) -> MetabolicModel:
        """Load metabolic model from file."""
        let file_ext = model_path.split(".")[-1].lower()
        
        if file_ext in ["xml", "sbml"]:
            return parse_sbml_file(model_path)
        elif file_ext == "json":
            return self._load_json_model(model_path)
        else:
            raise Error("Unsupported model format: " + file_ext)
    
    fn _load_json_model(self, model_path: String) -> MetabolicModel:
        """Load model from JSON format."""
        # This would implement JSON model loading
        # For now, return a demo model
        let parser = SBMLParser()
        return parser._create_demo_model()
    
    fn _apply_constraints(self, inout model: MetabolicModel, constraints: Dict):
        """Apply flux constraints to model."""
        for reaction_id in constraints:
            let constraint_value = float(constraints[reaction_id])
            
            # Find reaction index
            for i in range(len(model.reaction_ids)):
                if model.reaction_ids[i] == reaction_id:
                    # Set both bounds to the constraint value (equality constraint)
                    model.bounds_lower[i] = constraint_value
                    model.bounds_upper[i] = constraint_value
                    break
    
    fn _read_json_file(self, file_path: String) -> Dict:
        """Read JSON file and return parsed data."""
        let json_module = Python.import_module("json")
        
        with open(file_path, 'r') as f:
            let content = f.read()
            return json_module.loads(content)
    
    fn _write_json_file(self, file_path: String, data: Dict):
        """Write data to JSON file."""
        let json_module = Python.import_module("json")
        
        with open(file_path, 'w') as f:
            let json_str = json_module.dumps(data, indent=2)
            f.write(json_str)
    
    fn _dict_to_python(self, mojo_dict: Dict[String, Float64]) -> PythonObject:
        """Convert Mojo Dict to Python dict."""
        let py_dict = Python.dict()
        for key in mojo_dict:
            py_dict[key] = mojo_dict[key]
        return py_dict

# Main function for command-line usage
fn main():
    """Main entry point for IPC server."""
    let args = sys.argv
    
    if len(args) < 3:
        print("Usage: mojo run ipc_server.mojo <request_file> <response_file>")
        sys.exit(1)
    
    let request_file = args[1]
    let response_file = args[2]
    let verbose = len(args) > 3 and args[3] == "--verbose"
    
    # Create IPC server
    var server = IPCServer(verbose)
    
    # Process request
    let success = server.process_request(request_file, response_file)
    
    if not success:
        sys.exit(1)

# Utility functions for testing

fn test_fba_computation():
    """Test FBA computation with demo model."""
    print("Testing FBA computation...")
    
    # Create demo model
    let parser = SBMLParser()
    var model = MetabolicModel("test", "Test Model")
    parser._create_demo_model(model)
    
    # Solve FBA
    let solver = FBASolver()
    let result = solver.solve(model, "BIOMASS")
    
    print("Status:", result.status)
    print("Objective value:", result.objective_value)
    print("Number of fluxes:", len(result.flux_values))
    
    # Print some key fluxes
    let key_reactions = ["BIOMASS", "EX_glc__D_e", "HEX1"]
    for reaction_id in key_reactions:
        if reaction_id in result.flux_values:
            print(reaction_id + ":", result.flux_values[reaction_id])

fn benchmark_fba_solver():
    """Benchmark FBA solver performance."""
    print("Benchmarking FBA solver...")
    
    let parser = SBMLParser()
    var model = MetabolicModel("benchmark", "Benchmark Model")
    parser._create_demo_model(model)
    
    let solver = FBASolver()
    let num_runs = 100
    
    let start_time = time.time()
    for i in range(num_runs):
        let result = solver.solve(model, "BIOMASS")
    let total_time = time.time() - start_time
    
    let avg_time = total_time / num_runs
    print("Average solve time:", avg_time * 1000, "ms")
    print("Solves per second:", num_runs / total_time)

# Entry point when run as script
if __name__ == "__main__":
    main()
