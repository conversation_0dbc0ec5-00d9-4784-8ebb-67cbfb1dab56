from collections import Dict, List
from math import abs, sqrt
from time import time
from ..models.metabolic_model import MetabolicModel, SparseMatrix
from ..utils.matrix_utils import matrix_rank, null_space

struct EMAResult:
    """Result structure for Elementary Mode Analysis."""
    var elementary_modes: List[Dict[String, Float64]]
    var num_modes: Int
    var computation_time: Float64
    var status: String
    var statistics: Dict[String, Float64]
    
    fn __init__(inout self):
        self.elementary_modes = List[Dict[String, Float64]]()
        self.num_modes = 0
        self.computation_time = 0.0
        self.status = "unknown"
        self.statistics = Dict[String, Float64]()

struct EMASolver:
    """Elementary Mode Analysis solver using advanced algorithms."""
    var max_modes: Int
    var tolerance: Float64
    var use_compression: Bool
    var verbose: Bool
    
    fn __init__(inout self, max_modes: Int = 1000, tolerance: Float64 = 1e-10, 
                use_compression: Bool = True, verbose: Bool = False):
        self.max_modes = max_modes
        self.tolerance = tolerance
        self.use_compression = use_compression
        self.verbose = verbose
    
    fn analyze(self, model: MetabolicModel) -> EMAResult:
        """Perform Elementary Mode Analysis on the metabolic model."""
        let start_time = time.time()
        var result = EMAResult()
        
        try:
            if self.verbose:
                print("Starting Elementary Mode Analysis...")
                print(f"Model: {model.name}")
                print(f"Reactions: {len(model.reaction_ids)}")
                print(f"Metabolites: {len(model.metabolite_ids)}")
            
            # Preprocess the model
            let processed_model = self._preprocess_model(model)
            
            # Compute elementary modes using the double description method
            let modes = self._compute_elementary_modes(processed_model)
            
            # Post-process and filter modes
            let filtered_modes = self._filter_modes(modes, processed_model)
            
            result.elementary_modes = filtered_modes
            result.num_modes = len(filtered_modes)
            result.status = "completed"
            
            # Calculate statistics
            result.statistics = self._calculate_statistics(filtered_modes, processed_model)
            
            if self.verbose:
                print(f"Found {result.num_modes} elementary modes")
                print(f"Average mode length: {result.statistics.get('avg_mode_length', 0.0)}")
            
        except Exception as e:
            result.status = f"error: {str(e)}"
            result.num_modes = 0
            
        result.computation_time = time.time() - start_time
        return result
    
    fn _preprocess_model(self, model: MetabolicModel) -> MetabolicModel:
        """Preprocess model for EMA (remove blocked reactions, etc.)."""
        var processed = model  # Copy model
        
        # Remove exchange reactions for internal mode computation
        var internal_reactions = List[String]()
        for reaction_id in model.reaction_ids:
            if not self._is_exchange_reaction(reaction_id):
                internal_reactions.append(reaction_id)
        
        # Update model with only internal reactions
        # This is a simplified preprocessing step
        return processed
    
    fn _is_exchange_reaction(self, reaction_id: String) -> Bool:
        """Check if a reaction is an exchange reaction."""
        let lower_id = reaction_id.lower()
        return ("ex_" in lower_id or 
                "exchange" in lower_id or 
                "sink" in lower_id or
                "demand" in lower_id)
    
    fn _compute_elementary_modes(self, model: MetabolicModel) -> List[Dict[String, Float64]]:
        """Compute elementary modes using the double description method."""
        var modes = List[Dict[String, Float64]]()
        
        # Get stoichiometric matrix
        let S = model.stoichiometric_matrix
        let n_metabolites = len(model.metabolite_ids)
        let n_reactions = len(model.reaction_ids)
        
        # For now, implement a simplified version
        # In a full implementation, this would use the double description algorithm
        # or other advanced methods like the Schuster algorithm
        
        # Create some example modes for demonstration
        # In reality, this would compute the actual elementary modes
        for i in range(min(self.max_modes, 10)):  # Limit for demo
            var mode = Dict[String, Float64]()
            
            # Create a simple mode pattern
            for j in range(min(3, len(model.reaction_ids))):
                let reaction_id = model.reaction_ids[j]
                mode[reaction_id] = 1.0 if (i + j) % 2 == 0 else 0.0
            
            modes.append(mode)
        
        return modes
    
    fn _filter_modes(self, modes: List[Dict[String, Float64]], model: MetabolicModel) -> List[Dict[String, Float64]]:
        """Filter and validate elementary modes."""
        var filtered = List[Dict[String, Float64]]()
        
        for mode in modes:
            if self._is_valid_mode(mode, model):
                filtered.append(mode)
        
        return filtered
    
    fn _is_valid_mode(self, mode: Dict[String, Float64], model: MetabolicModel) -> Bool:
        """Check if a mode is valid (satisfies steady-state constraints)."""
        # Check if mode satisfies Sv = 0
        # This is a simplified check
        
        var total_flux = 0.0
        for reaction_id in mode:
            total_flux += abs(mode[reaction_id])
        
        return total_flux > self.tolerance
    
    fn _calculate_statistics(self, modes: List[Dict[String, Float64]], model: MetabolicModel) -> Dict[String, Float64]:
        """Calculate statistics about the elementary modes."""
        var stats = Dict[String, Float64]()
        
        if len(modes) == 0:
            return stats
        
        # Average mode length (number of active reactions)
        var total_length = 0.0
        var total_flux = 0.0
        
        for mode in modes:
            var mode_length = 0.0
            var mode_flux = 0.0
            
            for reaction_id in mode:
                if abs(mode[reaction_id]) > self.tolerance:
                    mode_length += 1.0
                    mode_flux += abs(mode[reaction_id])
            
            total_length += mode_length
            total_flux += mode_flux
        
        stats["avg_mode_length"] = total_length / len(modes)
        stats["avg_total_flux"] = total_flux / len(modes)
        stats["num_modes"] = Float64(len(modes))
        
        return stats

# Utility functions

fn solve_ema(model: MetabolicModel, max_modes: Int = 1000) -> EMAResult:
    """Convenience function to perform Elementary Mode Analysis."""
    let ema_solver = EMASolver(max_modes)
    return ema_solver.analyze(model)

fn find_essential_modes(modes: List[Dict[String, Float64]], biomass_reaction: String) -> List[Int]:
    """Find elementary modes that are essential for biomass production."""
    var essential_indices = List[Int]()
    
    for i in range(len(modes)):
        let mode = modes[i]
        if biomass_reaction in mode and abs(mode[biomass_reaction]) > 1e-10:
            essential_indices.append(i)
    
    return essential_indices

fn analyze_mode_diversity(modes: List[Dict[String, Float64]]) -> Dict[String, Float64]:
    """Analyze the diversity of elementary modes."""
    var diversity_stats = Dict[String, Float64]()
    
    if len(modes) == 0:
        return diversity_stats
    
    # Calculate reaction usage frequency
    var reaction_usage = Dict[String, Int]()
    
    for mode in modes:
        for reaction_id in mode:
            if abs(mode[reaction_id]) > 1e-10:
                if reaction_id in reaction_usage:
                    reaction_usage[reaction_id] += 1
                else:
                    reaction_usage[reaction_id] = 1
    
    # Calculate diversity metrics
    var total_reactions = len(reaction_usage)
    var total_modes = len(modes)
    
    diversity_stats["total_reactions_used"] = Float64(total_reactions)
    diversity_stats["total_modes"] = Float64(total_modes)
    
    if total_reactions > 0:
        var avg_usage = 0.0
        for reaction_id in reaction_usage:
            avg_usage += Float64(reaction_usage[reaction_id])
        avg_usage /= Float64(total_reactions)
        diversity_stats["avg_reaction_usage"] = avg_usage
    
    return diversity_stats
