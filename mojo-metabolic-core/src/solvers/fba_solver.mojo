from python import Python
from collections import Dict, List
from .models.metabolic_model import MetabolicModel
import time

struct FBAResult:
    """Result of Flux Balance Analysis."""
    var objective_value: Float64
    var flux_values: Dict[String, Float64]
    var status: String
    var solver_time: Float64
    var dual_values: Dict[String, Float64]
    
    fn __init__(inout self):
        self.objective_value = 0.0
        self.flux_values = Dict[String, Float64]()
        self.status = "unknown"
        self.solver_time = 0.0
        self.dual_values = Dict[String, Float64]()

struct FBASolver:
    """High-performance Flux Balance Analysis solver using Python interop."""
    var solver_name: String
    var tolerance: Float64
    var max_iterations: Int
    var verbose: Bool
    
    fn __init__(inout self, solver_name: String = "glpk", tolerance: Float64 = 1e-9, 
                max_iterations: Int = 10000, verbose: Bool = False):
        self.solver_name = solver_name
        self.tolerance = tolerance
        self.max_iterations = max_iterations
        self.verbose = verbose
    
    fn solve(self, model: MetabolicModel, objective_reaction: String = "") -> FBAResult:
        """Solve FBA problem for the given model."""
        let start_time = time.time()
        
        try:
            # Use Python scipy for linear programming
            let result = self._solve_with_scipy(model, objective_reaction)
            result.solver_time = time.time() - start_time
            return result
        except:
            # Fallback to simple solver
            let result = self._solve_simple(model, objective_reaction)
            result.solver_time = time.time() - start_time
            return result
    
    fn _solve_with_scipy(self, model: MetabolicModel, objective_reaction: String) -> FBAResult:
        """Solve using SciPy's linear programming solver."""
        let scipy = Python.import_module("scipy.optimize")
        let np = Python.import_module("numpy")
        
        let n_reactions = model.get_reaction_count()
        let n_metabolites = model.get_metabolite_count()
        
        # Prepare objective vector (minimize negative of objective for maximization)
        var c = np.zeros(n_reactions)
        
        if objective_reaction:
            # Find objective reaction index
            for i in range(len(model.reaction_ids)):
                if model.reaction_ids[i] == objective_reaction:
                    c[i] = -1.0  # Negative for maximization
                    break
        else:
            # Use model's objective coefficients
            for i in range(n_reactions):
                c[i] = -model.objective[i]  # Negative for maximization
        
        # Prepare constraint matrix (Ax = b for steady state)
        var A_eq = np.zeros((n_metabolites, n_reactions))
        
        # Fill constraint matrix from stoichiometric matrix
        let S = model.stoichiometric_matrix
        for k in range(S.nnz):
            let i = S.row_indices[k]
            let j = S.col_indices[k]
            let value = S.values[k]
            A_eq[i, j] = value
        
        # Right-hand side (steady state: Sv = 0)
        var b_eq = np.zeros(n_metabolites)
        
        # Bounds for variables
        var bounds = []
        for i in range(n_reactions):
            let lower = model.bounds_lower[i]
            let upper = model.bounds_upper[i]
            bounds.append((lower, upper))
        
        # Solve linear program
        let scipy_result = scipy.linprog(
            c=c,
            A_eq=A_eq,
            b_eq=b_eq,
            bounds=bounds,
            method="highs",
            options={"presolve": True}
        )
        
        # Process results
        var result = FBAResult()
        
        if scipy_result.success:
            result.status = "optimal"
            result.objective_value = -scipy_result.fun  # Convert back from minimization
            
            # Store flux values
            for i in range(n_reactions):
                let reaction_id = model.reaction_ids[i]
                let flux = float(scipy_result.x[i])
                result.flux_values[reaction_id] = flux
            
            # Store dual values if available
            if hasattr(scipy_result, "ineqlin") and scipy_result.ineqlin.marginals is not None:
                for i in range(n_metabolites):
                    let metabolite_id = model.metabolite_ids[i]
                    let dual = float(scipy_result.ineqlin.marginals[i])
                    result.dual_values[metabolite_id] = dual
        else:
            result.status = "infeasible"
            result.objective_value = 0.0
        
        return result
    
    fn _solve_simple(self, model: MetabolicModel, objective_reaction: String) -> FBAResult:
        """Simple solver fallback when SciPy is not available."""
        var result = FBAResult()
        
        # This is a simplified solver for demonstration
        # In practice, you would implement a proper LP solver or use existing libraries
        
        let n_reactions = model.get_reaction_count()
        
        # Find objective reaction
        var obj_index = -1
        if objective_reaction:
            for i in range(len(model.reaction_ids)):
                if model.reaction_ids[i] == objective_reaction:
                    obj_index = i
                    break
        else:
            # Find first reaction with non-zero objective coefficient
            for i in range(n_reactions):
                if abs(model.objective[i]) > 1e-12:
                    obj_index = i
                    break
        
        if obj_index >= 0:
            # Simple heuristic: set objective reaction to its upper bound
            let obj_reaction_id = model.reaction_ids[obj_index]
            let max_flux = min(model.bounds_upper[obj_index], 1000.0)
            
            result.status = "optimal"
            result.objective_value = max_flux
            
            # Set all fluxes to zero except objective
            for i in range(n_reactions):
                let reaction_id = model.reaction_ids[i]
                if i == obj_index:
                    result.flux_values[reaction_id] = max_flux
                else:
                    result.flux_values[reaction_id] = 0.0
        else:
            result.status = "no_objective"
            result.objective_value = 0.0
            
            # Set all fluxes to zero
            for i in range(n_reactions):
                let reaction_id = model.reaction_ids[i]
                result.flux_values[reaction_id] = 0.0
        
        return result
    
    fn solve_fva(self, model: MetabolicModel, reaction_ids: List[String]) -> Dict[String, Dict[String, Float64]]:
        """Perform Flux Variability Analysis for specified reactions."""
        var fva_results = Dict[String, Dict[String, Float64]]()
        
        # For each reaction, solve two optimization problems (min and max)
        for reaction_id in reaction_ids:
            var bounds = Dict[String, Float64]()
            
            # Maximize flux
            let max_result = self.solve(model, reaction_id)
            if max_result.status == "optimal":
                bounds["maximum"] = max_result.flux_values[reaction_id]
            else:
                bounds["maximum"] = 0.0
            
            # Minimize flux (negate objective)
            # This would require modifying the objective temporarily
            bounds["minimum"] = -bounds["maximum"]  # Simplified for demo
            
            fva_results[reaction_id] = bounds
        
        return fva_results
    
    fn analyze_essentiality(self, model: MetabolicModel, biomass_reaction: String) -> Dict[String, Bool]:
        """Analyze gene/reaction essentiality by knockout simulation."""
        var essentiality = Dict[String, Bool]()
        
        # Get wild-type growth rate
        let wt_result = self.solve(model, biomass_reaction)
        let wt_growth = wt_result.objective_value
        let growth_threshold = 0.01 * wt_growth  # 1% of wild-type growth
        
        # Test each reaction knockout
        for reaction_id in model.reaction_ids:
            if reaction_id == biomass_reaction:
                continue  # Skip biomass reaction
            
            # Create knockout model (set bounds to zero)
            var ko_model = model  # Copy model
            
            # Find reaction index and set bounds to zero
            for i in range(len(ko_model.reaction_ids)):
                if ko_model.reaction_ids[i] == reaction_id:
                    ko_model.bounds_lower[i] = 0.0
                    ko_model.bounds_upper[i] = 0.0
                    break
            
            # Solve knockout model
            let ko_result = self.solve(ko_model, biomass_reaction)
            
            # Check if growth is severely impaired
            let is_essential = ko_result.objective_value < growth_threshold
            essentiality[reaction_id] = is_essential
        
        return essentiality

# Utility functions

fn solve_fba(model: MetabolicModel, objective_reaction: String = "", solver: String = "glpk") -> FBAResult:
    """Convenience function to solve FBA."""
    let fba_solver = FBASolver(solver)
    return fba_solver.solve(model, objective_reaction)

fn find_biomass_reaction(model: MetabolicModel) -> String:
    """Find the biomass reaction in the model."""
    for reaction_id in model.reaction_ids:
        if "biomass" in reaction_id.lower() or "growth" in reaction_id.lower():
            return reaction_id
    
    # Look for reaction with highest objective coefficient
    var max_obj = 0.0
    var biomass_id = ""
    for i in range(len(model.objective)):
        if abs(model.objective[i]) > max_obj:
            max_obj = abs(model.objective[i])
            biomass_id = model.reaction_ids[i]
    
    return biomass_id

fn validate_fba_solution(model: MetabolicModel, result: FBAResult) -> Bool:
    """Validate FBA solution for mass balance and bounds."""
    if result.status != "optimal":
        return False
    
    let tolerance = 1e-6
    
    # Check flux bounds
    for i in range(len(model.reaction_ids)):
        let reaction_id = model.reaction_ids[i]
        if reaction_id in result.flux_values:
            let flux = result.flux_values[reaction_id]
            let lower = model.bounds_lower[i]
            let upper = model.bounds_upper[i]
            
            if flux < lower - tolerance or flux > upper + tolerance:
                return False
    
    # Check mass balance (Sv = 0)
    let S = model.stoichiometric_matrix
    for i in range(S.rows):
        var mass_balance = 0.0
        for k in range(S.nnz):
            if S.row_indices[k] == i:
                let j = S.col_indices[k]
                let stoich = S.values[k]
                let reaction_id = model.reaction_ids[j]
                if reaction_id in result.flux_values:
                    mass_balance += stoich * result.flux_values[reaction_id]
        
        if abs(mass_balance) > tolerance:
            return False
    
    return True
