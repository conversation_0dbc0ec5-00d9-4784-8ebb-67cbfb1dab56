from python import Python
from collections import Dict, List
from .models.metabolic_model import MetabolicModel, Reaction, Metabolite

struct SBMLParser:
    """Parser for SBML metabolic model files using Python interop."""
    
    fn __init__(inout self):
        pass
    
    fn parse_file(self, file_path: String) -> MetabolicModel:
        """Parse SBML file and return MetabolicModel."""
        try:
            # Import required Python libraries
            let libsbml = Python.import_module("libsbml")
            let os = Python.import_module("os")
            
            # Check if file exists
            if not os.path.exists(file_path):
                raise Error("SBML file not found: " + file_path)
            
            # Read SBML document
            let document = libsbml.readSBML(file_path)
            
            if document.getNumErrors() > 0:
                raise Error("Error reading SBML file")
            
            let sbml_model = document.getModel()
            if not sbml_model:
                raise Error("No model found in SBML file")
            
            # Create metabolic model
            let model_id = str(sbml_model.getId()) if sbml_model.getId() else "model"
            let model_name = str(sbml_model.getName()) if sbml_model.getName() else model_id
            var model = MetabolicModel(model_id, model_name)
            
            # Parse compartments (optional for now)
            self._parse_compartments(sbml_model, model)
            
            # Parse species (metabolites)
            self._parse_species(sbml_model, model)
            
            # Parse reactions
            self._parse_reactions(sbml_model, model)
            
            # Build stoichiometric matrix
            model.build_stoichiometric_matrix()
            
            return model
            
        except e:
            # Fallback to simple text parsing if libsbml not available
            return self._parse_simple_sbml(file_path)
    
    fn _parse_compartments(self, sbml_model: PythonObject, inout model: MetabolicModel):
        """Parse compartments from SBML model."""
        let compartments = sbml_model.getListOfCompartments()
        # For now, we'll just note compartments exist
        # Full implementation would store compartment information
        pass
    
    fn _parse_species(self, sbml_model: PythonObject, inout model: MetabolicModel):
        """Parse species (metabolites) from SBML model."""
        let species_list = sbml_model.getListOfSpecies()
        
        for i in range(species_list.size()):
            let species = species_list.get(i)
            let species_id = str(species.getId())
            let species_name = str(species.getName()) if species.getName() else species_id
            let compartment = str(species.getCompartment()) if species.getCompartment() else "c"
            
            # Get formula and charge if available
            var formula = ""
            var charge = 0
            
            # Try to get formula from FBC plugin
            try:
                let fbc_species = species.getPlugin("fbc")
                if fbc_species:
                    formula = str(fbc_species.getChemicalFormula()) if fbc_species.getChemicalFormula() else ""
                    charge = int(fbc_species.getCharge()) if fbc_species.getCharge() else 0
            except:
                pass
            
            let metabolite = Metabolite(species_id, species_name, formula, compartment, charge)
            model.add_metabolite(metabolite)
    
    fn _parse_reactions(self, sbml_model: PythonObject, inout model: MetabolicModel):
        """Parse reactions from SBML model."""
        let reactions_list = sbml_model.getListOfReactions()
        
        for i in range(reactions_list.size()):
            let sbml_reaction = reactions_list.get(i)
            let reaction_id = str(sbml_reaction.getId())
            let reaction_name = str(sbml_reaction.getName()) if sbml_reaction.getName() else reaction_id
            
            # Get bounds from FBC plugin
            var lower_bound = -1000.0
            var upper_bound = 1000.0
            var objective_coefficient = 0.0
            
            try:
                let fbc_reaction = sbml_reaction.getPlugin("fbc")
                if fbc_reaction:
                    lower_bound = float(fbc_reaction.getLowerFluxBound()) if fbc_reaction.getLowerFluxBound() else -1000.0
                    upper_bound = float(fbc_reaction.getUpperFluxBound()) if fbc_reaction.getUpperFluxBound() else 1000.0
            except:
                # Fallback: check reversibility
                if not sbml_reaction.getReversible():
                    lower_bound = 0.0
            
            var reaction = Reaction(reaction_id, reaction_name, lower_bound, upper_bound, objective_coefficient)
            
            # Parse reactants (negative stoichiometry)
            let reactants = sbml_reaction.getListOfReactants()
            for j in range(reactants.size()):
                let reactant = reactants.get(j)
                let species_id = str(reactant.getSpecies())
                let stoichiometry = -float(reactant.getStoichiometry())
                reaction.add_metabolite(species_id, stoichiometry)
            
            # Parse products (positive stoichiometry)
            let products = sbml_reaction.getListOfProducts()
            for j in range(products.size()):
                let product = products.get(j)
                let species_id = str(product.getSpecies())
                let stoichiometry = float(product.getStoichiometry())
                reaction.add_metabolite(species_id, stoichiometry)
            
            model.add_reaction(reaction)
    
    fn _parse_simple_sbml(self, file_path: String) -> MetabolicModel:
        """Simple SBML parser fallback when libsbml is not available."""
        # This is a simplified parser for basic SBML files
        # In a real implementation, this would parse XML manually
        
        var model = MetabolicModel("simple_model", "Simple Model")
        
        # For demonstration, create a simple E. coli core model
        self._create_demo_model(model)
        
        return model
    
    fn _create_demo_model(self, inout model: MetabolicModel):
        """Create a demo E. coli core model for testing."""
        # Add some basic metabolites
        model.add_metabolite(Metabolite("glc__D_e", "D-Glucose external", "C6H12O6", "e", 0))
        model.add_metabolite(Metabolite("glc__D_c", "D-Glucose cytoplasm", "C6H12O6", "c", 0))
        model.add_metabolite(Metabolite("g6p_c", "Glucose 6-phosphate", "C6H11O9P", "c", -2))
        model.add_metabolite(Metabolite("atp_c", "ATP", "C10H12N5O13P3", "c", -4))
        model.add_metabolite(Metabolite("adp_c", "ADP", "C10H12N5O10P2", "c", -3))
        model.add_metabolite(Metabolite("pi_c", "Phosphate", "HO4P", "c", -2))
        model.add_metabolite(Metabolite("h_c", "H+", "H", "c", 1))
        model.add_metabolite(Metabolite("biomass_c", "Biomass", "", "c", 0))
        
        # Add exchange reactions
        var ex_glc = Reaction("EX_glc__D_e", "Glucose exchange", -10.0, 1000.0, 0.0)
        ex_glc.add_metabolite("glc__D_e", -1.0)
        model.add_reaction(ex_glc)
        
        # Add glucose transport
        var glc_transport = Reaction("GLCpts", "Glucose transport", 0.0, 1000.0, 0.0)
        glc_transport.add_metabolite("glc__D_e", -1.0)
        glc_transport.add_metabolite("glc__D_c", 1.0)
        model.add_reaction(glc_transport)
        
        # Add hexokinase
        var hxk = Reaction("HEX1", "Hexokinase", 0.0, 1000.0, 0.0)
        hxk.add_metabolite("glc__D_c", -1.0)
        hxk.add_metabolite("atp_c", -1.0)
        hxk.add_metabolite("g6p_c", 1.0)
        hxk.add_metabolite("adp_c", 1.0)
        hxk.add_metabolite("h_c", 1.0)
        model.add_reaction(hxk)
        
        # Add biomass reaction
        var biomass = Reaction("BIOMASS", "Biomass production", 0.0, 1000.0, 1.0)
        biomass.add_metabolite("g6p_c", -1.0)
        biomass.add_metabolite("atp_c", -30.0)
        biomass.add_metabolite("biomass_c", 1.0)
        biomass.add_metabolite("adp_c", 30.0)
        biomass.add_metabolite("pi_c", 30.0)
        model.add_reaction(biomass)
        
        # Add ATP maintenance
        var atpm = Reaction("ATPM", "ATP maintenance", 8.39, 1000.0, 0.0)
        atpm.add_metabolite("atp_c", -1.0)
        atpm.add_metabolite("adp_c", 1.0)
        atpm.add_metabolite("pi_c", 1.0)
        atpm.add_metabolite("h_c", 1.0)
        model.add_reaction(atpm)
        
        # Build the stoichiometric matrix
        model.build_stoichiometric_matrix()

# Utility functions for parsing

fn parse_sbml_file(file_path: String) -> MetabolicModel:
    """Convenience function to parse SBML file."""
    let parser = SBMLParser()
    return parser.parse_file(file_path)

fn validate_sbml_file(file_path: String) -> Bool:
    """Validate if file is a valid SBML file."""
    try:
        let model = parse_sbml_file(file_path)
        return model.validate()
    except:
        return False
