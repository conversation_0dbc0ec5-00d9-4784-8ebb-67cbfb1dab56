from .ipc.server import IPCServer, test_fba_computation, benchmark_fba_solver
from .models.metabolic_model import MetabolicModel
from .parsers.sbml_parser import SBMLParser
from .solvers.fba_solver import FBASolver
import sys
import os

fn main():
    """Main entry point for Mojo metabolic core."""
    let args = sys.argv
    
    if len(args) < 2:
        print_usage()
        return
    
    let command = args[1]
    
    if command == "ipc":
        # IPC mode - process request/response files
        if len(args) < 4:
            print("Usage: mojo run main.mojo ipc <request_file> <response_file> [--verbose]")
            sys.exit(1)
        
        let request_file = args[2]
        let response_file = args[3]
        let verbose = len(args) > 4 and args[4] == "--verbose"
        
        var server = IPCServer(verbose)
        let success = server.process_request(request_file, response_file)
        
        if not success:
            sys.exit(1)
    
    elif command == "test":
        # Test mode - run built-in tests
        print("Running Mojo Metabolic Core Tests...")
        test_fba_computation()
        print("Tests completed successfully!")
    
    elif command == "benchmark":
        # Benchmark mode - performance testing
        print("Running Mojo Metabolic Core Benchmarks...")
        benchmark_fba_solver()
        print("Benchmarks completed!")
    
    elif command == "solve":
        # Direct solve mode - solve FBA for a model file
        if len(args) < 3:
            print("Usage: mojo run main.mojo solve <model_file> [objective_reaction]")
            sys.exit(1)
        
        let model_file = args[2]
        let objective = args[3] if len(args) > 3 else ""
        
        solve_model_file(model_file, objective)
    
    elif command == "validate":
        # Validation mode - validate model file
        if len(args) < 3:
            print("Usage: mojo run main.mojo validate <model_file>")
            sys.exit(1)
        
        let model_file = args[2]
        validate_model_file(model_file)
    
    elif command == "info":
        # Info mode - display model information
        if len(args) < 3:
            print("Usage: mojo run main.mojo info <model_file>")
            sys.exit(1)
        
        let model_file = args[2]
        show_model_info(model_file)
    
    else:
        print("Unknown command:", command)
        print_usage()
        sys.exit(1)

fn print_usage():
    """Print usage information."""
    print("Mojo Metabolic Core - High-performance metabolic modeling")
    print("")
    print("Usage:")
    print("  mojo run main.mojo <command> [options]")
    print("")
    print("Commands:")
    print("  ipc <request_file> <response_file>  Process IPC request from Go backend")
    print("  test                                Run built-in tests")
    print("  benchmark                           Run performance benchmarks")
    print("  solve <model_file> [objective]      Solve FBA for model file")
    print("  validate <model_file>               Validate model file")
    print("  info <model_file>                   Show model information")
    print("")
    print("Examples:")
    print("  mojo run main.mojo test")
    print("  mojo run main.mojo solve ecoli_core.xml BIOMASS")
    print("  mojo run main.mojo ipc request.json response.json --verbose")

fn solve_model_file(model_file: String, objective: String):
    """Solve FBA for a model file."""
    try:
        print("Loading model from:", model_file)
        
        # Check if file exists
        if not os.path.exists(model_file):
            print("Error: Model file not found:", model_file)
            sys.exit(1)
        
        # Parse model
        let parser = SBMLParser()
        var model = parser.parse_file(model_file)
        
        print("Model loaded successfully:")
        print("  Reactions:", model.get_reaction_count())
        print("  Metabolites:", model.get_metabolite_count())
        
        # Set objective if provided
        if objective:
            print("Setting objective to:", objective)
            model.set_objective(objective, 1.0)
        else:
            # Find biomass reaction
            let biomass_id = find_biomass_reaction(model)
            if biomass_id:
                print("Using biomass reaction:", biomass_id)
                objective = biomass_id
            else:
                print("Warning: No objective specified and no biomass reaction found")
        
        # Solve FBA
        print("Solving FBA...")
        let solver = FBASolver(verbose=True)
        let result = solver.solve(model, objective)
        
        # Display results
        print("\nFBA Results:")
        print("  Status:", result.status)
        print("  Objective value:", result.objective_value)
        print("  Solver time:", result.solver_time, "seconds")
        
        if result.status == "optimal":
            print("\nKey fluxes:")
            let key_reactions = [objective, "EX_glc__D_e", "EX_o2_e", "EX_co2_e"]
            for reaction_id in key_reactions:
                if reaction_id in result.flux_values:
                    let flux = result.flux_values[reaction_id]
                    if abs(flux) > 1e-6:  # Only show significant fluxes
                        print(f"  {reaction_id}: {flux:.6f}")
        
    except e:
        print("Error solving model:", str(e))
        sys.exit(1)

fn validate_model_file(model_file: String):
    """Validate a model file."""
    try:
        print("Validating model file:", model_file)
        
        if not os.path.exists(model_file):
            print("Error: Model file not found:", model_file)
            sys.exit(1)
        
        let parser = SBMLParser()
        let model = parser.parse_file(model_file)
        
        print("Model parsed successfully:")
        print("  ID:", model.id)
        print("  Name:", model.name)
        print("  Reactions:", model.get_reaction_count())
        print("  Metabolites:", model.get_metabolite_count())
        
        # Validate model consistency
        let is_valid = model.validate()
        
        if is_valid:
            print("✓ Model validation passed")
        else:
            print("✗ Model validation failed")
            sys.exit(1)
        
        # Additional checks
        let exchange_reactions = model.get_exchange_reactions()
        print("  Exchange reactions:", len(exchange_reactions))
        
        # Check for biomass reaction
        let biomass_id = find_biomass_reaction(model)
        if biomass_id:
            print("  Biomass reaction:", biomass_id)
        else:
            print("  Warning: No biomass reaction found")
        
    except e:
        print("Error validating model:", str(e))
        sys.exit(1)

fn show_model_info(model_file: String):
    """Show detailed information about a model."""
    try:
        print("Model Information for:", model_file)
        print("=" * 50)
        
        if not os.path.exists(model_file):
            print("Error: Model file not found:", model_file)
            sys.exit(1)
        
        let parser = SBMLParser()
        let model = parser.parse_file(model_file)
        
        # Basic info
        print("Basic Information:")
        print(f"  ID: {model.id}")
        print(f"  Name: {model.name}")
        print(f"  Reactions: {model.get_reaction_count()}")
        print(f"  Metabolites: {model.get_metabolite_count()}")
        
        # Exchange reactions
        let exchange_reactions = model.get_exchange_reactions()
        print(f"\nExchange Reactions ({len(exchange_reactions)}):")
        for i in range(min(10, len(exchange_reactions))):  # Show first 10
            print(f"  {exchange_reactions[i]}")
        if len(exchange_reactions) > 10:
            print(f"  ... and {len(exchange_reactions) - 10} more")
        
        # Objective reactions
        print("\nObjective Reactions:")
        var has_objective = False
        for i in range(len(model.objective)):
            if abs(model.objective[i]) > 1e-12:
                let reaction_id = model.reaction_ids[i]
                let coeff = model.objective[i]
                print(f"  {reaction_id}: {coeff}")
                has_objective = True
        
        if not has_objective:
            print("  No objective reactions found")
        
        # Bounds summary
        print("\nBounds Summary:")
        var reversible_count = 0
        var irreversible_count = 0
        var exchange_count = 0
        
        for i in range(len(model.reaction_ids)):
            let reaction_id = model.reaction_ids[i]
            let lower = model.bounds_lower[i]
            let upper = model.bounds_upper[i]
            
            if reaction_id.startswith("EX_"):
                exchange_count += 1
            elif lower < 0:
                reversible_count += 1
            else:
                irreversible_count += 1
        
        print(f"  Reversible reactions: {reversible_count}")
        print(f"  Irreversible reactions: {irreversible_count}")
        print(f"  Exchange reactions: {exchange_count}")
        
    except e:
        print("Error reading model info:", str(e))
        sys.exit(1)

# Helper function to find biomass reaction
fn find_biomass_reaction(model: MetabolicModel) -> String:
    """Find the biomass reaction in the model."""
    for reaction_id in model.reaction_ids:
        let lower_id = reaction_id.lower()
        if "biomass" in lower_id or "growth" in lower_id or "bio" in lower_id:
            return reaction_id
    
    # Look for reaction with highest objective coefficient
    var max_obj = 0.0
    var biomass_id = ""
    for i in range(len(model.objective)):
        if abs(model.objective[i]) > max_obj:
            max_obj = abs(model.objective[i])
            biomass_id = model.reaction_ids[i]
    
    return biomass_id

# Entry point
if __name__ == "__main__":
    main()
