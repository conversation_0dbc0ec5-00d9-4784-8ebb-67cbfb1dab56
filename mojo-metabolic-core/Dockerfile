# Mojo Compute Engine Dockerfile
FROM ubuntu:22.04

# Install system dependencies
RUN apt-get update && apt-get install -y \
    curl \
    wget \
    python3 \
    python3-pip \
    python3-dev \
    build-essential \
    libglpk-dev \
    glpk-utils \
    ca-certificates \
    && rm -rf /var/lib/apt/lists/*

# Install Python dependencies
COPY requirements.txt /tmp/
RUN pip3 install --no-cache-dir -r /tmp/requirements.txt

# Install Mojo (this would need to be updated with actual Mojo installation)
# For now, we'll use a placeholder since Mojo installation varies
RUN echo "# Mojo installation would go here" > /tmp/mojo_install.sh
# RUN curl -s https://get.modular.com | sh -
# RUN modular install mojo

# Create non-root user
RUN groupadd -g 1001 appgroup && \
    useradd -u 1001 -g appgroup -m appuser

# Set working directory
WORKDIR /app

# Copy source code
COPY src/ ./src/
COPY requirements.txt ./

# Create IPC directory
RUN mkdir -p ipc && \
    chown -R appuser:appgroup /app

# Switch to non-root user
USER appuser

# Set environment variables
ENV PYTHONPATH=/app/src
ENV MOJO_PATH=/app/src

# Health check (test Mojo availability)
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
    CMD python3 -c "import sys; print('Mojo compute engine ready')" || exit 1

# Default command (can be overridden)
CMD ["python3", "-c", "print('Mojo Metabolic Core ready for IPC requests')"]
