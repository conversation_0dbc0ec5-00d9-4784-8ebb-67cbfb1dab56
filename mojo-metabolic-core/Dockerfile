# Mojo Compute Engine Dockerfile
FROM ubuntu:22.04

# Install system dependencies
RUN apt-get update && apt-get install -y \
    curl \
    wget \
    git \
    python3 \
    python3-pip \
    python3-dev \
    python3-venv \
    build-essential \
    cmake \
    ninja-build \
    libglpk-dev \
    glpk-utils \
    libopenblas-dev \
    liblapack-dev \
    gfortran \
    ca-certificates \
    && rm -rf /var/lib/apt/lists/*

# Install Python dependencies
COPY requirements.txt /tmp/
RUN pip3 install --no-cache-dir -r /tmp/requirements.txt

# For now, we'll use Python as the compute engine
# In production, this would be replaced with actual Mojo installation
RUN echo "Using Python as compute engine fallback" > /tmp/compute_engine.log

# Create non-root user
RUN groupadd -g 1001 appgroup && \
    useradd -u 1001 -g appgroup -m appuser

# Set working directory
WORKDIR /app

# Copy source code
COPY src/ ./src/
COPY requirements.txt ./

# Create IPC directory
RUN mkdir -p ipc && \
    chown -R appuser:appgroup /app

# Switch to non-root user
USER appuser

# Set environment variables
ENV PYTHONPATH=/app/src
ENV MOJO_PATH=/app/src

# Health check (test Python compute engine availability)
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
    CMD python3 -c "import numpy, scipy; print('Python compute engine ready')" || exit 1

# Default command (can be overridden)
CMD ["python3", "-c", "print('Mojo Metabolic Core ready for IPC requests'); import time; time.sleep(3600)"]
