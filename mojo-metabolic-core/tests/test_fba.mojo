from ..src.models.metabolic_model import MetabolicModel, Reaction, Metabolite
from ..src.parsers.sbml_parser import SBMLParser
from ..src.solvers.fba_solver import FBASolver, solve_fba, validate_fba_solution
import testing

fn test_metabolic_model_creation():
    """Test basic metabolic model creation and manipulation."""
    print("Testing metabolic model creation...")
    
    var model = MetabolicModel("test_model", "Test Model")
    
    # Add metabolites
    let glc = Metabolite("glc_c", "Glucose", "C6H12O6", "c", 0)
    let atp = Metabolite("atp_c", "ATP", "C10H12N5O13P3", "c", -4)
    let adp = Metabolite("adp_c", "ADP", "C10H12N5O10P2", "c", -3)
    
    model.add_metabolite(glc)
    model.add_metabolite(atp)
    model.add_metabolite(adp)
    
    # Add reactions
    var reaction = Reaction("HEX1", "Hexokinase", 0.0, 1000.0, 0.0)
    reaction.add_metabolite("glc_c", -1.0)
    reaction.add_metabolite("atp_c", -1.0)
    reaction.add_metabolite("adp_c", 1.0)
    
    model.add_reaction(reaction)
    
    # Test model properties
    assert_equal(model.get_metabolite_count(), 3)
    assert_equal(model.get_reaction_count(), 1)
    assert_true(model.validate())
    
    print("✓ Metabolic model creation test passed")

fn test_sbml_parser():
    """Test SBML parser with demo model."""
    print("Testing SBML parser...")
    
    let parser = SBMLParser()
    var model = MetabolicModel("demo", "Demo Model")
    parser._create_demo_model(model)
    
    # Verify demo model structure
    assert_true(model.get_reaction_count() > 0)
    assert_true(model.get_metabolite_count() > 0)
    assert_true(model.validate())
    
    # Check for essential components
    let exchange_reactions = model.get_exchange_reactions()
    assert_true(len(exchange_reactions) > 0)
    
    # Verify biomass reaction exists
    var has_biomass = False
    for reaction_id in model.reaction_ids:
        if "BIOMASS" in reaction_id:
            has_biomass = True
            break
    assert_true(has_biomass)
    
    print("✓ SBML parser test passed")

fn test_fba_solver():
    """Test FBA solver with demo model."""
    print("Testing FBA solver...")
    
    # Create demo model
    let parser = SBMLParser()
    var model = MetabolicModel("fba_test", "FBA Test Model")
    parser._create_demo_model(model)
    
    # Solve FBA
    let solver = FBASolver()
    let result = solver.solve(model, "BIOMASS")
    
    # Verify results
    assert_equal(result.status, "optimal")
    assert_true(result.objective_value > 0.0)
    assert_true(len(result.flux_values) > 0)
    assert_true(result.solver_time >= 0.0)
    
    # Check specific fluxes
    assert_true("BIOMASS" in result.flux_values)
    assert_true(result.flux_values["BIOMASS"] > 0.0)
    
    # Validate solution
    let is_valid = validate_fba_solution(model, result)
    assert_true(is_valid)
    
    print("✓ FBA solver test passed")

fn test_fba_constraints():
    """Test FBA solver with constraints."""
    print("Testing FBA solver with constraints...")
    
    # Create demo model
    let parser = SBMLParser()
    var model = MetabolicModel("constraint_test", "Constraint Test Model")
    parser._create_demo_model(model)
    
    # Apply glucose uptake constraint
    for i in range(len(model.reaction_ids)):
        if model.reaction_ids[i] == "EX_glc__D_e":
            model.bounds_lower[i] = -5.0  # Limit glucose uptake
            model.bounds_upper[i] = 0.0
            break
    
    # Solve FBA
    let solver = FBASolver()
    let result = solver.solve(model, "BIOMASS")
    
    # Verify results
    assert_equal(result.status, "optimal")
    
    # Check that glucose flux respects constraint
    if "EX_glc__D_e" in result.flux_values:
        let glc_flux = result.flux_values["EX_glc__D_e"]
        assert_true(glc_flux >= -5.1)  # Allow small tolerance
        assert_true(glc_flux <= 0.1)
    
    print("✓ FBA constraints test passed")

fn test_sparse_matrix():
    """Test sparse matrix operations."""
    print("Testing sparse matrix...")
    
    from ..src.models.metabolic_model import SparseMatrix
    
    var matrix = SparseMatrix(3, 3)
    
    # Add entries
    matrix.add_entry(0, 0, 1.0)
    matrix.add_entry(1, 1, 2.0)
    matrix.add_entry(2, 2, 3.0)
    matrix.add_entry(0, 2, 0.5)
    
    # Test retrieval
    assert_equal(matrix.get_value(0, 0), 1.0)
    assert_equal(matrix.get_value(1, 1), 2.0)
    assert_equal(matrix.get_value(2, 2), 3.0)
    assert_equal(matrix.get_value(0, 2), 0.5)
    assert_equal(matrix.get_value(1, 0), 0.0)  # Should be zero
    
    # Test dimensions
    assert_equal(matrix.rows, 3)
    assert_equal(matrix.cols, 3)
    assert_equal(matrix.nnz, 4)
    
    print("✓ Sparse matrix test passed")

fn test_reaction_properties():
    """Test reaction property methods."""
    print("Testing reaction properties...")
    
    # Test reversible reaction
    var reversible = Reaction("R1", "Reversible", -1000.0, 1000.0, 0.0)
    assert_true(reversible.is_reversible())
    assert_false(reversible.is_exchange())
    
    # Test irreversible reaction
    var irreversible = Reaction("R2", "Irreversible", 0.0, 1000.0, 0.0)
    assert_false(irreversible.is_reversible())
    assert_false(irreversible.is_exchange())
    
    # Test exchange reaction
    var exchange = Reaction("EX_glc_e", "Glucose exchange", -10.0, 1000.0, 0.0)
    assert_true(exchange.is_reversible())
    assert_true(exchange.is_exchange())
    
    print("✓ Reaction properties test passed")

fn test_model_validation():
    """Test model validation."""
    print("Testing model validation...")
    
    var model = MetabolicModel("validation_test", "Validation Test")
    
    # Add metabolites
    model.add_metabolite(Metabolite("A", "Metabolite A", "", "c", 0))
    model.add_metabolite(Metabolite("B", "Metabolite B", "", "c", 0))
    
    # Add valid reaction
    var valid_reaction = Reaction("R1", "Valid reaction", 0.0, 1000.0, 0.0)
    valid_reaction.add_metabolite("A", -1.0)
    valid_reaction.add_metabolite("B", 1.0)
    model.add_reaction(valid_reaction)
    
    # Model should be valid
    assert_true(model.validate())
    
    # Add invalid reaction (references non-existent metabolite)
    var invalid_reaction = Reaction("R2", "Invalid reaction", 0.0, 1000.0, 0.0)
    invalid_reaction.add_metabolite("C", -1.0)  # C doesn't exist
    model.add_reaction(invalid_reaction)
    
    # Model should now be invalid
    assert_false(model.validate())
    
    print("✓ Model validation test passed")

fn test_objective_setting():
    """Test objective function setting."""
    print("Testing objective setting...")
    
    # Create demo model
    let parser = SBMLParser()
    var model = MetabolicModel("objective_test", "Objective Test")
    parser._create_demo_model(model)
    
    # Set objective to biomass reaction
    model.set_objective("BIOMASS", 1.0)
    
    # Verify objective was set
    var biomass_index = -1
    for i in range(len(model.reaction_ids)):
        if model.reaction_ids[i] == "BIOMASS":
            biomass_index = i
            break
    
    assert_true(biomass_index >= 0)
    assert_equal(model.objective[biomass_index], 1.0)
    
    # Verify other objectives are zero
    for i in range(len(model.objective)):
        if i != biomass_index:
            assert_equal(model.objective[i], 0.0)
    
    print("✓ Objective setting test passed")

fn test_performance_benchmark():
    """Test performance of FBA solver."""
    print("Testing FBA solver performance...")
    
    # Create demo model
    let parser = SBMLParser()
    var model = MetabolicModel("perf_test", "Performance Test")
    parser._create_demo_model(model)
    
    let solver = FBASolver()
    let num_runs = 10
    
    import time
    let start_time = time.time()
    
    for i in range(num_runs):
        let result = solver.solve(model, "BIOMASS")
        assert_equal(result.status, "optimal")
    
    let total_time = time.time() - start_time
    let avg_time = total_time / num_runs
    
    print(f"Average solve time: {avg_time * 1000:.2f} ms")
    print(f"Solves per second: {num_runs / total_time:.1f}")
    
    # Performance should be reasonable (less than 100ms per solve for demo model)
    assert_true(avg_time < 0.1)
    
    print("✓ Performance benchmark passed")

# Test runner
fn run_all_tests():
    """Run all tests."""
    print("Running Mojo Metabolic Core Tests")
    print("=" * 40)
    
    test_metabolic_model_creation()
    test_sbml_parser()
    test_fba_solver()
    test_fba_constraints()
    test_sparse_matrix()
    test_reaction_properties()
    test_model_validation()
    test_objective_setting()
    test_performance_benchmark()
    
    print("=" * 40)
    print("All tests passed! ✓")

# Helper assertion functions
fn assert_true(condition: Bool):
    if not condition:
        raise Error("Assertion failed: expected True")

fn assert_false(condition: Bool):
    if condition:
        raise Error("Assertion failed: expected False")

fn assert_equal[T: AnyType](actual: T, expected: T):
    if actual != expected:
        raise Error(f"Assertion failed: expected {expected}, got {actual}")

# Main entry point for testing
fn main():
    run_all_tests()
