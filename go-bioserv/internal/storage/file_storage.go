package storage

import (
	"encoding/json"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"sync"
	"time"

	"github.com/gem-fusion/go-bioserv/internal/models"
	"github.com/google/uuid"
)

// FileStorage implements file-based storage for models and jobs
type FileStorage struct {
	basePath   string
	modelsPath string
	jobsPath   string
	mu         sync.RWMutex
}

// NewFileStorage creates a new file storage instance
func NewFileStorage(basePath string) (*FileStorage, error) {
	modelsPath := filepath.Join(basePath, "models")
	jobsPath := filepath.Join(basePath, "jobs")

	// Create directories if they don't exist
	if err := os.MkdirAll(modelsPath, 0755); err != nil {
		return nil, fmt.Errorf("failed to create models directory: %w", err)
	}
	if err := os.MkdirAll(jobsPath, 0755); err != nil {
		return nil, fmt.Errorf("failed to create jobs directory: %w", err)
	}

	return &FileStorage{
		basePath:   basePath,
		modelsPath: modelsPath,
		jobsPath:   jobsPath,
	}, nil
}

// SaveModel saves a metabolic model to storage
func (fs *FileStorage) SaveModel(model *models.MetabolicModel) error {
	fs.mu.Lock()
	defer fs.mu.Unlock()

	modelFile := filepath.Join(fs.modelsPath, model.ID+".json")
	data, err := json.MarshalIndent(model, "", "  ")
	if err != nil {
		return fmt.Errorf("failed to marshal model: %w", err)
	}

	if err := os.WriteFile(modelFile, data, 0644); err != nil {
		return fmt.Errorf("failed to write model file: %w", err)
	}

	return nil
}

// GetModel retrieves a model by ID
func (fs *FileStorage) GetModel(modelID string) (*models.MetabolicModel, error) {
	fs.mu.RLock()
	defer fs.mu.RUnlock()

	modelFile := filepath.Join(fs.modelsPath, modelID+".json")
	data, err := os.ReadFile(modelFile)
	if err != nil {
		if os.IsNotExist(err) {
			return nil, fmt.Errorf("model not found: %s", modelID)
		}
		return nil, fmt.Errorf("failed to read model file: %w", err)
	}

	var model models.MetabolicModel
	if err := json.Unmarshal(data, &model); err != nil {
		return nil, fmt.Errorf("failed to unmarshal model: %w", err)
	}

	return &model, nil
}

// ListModels returns all models
func (fs *FileStorage) ListModels() ([]models.MetabolicModel, error) {
	fs.mu.RLock()
	defer fs.mu.RUnlock()

	files, err := os.ReadDir(fs.modelsPath)
	if err != nil {
		return nil, fmt.Errorf("failed to read models directory: %w", err)
	}

	var models []models.MetabolicModel
	for _, file := range files {
		if filepath.Ext(file.Name()) == ".json" {
			modelID := file.Name()[:len(file.Name())-5] // Remove .json extension
			model, err := fs.GetModel(modelID)
			if err != nil {
				continue // Skip invalid models
			}
			models = append(models, *model)
		}
	}

	return models, nil
}

// SaveModelFile saves the actual model file (SBML/JSON)
func (fs *FileStorage) SaveModelFile(modelID string, filename string, reader io.Reader) (string, error) {
	fs.mu.Lock()
	defer fs.mu.Unlock()

	// Create model-specific directory
	modelDir := filepath.Join(fs.modelsPath, modelID)
	if err := os.MkdirAll(modelDir, 0755); err != nil {
		return "", fmt.Errorf("failed to create model directory: %w", err)
	}

	filePath := filepath.Join(modelDir, filename)
	file, err := os.Create(filePath)
	if err != nil {
		return "", fmt.Errorf("failed to create model file: %w", err)
	}
	defer file.Close()

	if _, err := io.Copy(file, reader); err != nil {
		return "", fmt.Errorf("failed to write model file: %w", err)
	}

	return filePath, nil
}

// SaveJob saves a job to storage
func (fs *FileStorage) SaveJob(job *models.Job) error {
	fs.mu.Lock()
	defer fs.mu.Unlock()

	jobFile := filepath.Join(fs.jobsPath, job.ID+".json")
	data, err := json.MarshalIndent(job, "", "  ")
	if err != nil {
		return fmt.Errorf("failed to marshal job: %w", err)
	}

	if err := os.WriteFile(jobFile, data, 0644); err != nil {
		return fmt.Errorf("failed to write job file: %w", err)
	}

	return nil
}

// GetJob retrieves a job by ID
func (fs *FileStorage) GetJob(jobID string) (*models.Job, error) {
	fs.mu.RLock()
	defer fs.mu.RUnlock()

	jobFile := filepath.Join(fs.jobsPath, jobID+".json")
	data, err := os.ReadFile(jobFile)
	if err != nil {
		if os.IsNotExist(err) {
			return nil, fmt.Errorf("job not found: %s", jobID)
		}
		return nil, fmt.Errorf("failed to read job file: %w", err)
	}

	var job models.Job
	if err := json.Unmarshal(data, &job); err != nil {
		return nil, fmt.Errorf("failed to unmarshal job: %w", err)
	}

	return &job, nil
}

// UpdateJob updates a job in storage
func (fs *FileStorage) UpdateJob(job *models.Job) error {
	job.UpdatedAt = time.Now()
	return fs.SaveJob(job)
}

// CreateModel creates a new model with generated ID
func (fs *FileStorage) CreateModel(name, description, format string, metadata map[string]string) (*models.MetabolicModel, error) {
	model := &models.MetabolicModel{
		ID:          uuid.New().String(),
		Name:        name,
		Description: description,
		Format:      format,
		Metadata:    metadata,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}

	if err := fs.SaveModel(model); err != nil {
		return nil, err
	}

	return model, nil
}

// CreateJob creates a new job with generated ID
func (fs *FileStorage) CreateJob(modelID, jobType string, input map[string]any) (*models.Job, error) {
	job := &models.Job{
		ID:        uuid.New().String(),
		ModelID:   modelID,
		Type:      jobType,
		Status:    models.JobStatusPending,
		Input:     input,
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	if err := fs.SaveJob(job); err != nil {
		return nil, err
	}

	return job, nil
}
