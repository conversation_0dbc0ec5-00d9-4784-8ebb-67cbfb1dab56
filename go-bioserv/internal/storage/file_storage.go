package storage

import (
	"encoding/json"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"sort"
	"sync"
	"time"

	"github.com/gem-fusion/go-bioserv/internal/models"
	"github.com/google/uuid"
)

// FileStorage implements file-based storage for models and jobs
type FileStorage struct {
	basePath       string
	modelsPath     string
	jobsPath       string
	usersPath      string
	sessionsPath   string
	activitiesPath string
	mu             sync.RWMutex
}

// NewFileStorage creates a new file storage instance
func NewFileStorage(basePath string) (*FileStorage, error) {
	modelsPath := filepath.Join(basePath, "models")
	jobsPath := filepath.Join(basePath, "jobs")
	usersPath := filepath.Join(basePath, "users")
	sessionsPath := filepath.Join(basePath, "sessions")
	activitiesPath := filepath.Join(basePath, "activities")

	// Create directories if they don't exist
	if err := os.MkdirAll(modelsPath, 0755); err != nil {
		return nil, fmt.Errorf("failed to create models directory: %w", err)
	}
	if err := os.MkdirAll(jobsPath, 0755); err != nil {
		return nil, fmt.Errorf("failed to create jobs directory: %w", err)
	}
	if err := os.MkdirAll(usersPath, 0755); err != nil {
		return nil, fmt.Errorf("failed to create users directory: %w", err)
	}
	if err := os.MkdirAll(sessionsPath, 0755); err != nil {
		return nil, fmt.Errorf("failed to create sessions directory: %w", err)
	}
	if err := os.MkdirAll(activitiesPath, 0755); err != nil {
		return nil, fmt.Errorf("failed to create activities directory: %w", err)
	}

	return &FileStorage{
		basePath:       basePath,
		modelsPath:     modelsPath,
		jobsPath:       jobsPath,
		usersPath:      usersPath,
		sessionsPath:   sessionsPath,
		activitiesPath: activitiesPath,
	}, nil
}

// SaveModel saves a metabolic model to storage
func (fs *FileStorage) SaveModel(model *models.MetabolicModel) error {
	fs.mu.Lock()
	defer fs.mu.Unlock()

	modelFile := filepath.Join(fs.modelsPath, model.ID+".json")
	data, err := json.MarshalIndent(model, "", "  ")
	if err != nil {
		return fmt.Errorf("failed to marshal model: %w", err)
	}

	if err := os.WriteFile(modelFile, data, 0644); err != nil {
		return fmt.Errorf("failed to write model file: %w", err)
	}

	return nil
}

// GetModel retrieves a model by ID
func (fs *FileStorage) GetModel(modelID string) (*models.MetabolicModel, error) {
	fs.mu.RLock()
	defer fs.mu.RUnlock()

	modelFile := filepath.Join(fs.modelsPath, modelID+".json")
	data, err := os.ReadFile(modelFile)
	if err != nil {
		if os.IsNotExist(err) {
			return nil, fmt.Errorf("model not found: %s", modelID)
		}
		return nil, fmt.Errorf("failed to read model file: %w", err)
	}

	var model models.MetabolicModel
	if err := json.Unmarshal(data, &model); err != nil {
		return nil, fmt.Errorf("failed to unmarshal model: %w", err)
	}

	return &model, nil
}

// ListModels returns all models
func (fs *FileStorage) ListModels() ([]models.MetabolicModel, error) {
	fs.mu.RLock()
	defer fs.mu.RUnlock()

	files, err := os.ReadDir(fs.modelsPath)
	if err != nil {
		return nil, fmt.Errorf("failed to read models directory: %w", err)
	}

	var models []models.MetabolicModel
	for _, file := range files {
		if filepath.Ext(file.Name()) == ".json" {
			modelID := file.Name()[:len(file.Name())-5] // Remove .json extension
			model, err := fs.GetModel(modelID)
			if err != nil {
				continue // Skip invalid models
			}
			models = append(models, *model)
		}
	}

	return models, nil
}

// SaveModelFile saves the actual model file (SBML/JSON)
func (fs *FileStorage) SaveModelFile(modelID string, filename string, reader io.Reader) (string, error) {
	fs.mu.Lock()
	defer fs.mu.Unlock()

	// Create model-specific directory
	modelDir := filepath.Join(fs.modelsPath, modelID)
	if err := os.MkdirAll(modelDir, 0755); err != nil {
		return "", fmt.Errorf("failed to create model directory: %w", err)
	}

	filePath := filepath.Join(modelDir, filename)
	file, err := os.Create(filePath)
	if err != nil {
		return "", fmt.Errorf("failed to create model file: %w", err)
	}
	defer file.Close()

	if _, err := io.Copy(file, reader); err != nil {
		return "", fmt.Errorf("failed to write model file: %w", err)
	}

	return filePath, nil
}

// SaveJob saves a job to storage
func (fs *FileStorage) SaveJob(job *models.Job) error {
	fs.mu.Lock()
	defer fs.mu.Unlock()

	jobFile := filepath.Join(fs.jobsPath, job.ID+".json")
	data, err := json.MarshalIndent(job, "", "  ")
	if err != nil {
		return fmt.Errorf("failed to marshal job: %w", err)
	}

	if err := os.WriteFile(jobFile, data, 0644); err != nil {
		return fmt.Errorf("failed to write job file: %w", err)
	}

	return nil
}

// GetJob retrieves a job by ID
func (fs *FileStorage) GetJob(jobID string) (*models.Job, error) {
	fs.mu.RLock()
	defer fs.mu.RUnlock()

	jobFile := filepath.Join(fs.jobsPath, jobID+".json")
	data, err := os.ReadFile(jobFile)
	if err != nil {
		if os.IsNotExist(err) {
			return nil, fmt.Errorf("job not found: %s", jobID)
		}
		return nil, fmt.Errorf("failed to read job file: %w", err)
	}

	var job models.Job
	if err := json.Unmarshal(data, &job); err != nil {
		return nil, fmt.Errorf("failed to unmarshal job: %w", err)
	}

	return &job, nil
}

// UpdateJob updates a job in storage
func (fs *FileStorage) UpdateJob(job *models.Job) error {
	job.UpdatedAt = time.Now()
	return fs.SaveJob(job)
}

// CreateModel creates a new model with generated ID
func (fs *FileStorage) CreateModel(name, description, format string, metadata map[string]string) (*models.MetabolicModel, error) {
	model := &models.MetabolicModel{
		ID:          uuid.New().String(),
		Name:        name,
		Description: description,
		Format:      format,
		Metadata:    metadata,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}

	if err := fs.SaveModel(model); err != nil {
		return nil, err
	}

	return model, nil
}

// CreateJob creates a new job with generated ID
func (fs *FileStorage) CreateJob(modelID, jobType string, input map[string]any) (*models.Job, error) {
	job := &models.Job{
		ID:        uuid.New().String(),
		ModelID:   modelID,
		Type:      jobType,
		Status:    models.JobStatusPending,
		Input:     input,
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	if err := fs.SaveJob(job); err != nil {
		return nil, err
	}

	return job, nil
}

// ListJobs returns all jobs
func (fs *FileStorage) ListJobs() ([]models.Job, error) {
	fs.mu.RLock()
	defer fs.mu.RUnlock()

	files, err := os.ReadDir(fs.jobsPath)
	if err != nil {
		return nil, fmt.Errorf("failed to read jobs directory: %w", err)
	}

	var jobs []models.Job
	for _, file := range files {
		if filepath.Ext(file.Name()) == ".json" {
			jobID := file.Name()[:len(file.Name())-5] // Remove .json extension
			job, err := fs.GetJob(jobID)
			if err != nil {
				continue // Skip invalid jobs
			}
			jobs = append(jobs, *job)
		}
	}

	// Sort jobs by creation time (newest first)
	sort.Slice(jobs, func(i, j int) bool {
		return jobs[i].CreatedAt.After(jobs[j].CreatedAt)
	})

	return jobs, nil
}

// User storage methods

// StorageUser represents a user for storage (includes password)
type StorageUser struct {
	ID        string          `json:"id"`
	Username  string          `json:"username"`
	Email     string          `json:"email"`
	Password  string          `json:"password"` // Include password for storage
	FirstName string          `json:"first_name"`
	LastName  string          `json:"last_name"`
	Role      models.UserRole `json:"role"`
	IsActive  bool            `json:"is_active"`
	CreatedAt time.Time       `json:"created_at"`
	UpdatedAt time.Time       `json:"updated_at"`
	LastLogin *time.Time      `json:"last_login,omitempty"`
}

// CreateUser creates a new user
func (fs *FileStorage) CreateUser(user *models.User) error {
	fs.mu.Lock()
	defer fs.mu.Unlock()

	// Convert to storage user (includes password)
	storageUser := StorageUser{
		ID:        user.ID,
		Username:  user.Username,
		Email:     user.Email,
		Password:  user.Password,
		FirstName: user.FirstName,
		LastName:  user.LastName,
		Role:      user.Role,
		IsActive:  user.IsActive,
		CreatedAt: user.CreatedAt,
		UpdatedAt: user.UpdatedAt,
		LastLogin: user.LastLogin,
	}

	userFile := filepath.Join(fs.usersPath, user.ID+".json")
	data, err := json.MarshalIndent(storageUser, "", "  ")
	if err != nil {
		return fmt.Errorf("failed to marshal user: %w", err)
	}

	if err := os.WriteFile(userFile, data, 0644); err != nil {
		return fmt.Errorf("failed to write user file: %w", err)
	}

	return nil
}

// GetUser retrieves a user by ID
func (fs *FileStorage) GetUser(userID string) (*models.User, error) {
	fs.mu.RLock()
	defer fs.mu.RUnlock()

	userFile := filepath.Join(fs.usersPath, userID+".json")
	data, err := os.ReadFile(userFile)
	if err != nil {
		if os.IsNotExist(err) {
			return nil, fmt.Errorf("user not found: %s", userID)
		}
		return nil, fmt.Errorf("failed to read user file: %w", err)
	}

	var storageUser StorageUser
	if err := json.Unmarshal(data, &storageUser); err != nil {
		return nil, fmt.Errorf("failed to unmarshal user: %w", err)
	}

	// Convert back to models.User
	user := &models.User{
		ID:        storageUser.ID,
		Username:  storageUser.Username,
		Email:     storageUser.Email,
		Password:  storageUser.Password,
		FirstName: storageUser.FirstName,
		LastName:  storageUser.LastName,
		Role:      storageUser.Role,
		IsActive:  storageUser.IsActive,
		CreatedAt: storageUser.CreatedAt,
		UpdatedAt: storageUser.UpdatedAt,
		LastLogin: storageUser.LastLogin,
	}

	return user, nil
}

// GetUserByUsername retrieves a user by username
func (fs *FileStorage) GetUserByUsername(username string) (*models.User, error) {
	users, err := fs.ListUsers()
	if err != nil {
		return nil, err
	}

	for _, user := range users {
		if user.Username == username {
			return &user, nil
		}
	}

	return nil, fmt.Errorf("user not found: %s", username)
}

// GetUserByEmail retrieves a user by email
func (fs *FileStorage) GetUserByEmail(email string) (*models.User, error) {
	users, err := fs.ListUsers()
	if err != nil {
		return nil, err
	}

	for _, user := range users {
		if user.Email == email {
			return &user, nil
		}
	}

	return nil, fmt.Errorf("user not found: %s", email)
}

// UpdateUser updates a user
func (fs *FileStorage) UpdateUser(user *models.User) error {
	user.UpdatedAt = time.Now()
	return fs.CreateUser(user) // Reuse create method
}

// ListUsers returns all users
func (fs *FileStorage) ListUsers() ([]models.User, error) {
	fs.mu.RLock()
	defer fs.mu.RUnlock()

	files, err := os.ReadDir(fs.usersPath)
	if err != nil {
		return nil, fmt.Errorf("failed to read users directory: %w", err)
	}

	var users []models.User
	for _, file := range files {
		if filepath.Ext(file.Name()) == ".json" {
			userID := file.Name()[:len(file.Name())-5] // Remove .json extension
			user, err := fs.GetUser(userID)
			if err != nil {
				continue // Skip invalid users
			}
			users = append(users, *user)
		}
	}

	return users, nil
}

// Session storage methods

// CreateSession creates a new session
func (fs *FileStorage) CreateSession(session *models.Session) error {
	fs.mu.Lock()
	defer fs.mu.Unlock()

	sessionFile := filepath.Join(fs.sessionsPath, session.ID+".json")
	data, err := json.MarshalIndent(session, "", "  ")
	if err != nil {
		return fmt.Errorf("failed to marshal session: %w", err)
	}

	if err := os.WriteFile(sessionFile, data, 0644); err != nil {
		return fmt.Errorf("failed to write session file: %w", err)
	}

	return nil
}

// DeleteSessionByUserID deletes all sessions for a user
func (fs *FileStorage) DeleteSessionByUserID(userID string) error {
	fs.mu.Lock()
	defer fs.mu.Unlock()

	files, err := os.ReadDir(fs.sessionsPath)
	if err != nil {
		return fmt.Errorf("failed to read sessions directory: %w", err)
	}

	for _, file := range files {
		if filepath.Ext(file.Name()) == ".json" {
			sessionFile := filepath.Join(fs.sessionsPath, file.Name())
			data, err := os.ReadFile(sessionFile)
			if err != nil {
				continue
			}

			var session models.Session
			if err := json.Unmarshal(data, &session); err != nil {
				continue
			}

			if session.UserID == userID {
				os.Remove(sessionFile)
			}
		}
	}

	return nil
}

// CreateUserActivity creates a new user activity log
func (fs *FileStorage) CreateUserActivity(activity *models.UserActivity) error {
	fs.mu.Lock()
	defer fs.mu.Unlock()

	activityFile := filepath.Join(fs.activitiesPath, activity.ID+".json")
	data, err := json.MarshalIndent(activity, "", "  ")
	if err != nil {
		return fmt.Errorf("failed to marshal activity: %w", err)
	}

	if err := os.WriteFile(activityFile, data, 0644); err != nil {
		return fmt.Errorf("failed to write activity file: %w", err)
	}

	return nil
}
