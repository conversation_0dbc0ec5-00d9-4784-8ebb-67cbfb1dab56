package jobs

import (
	"context"
	"fmt"
	"log"
	"sync"
	"time"

	"github.com/gem-fusion/go-bioserv/internal/models"
	"github.com/gem-fusion/go-bioserv/internal/storage"
)

// JobQueue manages job execution
type JobQueue struct {
	storage    *storage.FileStorage
	workers    int
	jobChan    chan *models.Job
	ctx        context.Context
	cancel     context.CancelFunc
	wg         sync.WaitGroup
	processors map[string]JobProcessor
}

// JobProcessor defines the interface for job processors
type JobProcessor interface {
	Process(ctx context.Context, job *models.Job) error
}

// NewJobQueue creates a new job queue
func NewJobQueue(storage *storage.FileStorage, workers int) *JobQueue {
	ctx, cancel := context.WithCancel(context.Background())

	jq := &JobQueue{
		storage:    storage,
		workers:    workers,
		jobChan:    make(chan *models.Job, 100), // Buffer for 100 jobs
		ctx:        ctx,
		cancel:     cancel,
		processors: make(map[string]JobProcessor),
	}

	return jq
}

// RegisterProcessor registers a job processor for a specific job type
func (jq *JobQueue) RegisterProcessor(jobType string, processor JobProcessor) {
	jq.processors[jobType] = processor
}

// Start starts the job queue workers
func (jq *JobQueue) Start() {
	for i := 0; i < jq.workers; i++ {
		jq.wg.Add(1)
		go jq.worker(i)
	}
	log.Printf("Started %d job queue workers", jq.workers)
}

// Stop stops the job queue
func (jq *JobQueue) Stop() {
	jq.cancel()
	close(jq.jobChan)
	jq.wg.Wait()
	log.Println("Job queue stopped")
}

// SubmitJob submits a job to the queue
func (jq *JobQueue) SubmitJob(job *models.Job) error {
	select {
	case jq.jobChan <- job:
		return nil
	case <-jq.ctx.Done():
		return fmt.Errorf("job queue is shutting down")
	default:
		return fmt.Errorf("job queue is full")
	}
}

// worker processes jobs from the queue
func (jq *JobQueue) worker(id int) {
	defer jq.wg.Done()

	log.Printf("Worker %d started", id)

	for {
		select {
		case job, ok := <-jq.jobChan:
			if !ok {
				log.Printf("Worker %d stopping", id)
				return
			}
			jq.processJob(job)
		case <-jq.ctx.Done():
			log.Printf("Worker %d stopping due to context cancellation", id)
			return
		}
	}
}

// processJob processes a single job
func (jq *JobQueue) processJob(job *models.Job) {
	log.Printf("Processing job %s of type %s", job.ID, job.Type)

	// Update job status to running
	job.Status = models.JobStatusRunning
	now := time.Now()
	job.StartedAt = &now
	job.UpdatedAt = now

	if err := jq.storage.UpdateJob(job); err != nil {
		log.Printf("Failed to update job status to running: %v", err)
		return
	}

	// Get processor for job type
	processor, exists := jq.processors[job.Type]
	if !exists {
		jq.failJob(job, fmt.Sprintf("no processor found for job type: %s", job.Type))
		return
	}

	// Process the job
	err := processor.Process(jq.ctx, job)

	// Update job status based on result
	now = time.Now()
	job.EndedAt = &now
	job.UpdatedAt = now

	if err != nil {
		jq.failJob(job, err.Error())
	} else {
		job.Status = models.JobStatusCompleted
		if err := jq.storage.UpdateJob(job); err != nil {
			log.Printf("Failed to update job status to completed: %v", err)
		} else {
			log.Printf("Job %s completed successfully", job.ID)
		}
	}
}

// failJob marks a job as failed
func (jq *JobQueue) failJob(job *models.Job, errorMsg string) {
	job.Status = models.JobStatusFailed
	job.Error = errorMsg
	job.UpdatedAt = time.Now()

	if err := jq.storage.UpdateJob(job); err != nil {
		log.Printf("Failed to update job status to failed: %v", err)
	}

	log.Printf("Job %s failed: %s", job.ID, errorMsg)
}

// GetJobStatus returns the current status of a job
func (jq *JobQueue) GetJobStatus(jobID string) (*models.Job, error) {
	return jq.storage.GetJob(jobID)
}

// FBAProcessor implements JobProcessor for FBA jobs
type FBAProcessor struct {
	storage    *storage.FileStorage
	mojoClient MojoClient
}

// MojoClient defines the interface for communicating with Mojo compute engine
type MojoClient interface {
	SubmitFBA(modelPath string, request *models.FBARequest) (*models.FBAResult, error)
	SubmitEMA(modelPath string, request *models.EMARequest) (*models.EMAResult, error)
}

// NewFBAProcessor creates a new FBA processor
func NewFBAProcessor(storage *storage.FileStorage, mojoClient MojoClient) *FBAProcessor {
	return &FBAProcessor{
		storage:    storage,
		mojoClient: mojoClient,
	}
}

// Process processes an FBA job
func (fp *FBAProcessor) Process(ctx context.Context, job *models.Job) error {
	// Get the model
	model, err := fp.storage.GetModel(job.ModelID)
	if err != nil {
		return fmt.Errorf("failed to get model: %w", err)
	}

	// Parse FBA request from job input
	fbaRequest, err := fp.parseFBARequest(job.Input)
	if err != nil {
		return fmt.Errorf("failed to parse FBA request: %w", err)
	}

	// Submit to Mojo compute engine
	result, err := fp.mojoClient.SubmitFBA(model.FilePath, fbaRequest)
	if err != nil {
		return fmt.Errorf("FBA computation failed: %w", err)
	}

	// Store result in job output
	job.Output = map[string]any{
		"objective_value": result.ObjectiveValue,
		"flux_values":     result.FluxValues,
		"status":          result.Status,
		"solver_time":     result.SolverTime,
	}

	return nil
}

// parseFBARequest parses FBA request from job input
func (fp *FBAProcessor) parseFBARequest(input map[string]any) (*models.FBARequest, error) {
	request := &models.FBARequest{}

	if obj, ok := input["objective"].(string); ok {
		request.Objective = obj
	} else {
		return nil, fmt.Errorf("missing or invalid objective")
	}

	if constraints, ok := input["constraints"].(map[string]any); ok {
		request.Constraints = make(map[string]float64)
		for k, v := range constraints {
			if val, ok := v.(float64); ok {
				request.Constraints[k] = val
			}
		}
	}

	if options, ok := input["options"].(map[string]any); ok {
		request.Options = options
	}

	return request, nil
}
