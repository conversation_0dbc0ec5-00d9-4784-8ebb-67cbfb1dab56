package jobs

import (
	"context"
	"fmt"
	"log"
	"time"

	"github.com/gem-fusion/go-bioserv/internal/models"
	"github.com/gem-fusion/go-bioserv/internal/storage"
)

// SchusterProcessor handles Network Decomposition (Schuster) jobs
type SchusterProcessor struct {
	storage    *storage.FileStorage
	mojoClient MojoClient
}

// NewSchusterProcessor creates a new Schuster processor
func NewSchusterProcessor(storage *storage.FileStorage, mojoClient MojoClient) *SchusterProcessor {
	return &SchusterProcessor{
		storage:    storage,
		mojoClient: mojoClient,
	}
}

// Process processes a Schuster decomposition job
func (p *SchusterProcessor) Process(ctx context.Context, job *models.Job) error {
	log.Printf("Processing Network Decomposition job: %s", job.ID)

	// Update job status to running
	job.Status = models.JobStatusRunning
	job.UpdatedAt = time.Now()
	if err := p.storage.SaveJob(job); err != nil {
		return fmt.Errorf("failed to update job status: %w", err)
	}

	// Get the model
	model, err := p.storage.GetModel(job.ModelID)
	if err != nil {
		return fmt.Errorf("failed to get model: %w", err)
	}

	// Parse Schuster request from job input
	schusterRequest, err := p.parseSchusterRequest(job.Input)
	if err != nil {
		job.Status = models.JobStatusFailed
		job.Error = fmt.Sprintf("Failed to parse Network Decomposition request: %v", err)
		job.UpdatedAt = time.Now()
		p.storage.SaveJob(job)
		return fmt.Errorf("failed to parse Schuster request: %w", err)
	}

	log.Printf("Submitting Network Decomposition request for model: %s", model.Name)

	// For now, create a mock result since we need to implement the actual algorithm
	// TODO: Implement actual network decomposition algorithm in Mojo
	schusterResult := &models.SchusterResult{
		Subnetworks: []models.Subnetwork{
			{
				ID:    1,
				Nodes: []string{"glucose", "g6p", "f6p", "fbp"},
				Connections: []models.Connection{
					{From: "glucose", To: "g6p", Weight: 0.8},
					{From: "g6p", To: "f6p", Weight: 0.7},
					{From: "f6p", To: "fbp", Weight: 0.6},
				},
				Size: 4,
			},
			{
				ID:    2,
				Nodes: []string{"pep", "pyruvate", "acetyl_coa"},
				Connections: []models.Connection{
					{From: "pep", To: "pyruvate", Weight: 0.9},
					{From: "pyruvate", To: "acetyl_coa", Weight: 0.8},
				},
				Size: 3,
			},
		},
		TotalSubnetworks:      2,
		ConnectivityThreshold: schusterRequest.ConnectivityThreshold,
		ComputationTime:       0.125, // Mock computation time
		Status:                "completed",
		Statistics: map[string]any{
			"original_nodes":     15,
			"filtered_nodes":     7,
			"original_edges":     20,
			"filtered_edges":     5,
			"largest_component":  4,
			"smallest_component": 3,
		},
	}

	// Store result in job output
	job.Output = map[string]any{
		"subnetworks":            schusterResult.Subnetworks,
		"total_subnetworks":      schusterResult.TotalSubnetworks,
		"connectivity_threshold": schusterResult.ConnectivityThreshold,
		"computation_time":       schusterResult.ComputationTime,
		"status":                 schusterResult.Status,
		"statistics":             schusterResult.Statistics,
		"connection_type":        schusterRequest.ConnectionType,
		"output_format":          schusterRequest.OutputFormat,
	}

	// Update job status to completed
	job.Status = models.JobStatusCompleted
	job.UpdatedAt = time.Now()

	if err := p.storage.SaveJob(job); err != nil {
		return fmt.Errorf("failed to save job results: %w", err)
	}

	log.Printf("Network Decomposition job completed successfully: %s (found %d subnetworks in %.3fs)",
		job.ID, schusterResult.TotalSubnetworks, schusterResult.ComputationTime)

	return nil
}

// parseSchusterRequest parses Schuster request from job input
func (p *SchusterProcessor) parseSchusterRequest(input map[string]any) (*models.SchusterRequest, error) {
	request := &models.SchusterRequest{
		ConnectivityThreshold: 0.1,        // Default value
		MinSubnetworkSize:     3,          // Default value
		ConnectionType:        "metabolite", // Default value
		OutputFormat:          "subnetworks", // Default value
	}

	if threshold, ok := input["connectivity_threshold"]; ok {
		if val, ok := threshold.(float64); ok {
			request.ConnectivityThreshold = val
		}
	}

	if minSize, ok := input["min_subnetwork_size"]; ok {
		if val, ok := minSize.(float64); ok {
			request.MinSubnetworkSize = int(val)
		} else if val, ok := minSize.(int); ok {
			request.MinSubnetworkSize = val
		}
	}

	if connType, ok := input["connection_type"]; ok {
		if val, ok := connType.(string); ok {
			request.ConnectionType = val
		}
	}

	if outputFormat, ok := input["output_format"]; ok {
		if val, ok := outputFormat.(string); ok {
			request.OutputFormat = val
		}
	}

	return request, nil
}

// GetJobType returns the job type this processor handles
func (p *SchusterProcessor) GetJobType() string {
	return "schuster"
}
