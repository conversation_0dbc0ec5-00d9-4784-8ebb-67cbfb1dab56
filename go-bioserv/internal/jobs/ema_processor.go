package jobs

import (
	"context"
	"fmt"
	"log"
	"time"

	"github.com/gem-fusion/go-bioserv/internal/models"
	"github.com/gem-fusion/go-bioserv/internal/storage"
)

// EMAProcessor handles Elementary Mode Analysis jobs
type EMAProcessor struct {
	storage    *storage.FileStorage
	mojoClient MojoClient
}

// NewEMAProcessor creates a new EMA processor
func NewEMAProcessor(storage *storage.FileStorage, mojoClient MojoClient) *EMAProcessor {
	return &EMAProcessor{
		storage:    storage,
		mojoClient: mojoClient,
	}
}

// Process processes an EMA job
func (p *EMAProcessor) Process(ctx context.Context, job *models.Job) error {
	log.Printf("Processing EMA job: %s", job.ID)

	// Update job status to running
	job.Status = models.JobStatusRunning
	job.UpdatedAt = time.Now()
	if err := p.storage.SaveJob(job); err != nil {
		return fmt.Errorf("failed to update job status: %w", err)
	}

	// Get the model
	model, err := p.storage.GetModel(job.ModelID)
	if err != nil {
		return fmt.Errorf("failed to get model: %w", err)
	}

	// Parse EMA request from job input
	emaRequest, err := p.parseEMARequest(job.Input)
	if err != nil {
		job.Status = models.JobStatusFailed
		job.Error = fmt.Sprintf("Failed to parse EMA request: %v", err)
		job.UpdatedAt = time.Now()
		p.storage.SaveJob(job)
		return fmt.Errorf("failed to parse EMA request: %w", err)
	}

	log.Printf("Submitting EMA request for model: %s", model.Name)

	// Submit to Mojo compute engine
	emaResult, err := p.mojoClient.SubmitEMA(model.FilePath, emaRequest)
	if err != nil {
		// Update job status to failed
		job.Status = models.JobStatusFailed
		job.Error = fmt.Sprintf("Mojo computation failed: %v", err)
		job.UpdatedAt = time.Now()
		p.storage.SaveJob(job)
		return fmt.Errorf("mojo computation failed: %w", err)
	}

	// Store result in job output
	job.Output = map[string]any{
		"elementary_modes":   emaResult.ElementaryModes,
		"num_modes":          emaResult.NumModes,
		"computation_time":   emaResult.ComputationTime,
		"status":             emaResult.Status,
		"statistics":         emaResult.Statistics,
		"internal_reactions": emaResult.InternalReactions,
		"exchange_reactions": emaResult.ExchangeReactions,
	}

	// Update job status to completed
	job.Status = models.JobStatusCompleted
	job.UpdatedAt = time.Now()

	if err := p.storage.SaveJob(job); err != nil {
		return fmt.Errorf("failed to save job results: %w", err)
	}

	log.Printf("EMA job completed successfully: %s (found %d modes in %.3fs)",
		job.ID, emaResult.NumModes, emaResult.ComputationTime)

	return nil
}

// parseEMARequest parses EMA request from job input
func (p *EMAProcessor) parseEMARequest(input map[string]any) (*models.EMARequest, error) {
	request := &models.EMARequest{
		MaxModes:  1000,  // Default value
		Tolerance: 1e-10, // Default value
	}

	if maxModes, ok := input["max_modes"]; ok {
		if val, ok := maxModes.(float64); ok {
			request.MaxModes = int(val)
		} else if val, ok := maxModes.(int); ok {
			request.MaxModes = val
		}
	}

	if tolerance, ok := input["tolerance"]; ok {
		if val, ok := tolerance.(float64); ok {
			request.Tolerance = val
		}
	}

	if options, ok := input["options"].(map[string]any); ok {
		request.Options = options
	}

	return request, nil
}

// GetJobType returns the job type this processor handles
func (p *EMAProcessor) GetJobType() string {
	return "ema"
}
