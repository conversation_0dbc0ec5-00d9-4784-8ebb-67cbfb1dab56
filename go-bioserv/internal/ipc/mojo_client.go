package ipc

import (
	"encoding/json"
	"fmt"
	"os"
	"os/exec"
	"path/filepath"
	"time"

	"github.com/gem-fusion/go-bioserv/internal/models"
	"github.com/google/uuid"
)

// FileMojoClient implements file-based IPC with Mojo compute engine
type FileMojoClient struct {
	ipcDir     string
	mojoPath   string
	timeout    time.Duration
}

// NewFileMojoClient creates a new file-based Mojo client
func NewFileMojoClient(ipcDir, mojoPath string, timeout time.Duration) (*FileMojoClient, error) {
	// Create IPC directory if it doesn't exist
	if err := os.MkdirAll(ipcDir, 0755); err != nil {
		return nil, fmt.Errorf("failed to create IPC directory: %w", err)
	}

	return &FileMojoClient{
		ipcDir:   ipcDir,
		mojoPath: mojoPath,
		timeout:  timeout,
	}, nil
}

// SubmitFBA submits an FBA request to the Mojo compute engine
func (c *FileMojoClient) SubmitFBA(modelPath string, request *models.FBARequest) (*models.FBAResult, error) {
	// Generate unique request ID
	requestID := uuid.New().String()
	
	// Create request file
	requestFile := filepath.Join(c.ipcDir, fmt.Sprintf("fba_request_%s.json", requestID))
	responseFile := filepath.Join(c.ipcDir, fmt.Sprintf("fba_response_%s.json", requestID))
	
	// Prepare request data
	requestData := map[string]any{
		"request_id":  requestID,
		"model_path":  modelPath,
		"objective":   request.Objective,
		"constraints": request.Constraints,
		"options":     request.Options,
	}
	
	// Write request file
	data, err := json.MarshalIndent(requestData, "", "  ")
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request: %w", err)
	}
	
	if err := os.WriteFile(requestFile, data, 0644); err != nil {
		return nil, fmt.Errorf("failed to write request file: %w", err)
	}
	
	// Cleanup request file when done
	defer os.Remove(requestFile)
	defer os.Remove(responseFile)
	
	// Execute Mojo compute engine
	cmd := exec.Command("mojo", "run", c.mojoPath, requestFile, responseFile)
	if err := cmd.Run(); err != nil {
		return nil, fmt.Errorf("failed to execute Mojo compute engine: %w", err)
	}
	
	// Wait for response file with timeout
	start := time.Now()
	for {
		if time.Since(start) > c.timeout {
			return nil, fmt.Errorf("timeout waiting for Mojo response")
		}
		
		if _, err := os.Stat(responseFile); err == nil {
			break
		}
		
		time.Sleep(100 * time.Millisecond)
	}
	
	// Read response file
	responseData, err := os.ReadFile(responseFile)
	if err != nil {
		return nil, fmt.Errorf("failed to read response file: %w", err)
	}
	
	// Parse response
	var response struct {
		Success bool                `json:"success"`
		Error   string              `json:"error,omitempty"`
		Result  *models.FBAResult   `json:"result,omitempty"`
	}
	
	if err := json.Unmarshal(responseData, &response); err != nil {
		return nil, fmt.Errorf("failed to unmarshal response: %w", err)
	}
	
	if !response.Success {
		return nil, fmt.Errorf("Mojo computation failed: %s", response.Error)
	}
	
	if response.Result == nil {
		return nil, fmt.Errorf("no result in response")
	}
	
	return response.Result, nil
}

// MockMojoClient implements a mock Mojo client for testing
type MockMojoClient struct {
	delay time.Duration
}

// NewMockMojoClient creates a new mock Mojo client
func NewMockMojoClient(delay time.Duration) *MockMojoClient {
	return &MockMojoClient{delay: delay}
}

// SubmitFBA simulates FBA computation with mock results
func (c *MockMojoClient) SubmitFBA(modelPath string, request *models.FBARequest) (*models.FBAResult, error) {
	// Simulate computation time
	if c.delay > 0 {
		time.Sleep(c.delay)
	}
	
	// Return mock results
	result := &models.FBAResult{
		ObjectiveValue: 0.8739215,
		FluxValues: map[string]float64{
			"BIOMASS_Ecoli_core_w_GAM": 0.8739215,
			"EX_glc__D_e":              -10.0,
			"EX_o2_e":                  -21.799493,
			"EX_co2_e":                 22.809833,
			"EX_h2o_e":                 29.175827,
			"EX_h_e":                   17.530865,
			"EX_pi_e":                  -3.2149,
		},
		Status:     "optimal",
		SolverTime: 0.045,
	}
	
	return result, nil
}

// HealthCheck checks if the Mojo compute engine is available
func (c *FileMojoClient) HealthCheck() error {
	// Try to execute a simple Mojo command
	cmd := exec.Command("mojo", "--version")
	if err := cmd.Run(); err != nil {
		return fmt.Errorf("Mojo not available: %w", err)
	}
	
	// Check if the compute engine script exists
	if _, err := os.Stat(c.mojoPath); err != nil {
		return fmt.Errorf("Mojo compute engine script not found: %w", err)
	}
	
	return nil
}

// GetStatus returns the status of the Mojo compute engine
func (c *FileMojoClient) GetStatus() map[string]any {
	status := map[string]any{
		"type":     "file_based_ipc",
		"ipc_dir":  c.ipcDir,
		"timeout":  c.timeout.String(),
	}
	
	if err := c.HealthCheck(); err != nil {
		status["healthy"] = false
		status["error"] = err.Error()
	} else {
		status["healthy"] = true
	}
	
	return status
}
