package auth

import (
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/gem-fusion/go-bioserv/internal/models"
)

// Middleware provides authentication middleware
type Middleware struct {
	authService *Service
}

// NewMiddleware creates a new authentication middleware
func NewMiddleware(authService *Service) *Middleware {
	return &Middleware{
		authService: authService,
	}
}

// RequireAuth middleware that requires authentication
func (m *Middleware) RequireAuth() gin.HandlerFunc {
	return func(c *gin.Context) {
		token := extractToken(c)
		if token == "" {
			c.JSON(http.StatusUnauthorized, models.APIResponse{
				Success: false,
				Error:   "Authentication required",
			})
			c.Abort()
			return
		}

		user, err := m.authService.GetUserFromToken(token)
		if err != nil {
			c.JSON(http.StatusUnauthorized, models.APIResponse{
				Success: false,
				Error:   "Invalid or expired token",
			})
			c.Abort()
			return
		}

		// Store user in context
		c.Set("user", user)
		c.Set("user_id", user.ID)
		c.Set("username", user.Username)
		c.Set("user_role", user.Role)

		c.Next()
	}
}

// RequireRole middleware that requires a specific role
func (m *Middleware) RequireRole(requiredRole models.UserRole) gin.HandlerFunc {
	return func(c *gin.Context) {
		user, exists := c.Get("user")
		if !exists {
			c.JSON(http.StatusUnauthorized, models.APIResponse{
				Success: false,
				Error:   "Authentication required",
			})
			c.Abort()
			return
		}

		userObj, ok := user.(*models.User)
		if !ok {
			c.JSON(http.StatusInternalServerError, models.APIResponse{
				Success: false,
				Error:   "Invalid user context",
			})
			c.Abort()
			return
		}

		if userObj.Role != requiredRole && userObj.Role != models.UserRoleAdmin {
			c.JSON(http.StatusForbidden, models.APIResponse{
				Success: false,
				Error:   "Insufficient permissions",
			})
			c.Abort()
			return
		}

		c.Next()
	}
}

// RequirePermission middleware that requires a specific permission
func (m *Middleware) RequirePermission(permission string) gin.HandlerFunc {
	return func(c *gin.Context) {
		user, exists := c.Get("user")
		if !exists {
			c.JSON(http.StatusUnauthorized, models.APIResponse{
				Success: false,
				Error:   "Authentication required",
			})
			c.Abort()
			return
		}

		userObj, ok := user.(*models.User)
		if !ok {
			c.JSON(http.StatusInternalServerError, models.APIResponse{
				Success: false,
				Error:   "Invalid user context",
			})
			c.Abort()
			return
		}

		if !userObj.HasPermission(permission) {
			c.JSON(http.StatusForbidden, models.APIResponse{
				Success: false,
				Error:   "Insufficient permissions",
			})
			c.Abort()
			return
		}

		c.Next()
	}
}

// OptionalAuth middleware that optionally authenticates users
func (m *Middleware) OptionalAuth() gin.HandlerFunc {
	return func(c *gin.Context) {
		token := extractToken(c)
		if token != "" {
			user, err := m.authService.GetUserFromToken(token)
			if err == nil {
				// Store user in context if token is valid
				c.Set("user", user)
				c.Set("user_id", user.ID)
				c.Set("username", user.Username)
				c.Set("user_role", user.Role)
			}
		}

		c.Next()
	}
}

// AdminOnly middleware that requires admin role
func (m *Middleware) AdminOnly() gin.HandlerFunc {
	return m.RequireRole(models.UserRoleAdmin)
}

// ResearcherOrAdmin middleware that requires researcher or admin role
func (m *Middleware) ResearcherOrAdmin() gin.HandlerFunc {
	return func(c *gin.Context) {
		user, exists := c.Get("user")
		if !exists {
			c.JSON(http.StatusUnauthorized, models.APIResponse{
				Success: false,
				Error:   "Authentication required",
			})
			c.Abort()
			return
		}

		userObj, ok := user.(*models.User)
		if !ok {
			c.JSON(http.StatusInternalServerError, models.APIResponse{
				Success: false,
				Error:   "Invalid user context",
			})
			c.Abort()
			return
		}

		if userObj.Role != models.UserRoleResearcher && userObj.Role != models.UserRoleAdmin {
			c.JSON(http.StatusForbidden, models.APIResponse{
				Success: false,
				Error:   "Researcher or admin access required",
			})
			c.Abort()
			return
		}

		c.Next()
	}
}

// LogActivity middleware that logs user activities
func (m *Middleware) LogActivity(action string) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Execute the request first
		c.Next()

		// Log activity after request completion
		user, exists := c.Get("user")
		if !exists {
			return // Skip logging for unauthenticated requests
		}

		userObj, ok := user.(*models.User)
		if !ok {
			return
		}

		resource := c.Request.URL.Path
		details := c.Request.Method + " " + resource
		ipAddress := c.ClientIP()
		userAgent := c.Request.UserAgent()

		// Log activity asynchronously
		go func() {
			m.authService.LogActivity(userObj.ID, action, resource, details, ipAddress, userAgent)
		}()
	}
}

// extractToken extracts the JWT token from the request
func extractToken(c *gin.Context) string {
	// Try Authorization header first
	authHeader := c.GetHeader("Authorization")
	if authHeader != "" {
		// Bearer token format
		if strings.HasPrefix(authHeader, "Bearer ") {
			return strings.TrimPrefix(authHeader, "Bearer ")
		}
		// Direct token
		return authHeader
	}

	// Try query parameter
	token := c.Query("token")
	if token != "" {
		return token
	}

	// Try cookie
	cookie, err := c.Cookie("auth_token")
	if err == nil && cookie != "" {
		return cookie
	}

	return ""
}

// GetCurrentUser helper function to get current user from context
func GetCurrentUser(c *gin.Context) (*models.User, bool) {
	user, exists := c.Get("user")
	if !exists {
		return nil, false
	}

	userObj, ok := user.(*models.User)
	return userObj, ok
}

// GetCurrentUserID helper function to get current user ID from context
func GetCurrentUserID(c *gin.Context) (string, bool) {
	userID, exists := c.Get("user_id")
	if !exists {
		return "", false
	}

	userIDStr, ok := userID.(string)
	return userIDStr, ok
}

// IsAuthenticated helper function to check if user is authenticated
func IsAuthenticated(c *gin.Context) bool {
	_, exists := c.Get("user")
	return exists
}

// HasRole helper function to check if user has a specific role
func HasRole(c *gin.Context, role models.UserRole) bool {
	user, exists := GetCurrentUser(c)
	if !exists {
		return false
	}
	return user.Role == role || user.Role == models.UserRoleAdmin
}

// HasPermission helper function to check if user has a specific permission
func HasPermission(c *gin.Context, permission string) bool {
	user, exists := GetCurrentUser(c)
	if !exists {
		return false
	}
	return user.HasPermission(permission)
}
