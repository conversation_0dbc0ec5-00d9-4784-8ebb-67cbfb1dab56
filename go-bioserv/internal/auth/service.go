package auth

import (
	"crypto/rand"
	"encoding/hex"
	"fmt"
	"time"

	"github.com/golang-jwt/jwt/v5"
	"github.com/google/uuid"
	"github.com/gem-fusion/go-bioserv/internal/models"
	"github.com/gem-fusion/go-bioserv/internal/storage"
)

// Service handles authentication operations
type Service struct {
	storage   *storage.FileStorage
	jwtSecret []byte
	tokenTTL  time.Duration
}

// NewService creates a new authentication service
func NewService(storage *storage.FileStorage, jwtSecret string, tokenTTL time.Duration) *Service {
	if jwtSecret == "" {
		// Generate a random secret if none provided (for development)
		jwtSecret = generateRandomSecret()
	}
	
	return &Service{
		storage:   storage,
		jwtSecret: []byte(jwtSecret),
		tokenTTL:  tokenTTL,
	}
}

// Register creates a new user account
func (s *Service) Register(req *models.UserRegistrationRequest) (*models.User, error) {
	// Check if username already exists
	if _, err := s.storage.GetUserByUsername(req.Username); err == nil {
		return nil, fmt.Errorf("username already exists")
	}

	// Check if email already exists
	if _, err := s.storage.GetUserByEmail(req.Email); err == nil {
		return nil, fmt.Errorf("email already exists")
	}

	// Set default role if not specified
	role := req.Role
	if role == "" {
		role = models.UserRoleStudent // Default role
	}

	// Validate role
	if !models.IsValidRole(role) {
		return nil, fmt.Errorf("invalid role")
	}

	// Create user
	user := &models.User{
		ID:        uuid.New().String(),
		Username:  req.Username,
		Email:     req.Email,
		FirstName: req.FirstName,
		LastName:  req.LastName,
		Role:      role,
		IsActive:  true,
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	// Hash password
	if err := user.HashPassword(req.Password); err != nil {
		return nil, fmt.Errorf("failed to hash password: %w", err)
	}

	// Save user
	if err := s.storage.CreateUser(user); err != nil {
		return nil, fmt.Errorf("failed to create user: %w", err)
	}

	return user, nil
}

// Login authenticates a user and returns a token
func (s *Service) Login(req *models.UserLoginRequest) (*models.AuthResponse, error) {
	// Get user by username
	user, err := s.storage.GetUserByUsername(req.Username)
	if err != nil {
		return nil, fmt.Errorf("invalid credentials")
	}

	// Check if user is active
	if !user.IsActive {
		return nil, fmt.Errorf("account is disabled")
	}

	// Check password
	if !user.CheckPassword(req.Password) {
		return nil, fmt.Errorf("invalid credentials")
	}

	// Update last login
	now := time.Now()
	user.LastLogin = &now
	user.UpdatedAt = now
	s.storage.UpdateUser(user)

	// Generate token
	token, expiresAt, err := s.GenerateToken(user)
	if err != nil {
		return nil, fmt.Errorf("failed to generate token: %w", err)
	}

	// Create session
	session := &models.Session{
		ID:        uuid.New().String(),
		UserID:    user.ID,
		Token:     token,
		ExpiresAt: time.Unix(expiresAt, 0),
		CreatedAt: time.Now(),
	}
	s.storage.CreateSession(session)

	return &models.AuthResponse{
		User:      user.Sanitize(),
		Token:     token,
		ExpiresAt: expiresAt,
	}, nil
}

// GenerateToken generates a JWT token for a user
func (s *Service) GenerateToken(user *models.User) (string, int64, error) {
	now := time.Now()
	expiresAt := now.Add(s.tokenTTL)

	claims := &models.TokenClaims{
		UserID:    user.ID,
		Username:  user.Username,
		Role:      user.Role,
		IssuedAt:  now.Unix(),
		ExpiresAt: expiresAt.Unix(),
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, jwt.MapClaims{
		"user_id":  claims.UserID,
		"username": claims.Username,
		"role":     claims.Role,
		"iat":      claims.IssuedAt,
		"exp":      claims.ExpiresAt,
	})

	tokenString, err := token.SignedString(s.jwtSecret)
	if err != nil {
		return "", 0, err
	}

	return tokenString, expiresAt.Unix(), nil
}

// ValidateToken validates a JWT token and returns the claims
func (s *Service) ValidateToken(tokenString string) (*models.TokenClaims, error) {
	token, err := jwt.Parse(tokenString, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}
		return s.jwtSecret, nil
	})

	if err != nil {
		return nil, err
	}

	if !token.Valid {
		return nil, fmt.Errorf("invalid token")
	}

	claims, ok := token.Claims.(jwt.MapClaims)
	if !ok {
		return nil, fmt.Errorf("invalid token claims")
	}

	userID, ok := claims["user_id"].(string)
	if !ok {
		return nil, fmt.Errorf("invalid user_id in token")
	}

	username, ok := claims["username"].(string)
	if !ok {
		return nil, fmt.Errorf("invalid username in token")
	}

	roleStr, ok := claims["role"].(string)
	if !ok {
		return nil, fmt.Errorf("invalid role in token")
	}

	exp, ok := claims["exp"].(float64)
	if !ok {
		return nil, fmt.Errorf("invalid exp in token")
	}

	iat, ok := claims["iat"].(float64)
	if !ok {
		return nil, fmt.Errorf("invalid iat in token")
	}

	return &models.TokenClaims{
		UserID:    userID,
		Username:  username,
		Role:      models.UserRole(roleStr),
		IssuedAt:  int64(iat),
		ExpiresAt: int64(exp),
	}, nil
}

// GetUserFromToken extracts user information from a token
func (s *Service) GetUserFromToken(tokenString string) (*models.User, error) {
	claims, err := s.ValidateToken(tokenString)
	if err != nil {
		return nil, err
	}

	user, err := s.storage.GetUser(claims.UserID)
	if err != nil {
		return nil, fmt.Errorf("user not found")
	}

	if !user.IsActive {
		return nil, fmt.Errorf("account is disabled")
	}

	return user, nil
}

// Logout invalidates a user session
func (s *Service) Logout(tokenString string) error {
	claims, err := s.ValidateToken(tokenString)
	if err != nil {
		return err
	}

	return s.storage.DeleteSessionByUserID(claims.UserID)
}

// ChangePassword changes a user's password
func (s *Service) ChangePassword(userID string, req *models.PasswordChangeRequest) error {
	user, err := s.storage.GetUser(userID)
	if err != nil {
		return fmt.Errorf("user not found")
	}

	// Check current password
	if !user.CheckPassword(req.CurrentPassword) {
		return fmt.Errorf("current password is incorrect")
	}

	// Hash new password
	if err := user.HashPassword(req.NewPassword); err != nil {
		return fmt.Errorf("failed to hash new password: %w", err)
	}

	user.UpdatedAt = time.Now()
	return s.storage.UpdateUser(user)
}

// RefreshToken generates a new token for a user
func (s *Service) RefreshToken(tokenString string) (*models.AuthResponse, error) {
	claims, err := s.ValidateToken(tokenString)
	if err != nil {
		return nil, err
	}

	user, err := s.storage.GetUser(claims.UserID)
	if err != nil {
		return nil, fmt.Errorf("user not found")
	}

	if !user.IsActive {
		return nil, fmt.Errorf("account is disabled")
	}

	// Generate new token
	newToken, expiresAt, err := s.GenerateToken(user)
	if err != nil {
		return nil, fmt.Errorf("failed to generate token: %w", err)
	}

	return &models.AuthResponse{
		User:      user.Sanitize(),
		Token:     newToken,
		ExpiresAt: expiresAt,
	}, nil
}

// generateRandomSecret generates a random JWT secret
func generateRandomSecret() string {
	bytes := make([]byte, 32)
	rand.Read(bytes)
	return hex.EncodeToString(bytes)
}

// LogActivity logs user activity
func (s *Service) LogActivity(userID, action, resource, details, ipAddress, userAgent string) error {
	activity := &models.UserActivity{
		ID:        uuid.New().String(),
		UserID:    userID,
		Action:    action,
		Resource:  resource,
		Details:   details,
		IPAddress: ipAddress,
		UserAgent: userAgent,
		CreatedAt: time.Now(),
	}

	return s.storage.CreateUserActivity(activity)
}
