package models

import (
	"time"
)

// MetabolicModel represents a genome-scale metabolic model
type MetabolicModel struct {
	ID          string            `json:"id"`
	Name        string            `json:"name"`
	Description string            `json:"description"`
	Format      string            `json:"format"` // "sbml" or "json"
	FilePath    string            `json:"file_path"`
	Metadata    map[string]string `json:"metadata"`
	CreatedAt   time.Time         `json:"created_at"`
	UpdatedAt   time.Time         `json:"updated_at"`
}

// Job represents an analysis job
type Job struct {
	ID        string            `json:"id"`
	ModelID   string            `json:"model_id"`
	Type      string            `json:"type"` // "fba", "fva", etc.
	Status    JobStatus         `json:"status"`
	Input     map[string]any    `json:"input"`
	Output    map[string]any    `json:"output,omitempty"`
	Error     string            `json:"error,omitempty"`
	CreatedAt time.Time         `json:"created_at"`
	UpdatedAt time.Time         `json:"updated_at"`
	StartedAt *time.Time        `json:"started_at,omitempty"`
	EndedAt   *time.Time        `json:"ended_at,omitempty"`
}

// JobStatus represents the status of a job
type JobStatus string

const (
	JobStatusPending   JobStatus = "pending"
	JobStatusRunning   JobStatus = "running"
	JobStatusCompleted JobStatus = "completed"
	JobStatusFailed    JobStatus = "failed"
)

// FBARequest represents a request for Flux Balance Analysis
type FBARequest struct {
	Objective   string             `json:"objective"`
	Constraints map[string]float64 `json:"constraints,omitempty"`
	Options     map[string]any     `json:"options,omitempty"`
}

// FBAResult represents the result of Flux Balance Analysis
type FBAResult struct {
	ObjectiveValue float64            `json:"objective_value"`
	FluxValues     map[string]float64 `json:"flux_values"`
	Status         string             `json:"status"`
	SolverTime     float64            `json:"solver_time"`
}

// ModelUploadRequest represents a model upload request
type ModelUploadRequest struct {
	Name        string            `json:"name"`
	Description string            `json:"description"`
	Format      string            `json:"format"`
	Metadata    map[string]string `json:"metadata,omitempty"`
}

// APIResponse represents a standard API response
type APIResponse struct {
	Success bool   `json:"success"`
	Message string `json:"message,omitempty"`
	Data    any    `json:"data,omitempty"`
	Error   string `json:"error,omitempty"`
}

// JobStatusResponse represents a job status response
type JobStatusResponse struct {
	Job      Job     `json:"job"`
	Progress float64 `json:"progress,omitempty"`
}

// ModelListResponse represents a list of models response
type ModelListResponse struct {
	Models []MetabolicModel `json:"models"`
	Total  int              `json:"total"`
}

// Reaction represents a metabolic reaction
type Reaction struct {
	ID           string             `json:"id"`
	Name         string             `json:"name"`
	Equation     string             `json:"equation"`
	LowerBound   float64            `json:"lower_bound"`
	UpperBound   float64            `json:"upper_bound"`
	Objective    float64            `json:"objective"`
	Stoichiometry map[string]float64 `json:"stoichiometry"`
}

// Metabolite represents a metabolite
type Metabolite struct {
	ID          string `json:"id"`
	Name        string `json:"name"`
	Formula     string `json:"formula"`
	Compartment string `json:"compartment"`
	Charge      int    `json:"charge"`
}

// ModelSummary represents a summary of model contents
type ModelSummary struct {
	ReactionCount   int `json:"reaction_count"`
	MetaboliteCount int `json:"metabolite_count"`
	GeneCount       int `json:"gene_count"`
}
