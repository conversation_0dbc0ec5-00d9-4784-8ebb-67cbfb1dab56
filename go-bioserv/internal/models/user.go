package models

import (
	"time"
	"golang.org/x/crypto/bcrypt"
)

// User represents a user in the system
type User struct {
	ID        string    `json:"id"`
	Username  string    `json:"username"`
	Email     string    `json:"email"`
	Password  string    `json:"-"` // Never include in JSON responses
	FirstName string    `json:"first_name"`
	LastName  string    `json:"last_name"`
	Role      UserRole  `json:"role"`
	IsActive  bool      `json:"is_active"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
	LastLogin *time.Time `json:"last_login,omitempty"`
}

// UserRole represents user roles
type UserRole string

const (
	UserRoleAdmin      UserRole = "admin"
	UserRoleResearcher UserRole = "researcher"
	UserRoleStudent    UserRole = "student"
	UserRoleGuest      UserRole = "guest"
)

// UserRegistrationRequest represents a user registration request
type UserRegistrationRequest struct {
	Username  string `json:"username" binding:"required,min=3,max=50"`
	Email     string `json:"email" binding:"required,email"`
	Password  string `json:"password" binding:"required,min=8"`
	FirstName string `json:"first_name" binding:"required,min=1,max=50"`
	LastName  string `json:"last_name" binding:"required,min=1,max=50"`
	Role      UserRole `json:"role,omitempty"`
}

// UserLoginRequest represents a user login request
type UserLoginRequest struct {
	Username string `json:"username" binding:"required"`
	Password string `json:"password" binding:"required"`
}

// UserUpdateRequest represents a user update request
type UserUpdateRequest struct {
	FirstName string   `json:"first_name,omitempty"`
	LastName  string   `json:"last_name,omitempty"`
	Email     string   `json:"email,omitempty"`
	Role      UserRole `json:"role,omitempty"`
	IsActive  *bool    `json:"is_active,omitempty"`
}

// PasswordChangeRequest represents a password change request
type PasswordChangeRequest struct {
	CurrentPassword string `json:"current_password" binding:"required"`
	NewPassword     string `json:"new_password" binding:"required,min=8"`
}

// AuthResponse represents an authentication response
type AuthResponse struct {
	User         User   `json:"user"`
	Token        string `json:"token"`
	RefreshToken string `json:"refresh_token,omitempty"`
	ExpiresAt    int64  `json:"expires_at"`
}

// TokenClaims represents JWT token claims
type TokenClaims struct {
	UserID   string   `json:"user_id"`
	Username string   `json:"username"`
	Role     UserRole `json:"role"`
	IssuedAt int64    `json:"iat"`
	ExpiresAt int64   `json:"exp"`
}

// UserListResponse represents a list of users response
type UserListResponse struct {
	Users []User `json:"users"`
	Total int    `json:"total"`
	Page  int    `json:"page"`
	Limit int    `json:"limit"`
}

// HashPassword hashes a password using bcrypt
func (u *User) HashPassword(password string) error {
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	if err != nil {
		return err
	}
	u.Password = string(hashedPassword)
	return nil
}

// CheckPassword checks if the provided password matches the user's password
func (u *User) CheckPassword(password string) bool {
	err := bcrypt.CompareHashAndPassword([]byte(u.Password), []byte(password))
	return err == nil
}

// IsValidRole checks if the role is valid
func IsValidRole(role UserRole) bool {
	switch role {
	case UserRoleAdmin, UserRoleResearcher, UserRoleStudent, UserRoleGuest:
		return true
	default:
		return false
	}
}

// HasPermission checks if the user has permission for a specific action
func (u *User) HasPermission(action string) bool {
	switch u.Role {
	case UserRoleAdmin:
		return true // Admin has all permissions
	case UserRoleResearcher:
		// Researchers can do most things except user management
		return action != "manage_users"
	case UserRoleStudent:
		// Students have limited permissions
		switch action {
		case "view_models", "run_analysis", "view_results":
			return true
		default:
			return false
		}
	case UserRoleGuest:
		// Guests can only view
		return action == "view_models"
	default:
		return false
	}
}

// GetDisplayName returns the user's display name
func (u *User) GetDisplayName() string {
	if u.FirstName != "" && u.LastName != "" {
		return u.FirstName + " " + u.LastName
	}
	if u.FirstName != "" {
		return u.FirstName
	}
	return u.Username
}

// Sanitize removes sensitive information from user object
func (u *User) Sanitize() User {
	sanitized := *u
	sanitized.Password = ""
	return sanitized
}

// Session represents a user session
type Session struct {
	ID        string    `json:"id"`
	UserID    string    `json:"user_id"`
	Token     string    `json:"token"`
	ExpiresAt time.Time `json:"expires_at"`
	CreatedAt time.Time `json:"created_at"`
	IPAddress string    `json:"ip_address,omitempty"`
	UserAgent string    `json:"user_agent,omitempty"`
}

// IsExpired checks if the session is expired
func (s *Session) IsExpired() bool {
	return time.Now().After(s.ExpiresAt)
}

// UserActivity represents user activity logging
type UserActivity struct {
	ID        string    `json:"id"`
	UserID    string    `json:"user_id"`
	Action    string    `json:"action"`
	Resource  string    `json:"resource,omitempty"`
	Details   string    `json:"details,omitempty"`
	IPAddress string    `json:"ip_address,omitempty"`
	UserAgent string    `json:"user_agent,omitempty"`
	CreatedAt time.Time `json:"created_at"`
}

// UserPreferences represents user preferences
type UserPreferences struct {
	UserID              string `json:"user_id"`
	Theme               string `json:"theme"` // "light", "dark", "auto"
	Language            string `json:"language"`
	DefaultAnalysisType string `json:"default_analysis_type"`
	EmailNotifications  bool   `json:"email_notifications"`
	UpdatedAt           time.Time `json:"updated_at"`
}
