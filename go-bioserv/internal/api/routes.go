package api

import (
	"github.com/gin-gonic/gin"
)

// SetupRoutes configures all API routes
func SetupRoutes(handlers *Handlers) *gin.Engine {
	// Create Gin router with default middleware
	router := gin.Default()

	// Add CORS middleware
	router.Use(func(c *gin.Context) {
		c.<PERSON><PERSON>("Access-Control-Allow-Origin", "*")
		c.<PERSON><PERSON>("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.<PERSON><PERSON>("Access-Control-Allow-Headers", "Content-Type, Authorization")
		
		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(204)
			return
		}
		
		c.Next()
	})

	// Health check endpoint
	router.GET("/health", handlers.HealthCheck)
	router.GET("/stats", handlers.GetStats)

	// API v1 routes
	v1 := router.Group("/api/v1")
	{
		// Model management
		models := v1.Group("/models")
		{
			models.POST("", handlers.UploadModel)
			models.GET("", handlers.ListModels)
			models.GET("/:model_id", handlers.GetModel)
			
			// Analysis endpoints
			models.POST("/:model_id/fba", handlers.SubmitFBA)
		}

		// Job management
		jobs := v1.Group("/jobs")
		{
			jobs.GET("/:job_id/status", handlers.GetJobStatus)
			jobs.GET("/:job_id/results", handlers.GetJobResults)
		}
	}

	// Legacy routes (for backward compatibility)
	router.POST("/models", handlers.UploadModel)
	router.GET("/models/:model_id", handlers.GetModel)
	router.POST("/models/:model_id/fba", handlers.SubmitFBA)
	router.GET("/jobs/:job_id/status", handlers.GetJobStatus)
	router.GET("/jobs/:job_id/results", handlers.GetJobResults)

	return router
}
