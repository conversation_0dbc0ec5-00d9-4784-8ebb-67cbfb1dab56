package api

import (
	"github.com/gem-fusion/go-bioserv/internal/auth"
	"github.com/gin-gonic/gin"
)

// SetupRoutes configures all API routes
func SetupRoutes(handlers *Handlers, authService *auth.Service) *gin.Engine {
	// Create Gin router with default middleware
	router := gin.Default()

	// Add CORS middleware
	router.Use(func(c *gin.Context) {
		c.<PERSON><PERSON>("Access-Control-Allow-Origin", "*")
		c.<PERSON><PERSON>("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.<PERSON><PERSON>("Access-Control-Allow-Headers", "Content-Type, Authorization")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(204)
			return
		}

		c.Next()
	})

	// Initialize auth middleware and handlers
	authMiddleware := auth.NewMiddleware(authService)
	authHandlers := NewAuthHandlers(authService)

	// Health check endpoint
	router.GET("/health", handlers.HealthCheck)
	router.GET("/stats", handlers.GetStats)

	// Authentication routes (public)
	authGroup := router.Group("/auth")
	{
		authGroup.POST("/register", authHandlers.Register)
		authGroup.POST("/login", authHandlers.Login)
		authGroup.POST("/logout", authHandlers.Logout)
		authGroup.POST("/refresh", authHandlers.RefreshToken)
	}

	// API v1 routes
	v1 := router.Group("/api/v1")
	{
		// User management (protected)
		users := v1.Group("/users")
		users.Use(authMiddleware.RequireAuth())
		{
			users.GET("/profile", authHandlers.GetProfile)
			users.PUT("/profile", authHandlers.UpdateProfile)
			users.POST("/change-password", authHandlers.ChangePassword)

			// Admin only routes
			users.GET("", authMiddleware.AdminOnly(), authHandlers.ListUsers)
		}

		// Model management (protected)
		models := v1.Group("/models")
		models.Use(authMiddleware.OptionalAuth()) // Allow both authenticated and guest access
		{
			models.POST("", authMiddleware.RequirePermission("upload_models"), handlers.UploadModel)
			models.GET("", handlers.ListModels)
			models.GET("/:model_id", handlers.GetModel)

			// Analysis endpoints (require authentication)
			models.POST("/:model_id/fba", authMiddleware.RequirePermission("run_analysis"), handlers.SubmitFBA)
			models.POST("/:model_id/ema", authMiddleware.RequirePermission("run_analysis"), handlers.SubmitEMA)
		}

		// Job management (protected)
		jobs := v1.Group("/jobs")
		jobs.Use(authMiddleware.RequireAuth())
		{
			jobs.GET("", handlers.ListJobs)
			jobs.GET("/:job_id/status", handlers.GetJobStatus)
			jobs.GET("/:job_id/results", handlers.GetJobResults)
		}
	}

	// Legacy routes (for backward compatibility)
	router.POST("/models", handlers.UploadModel)
	router.GET("/models", handlers.ListModels)
	router.GET("/models/:model_id", handlers.GetModel)
	router.POST("/models/:model_id/fba", handlers.SubmitFBA)
	router.POST("/models/:model_id/ema", handlers.SubmitEMA)
	router.GET("/jobs", handlers.ListJobs)
	router.GET("/jobs/:job_id/status", handlers.GetJobStatus)
	router.GET("/jobs/:job_id/results", handlers.GetJobResults)

	return router
}
