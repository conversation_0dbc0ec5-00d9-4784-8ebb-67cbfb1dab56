package api

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/gem-fusion/go-bioserv/internal/auth"
	"github.com/gem-fusion/go-bioserv/internal/models"
)

// AuthHandlers contains authentication-related HTTP handlers
type AuthHandlers struct {
	authService *auth.Service
}

// NewAuthHandlers creates a new auth handlers instance
func NewAuthHandlers(authService *auth.Service) *AuthHandlers {
	return &AuthHandlers{
		authService: authService,
	}
}

// Register handles user registration
func (h *AuthHandlers) Register(c *gin.Context) {
	var req models.UserRegistrationRequest
	if err := c.ShouldBindJ<PERSON>N(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Error:   "Invalid request: " + err.Error(),
		})
		return
	}

	user, err := h.authService.Register(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Error:   err.Error(),
		})
		return
	}

	c.J<PERSON>(http.StatusCreated, models.APIResponse{
		Success: true,
		Message: "User registered successfully",
		Data:    user.Sanitize(),
	})
}

// Login handles user login
func (h *AuthHandlers) Login(c *gin.Context) {
	var req models.UserLoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Error:   "Invalid request: " + err.Error(),
		})
		return
	}

	authResponse, err := h.authService.Login(&req)
	if err != nil {
		c.JSON(http.StatusUnauthorized, models.APIResponse{
			Success: false,
			Error:   err.Error(),
		})
		return
	}

	// Set cookie for web clients
	c.SetCookie("auth_token", authResponse.Token, int(authResponse.ExpiresAt), "/", "", false, true)

	c.JSON(http.StatusOK, models.APIResponse{
		Success: true,
		Message: "Login successful",
		Data:    authResponse,
	})
}

// Logout handles user logout
func (h *AuthHandlers) Logout(c *gin.Context) {
	token := extractTokenFromRequest(c)
	if token == "" {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Error:   "No token provided",
		})
		return
	}

	if err := h.authService.Logout(token); err != nil {
		c.JSON(http.StatusInternalServerError, models.APIResponse{
			Success: false,
			Error:   "Failed to logout: " + err.Error(),
		})
		return
	}

	// Clear cookie
	c.SetCookie("auth_token", "", -1, "/", "", false, true)

	c.JSON(http.StatusOK, models.APIResponse{
		Success: true,
		Message: "Logout successful",
	})
}

// GetProfile returns the current user's profile
func (h *AuthHandlers) GetProfile(c *gin.Context) {
	user, exists := auth.GetCurrentUser(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, models.APIResponse{
			Success: false,
			Error:   "Authentication required",
		})
		return
	}

	c.JSON(http.StatusOK, models.APIResponse{
		Success: true,
		Data:    user.Sanitize(),
	})
}

// UpdateProfile updates the current user's profile
func (h *AuthHandlers) UpdateProfile(c *gin.Context) {
	user, exists := auth.GetCurrentUser(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, models.APIResponse{
			Success: false,
			Error:   "Authentication required",
		})
		return
	}

	var req models.UserUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Error:   "Invalid request: " + err.Error(),
		})
		return
	}

	// Update user fields
	if req.FirstName != "" {
		user.FirstName = req.FirstName
	}
	if req.LastName != "" {
		user.LastName = req.LastName
	}
	if req.Email != "" {
		user.Email = req.Email
	}

	// Only admins can change roles and active status
	if user.Role == models.UserRoleAdmin {
		if req.Role != "" {
			user.Role = req.Role
		}
		if req.IsActive != nil {
			user.IsActive = *req.IsActive
		}
	}

	// Save updated user (this would need to be implemented in storage)
	// For now, just return the updated user
	c.JSON(http.StatusOK, models.APIResponse{
		Success: true,
		Message: "Profile updated successfully",
		Data:    user.Sanitize(),
	})
}

// ChangePassword handles password change
func (h *AuthHandlers) ChangePassword(c *gin.Context) {
	userID, exists := auth.GetCurrentUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, models.APIResponse{
			Success: false,
			Error:   "Authentication required",
		})
		return
	}

	var req models.PasswordChangeRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Error:   "Invalid request: " + err.Error(),
		})
		return
	}

	if err := h.authService.ChangePassword(userID, &req); err != nil {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, models.APIResponse{
		Success: true,
		Message: "Password changed successfully",
	})
}

// RefreshToken handles token refresh
func (h *AuthHandlers) RefreshToken(c *gin.Context) {
	token := extractTokenFromRequest(c)
	if token == "" {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Error:   "No token provided",
		})
		return
	}

	authResponse, err := h.authService.RefreshToken(token)
	if err != nil {
		c.JSON(http.StatusUnauthorized, models.APIResponse{
			Success: false,
			Error:   err.Error(),
		})
		return
	}

	// Update cookie
	c.SetCookie("auth_token", authResponse.Token, int(authResponse.ExpiresAt), "/", "", false, true)

	c.JSON(http.StatusOK, models.APIResponse{
		Success: true,
		Message: "Token refreshed successfully",
		Data:    authResponse,
	})
}

// ListUsers handles listing users (admin only)
func (h *AuthHandlers) ListUsers(c *gin.Context) {
	// Parse pagination parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))

	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 20
	}

	// This would need to be implemented in the auth service
	// For now, return a placeholder response
	c.JSON(http.StatusOK, models.APIResponse{
		Success: true,
		Data: models.UserListResponse{
			Users: []models.User{},
			Total: 0,
			Page:  page,
			Limit: limit,
		},
	})
}

// extractTokenFromRequest extracts token from various sources
func extractTokenFromRequest(c *gin.Context) string {
	// Try Authorization header
	if auth := c.GetHeader("Authorization"); auth != "" {
		if len(auth) > 7 && auth[:7] == "Bearer " {
			return auth[7:]
		}
		return auth
	}

	// Try query parameter
	if token := c.Query("token"); token != "" {
		return token
	}

	// Try cookie
	if cookie, err := c.Cookie("auth_token"); err == nil {
		return cookie
	}

	return ""
}
