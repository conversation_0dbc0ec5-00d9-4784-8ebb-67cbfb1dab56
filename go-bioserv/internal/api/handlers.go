package api

import (
	"fmt"
	"net/http"
	"path/filepath"
	"strings"

	"github.com/gem-fusion/go-bioserv/internal/jobs"
	"github.com/gem-fusion/go-bioserv/internal/models"
	"github.com/gem-fusion/go-bioserv/internal/storage"
	"github.com/gin-gonic/gin"
)

// Handlers contains all HTTP handlers
type Handlers struct {
	storage  *storage.FileStorage
	jobQueue *jobs.JobQueue
}

// NewHandlers creates a new handlers instance
func NewHandlers(storage *storage.FileStorage, jobQueue *jobs.JobQueue) *Handlers {
	return &Handlers{
		storage:  storage,
		jobQueue: jobQueue,
	}
}

// UploadModel handles model upload
func (h *Handlers) UploadModel(c *gin.Context) {
	// Parse multipart form
	file, header, err := c.Request.FormFile("file")
	if err != nil {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Error:   "No file provided",
		})
		return
	}
	defer file.Close()

	// Get form fields
	name := c.PostForm("name")
	description := c.PostForm("description")

	if name == "" {
		name = header.Filename
	}

	// Determine format from file extension
	format := "unknown"
	ext := strings.ToLower(filepath.Ext(header.Filename))
	switch ext {
	case ".xml", ".sbml":
		format = "sbml"
	case ".json":
		format = "json"
	}

	// Create model record
	model, err := h.storage.CreateModel(name, description, format, nil)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.APIResponse{
			Success: false,
			Error:   fmt.Sprintf("Failed to create model: %v", err),
		})
		return
	}

	// Save model file
	filePath, err := h.storage.SaveModelFile(model.ID, header.Filename, file)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.APIResponse{
			Success: false,
			Error:   fmt.Sprintf("Failed to save model file: %v", err),
		})
		return
	}

	// Update model with file path
	model.FilePath = filePath
	if err := h.storage.SaveModel(model); err != nil {
		c.JSON(http.StatusInternalServerError, models.APIResponse{
			Success: false,
			Error:   fmt.Sprintf("Failed to update model: %v", err),
		})
		return
	}

	c.JSON(http.StatusCreated, models.APIResponse{
		Success: true,
		Message: "Model uploaded successfully",
		Data:    model,
	})
}

// GetModel handles model retrieval
func (h *Handlers) GetModel(c *gin.Context) {
	modelID := c.Param("model_id")

	model, err := h.storage.GetModel(modelID)
	if err != nil {
		c.JSON(http.StatusNotFound, models.APIResponse{
			Success: false,
			Error:   fmt.Sprintf("Model not found: %v", err),
		})
		return
	}

	c.JSON(http.StatusOK, models.APIResponse{
		Success: true,
		Data:    model,
	})
}

// ListModels handles model listing
func (h *Handlers) ListModels(c *gin.Context) {
	modelList, err := h.storage.ListModels()
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.APIResponse{
			Success: false,
			Error:   fmt.Sprintf("Failed to list models: %v", err),
		})
		return
	}

	response := models.ModelListResponse{
		Models: modelList,
		Total:  len(modelList),
	}

	c.JSON(http.StatusOK, models.APIResponse{
		Success: true,
		Data:    response,
	})
}

// SubmitFBA handles FBA job submission
func (h *Handlers) SubmitFBA(c *gin.Context) {
	modelID := c.Param("model_id")

	// Check if model exists
	_, err := h.storage.GetModel(modelID)
	if err != nil {
		c.JSON(http.StatusNotFound, models.APIResponse{
			Success: false,
			Error:   fmt.Sprintf("Model not found: %v", err),
		})
		return
	}

	// Parse FBA request
	var fbaRequest models.FBARequest
	if err := c.ShouldBindJSON(&fbaRequest); err != nil {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Error:   fmt.Sprintf("Invalid request: %v", err),
		})
		return
	}

	// Validate required fields
	if fbaRequest.Objective == "" {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Error:   "Invalid request: objective is required",
		})
		return
	}

	// Create job input
	input := map[string]any{
		"objective":   fbaRequest.Objective,
		"constraints": fbaRequest.Constraints,
		"options":     fbaRequest.Options,
	}

	// Create job
	job, err := h.storage.CreateJob(modelID, "fba", input)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.APIResponse{
			Success: false,
			Error:   fmt.Sprintf("Failed to create job: %v", err),
		})
		return
	}

	// Submit to job queue
	if err := h.jobQueue.SubmitJob(job); err != nil {
		c.JSON(http.StatusInternalServerError, models.APIResponse{
			Success: false,
			Error:   fmt.Sprintf("Failed to submit job: %v", err),
		})
		return
	}

	c.JSON(http.StatusAccepted, models.APIResponse{
		Success: true,
		Message: "FBA job submitted successfully",
		Data:    job,
	})
}

// ListJobs handles listing all jobs
func (h *Handlers) ListJobs(c *gin.Context) {
	jobs, err := h.storage.ListJobs()
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.APIResponse{
			Success: false,
			Error:   fmt.Sprintf("Failed to list jobs: %v", err),
		})
		return
	}

	response := models.JobListResponse{
		Jobs:  jobs,
		Total: len(jobs),
	}

	c.JSON(http.StatusOK, models.APIResponse{
		Success: true,
		Data:    response,
	})
}

// GetJobStatus handles job status retrieval
func (h *Handlers) GetJobStatus(c *gin.Context) {
	jobID := c.Param("job_id")

	job, err := h.storage.GetJob(jobID)
	if err != nil {
		c.JSON(http.StatusNotFound, models.APIResponse{
			Success: false,
			Error:   fmt.Sprintf("Job not found: %v", err),
		})
		return
	}

	// Calculate progress based on job status
	progress := 0.0
	switch job.Status {
	case models.JobStatusPending:
		progress = 0.0
	case models.JobStatusRunning:
		progress = 0.5
	case models.JobStatusCompleted:
		progress = 1.0
	case models.JobStatusFailed:
		progress = 0.0
	}

	response := models.JobStatusResponse{
		Job:      *job,
		Progress: progress,
	}

	c.JSON(http.StatusOK, models.APIResponse{
		Success: true,
		Data:    response,
	})
}

// GetJobResults handles job results retrieval
func (h *Handlers) GetJobResults(c *gin.Context) {
	jobID := c.Param("job_id")

	job, err := h.storage.GetJob(jobID)
	if err != nil {
		c.JSON(http.StatusNotFound, models.APIResponse{
			Success: false,
			Error:   fmt.Sprintf("Job not found: %v", err),
		})
		return
	}

	if job.Status != models.JobStatusCompleted {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Error:   fmt.Sprintf("Job not completed, current status: %s", job.Status),
		})
		return
	}

	c.JSON(http.StatusOK, models.APIResponse{
		Success: true,
		Data:    job.Output,
	})
}

// HealthCheck handles health check
func (h *Handlers) HealthCheck(c *gin.Context) {
	c.JSON(http.StatusOK, models.APIResponse{
		Success: true,
		Message: "Service is healthy",
		Data: map[string]any{
			"service": "go-bioserv",
			"version": "1.0.0",
			"status":  "running",
		},
	})
}

// GetStats handles statistics retrieval
func (h *Handlers) GetStats(c *gin.Context) {
	modelList, err := h.storage.ListModels()
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.APIResponse{
			Success: false,
			Error:   fmt.Sprintf("Failed to get stats: %v", err),
		})
		return
	}

	jobList, err := h.storage.ListJobs()
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.APIResponse{
			Success: false,
			Error:   fmt.Sprintf("Failed to get job stats: %v", err),
		})
		return
	}

	// Count job statuses
	completedJobs := 0
	failedJobs := 0
	for _, job := range jobList {
		switch job.Status {
		case models.JobStatusCompleted:
			completedJobs++
		case models.JobStatusFailed:
			failedJobs++
		}
	}

	stats := map[string]any{
		"models_count":   len(modelList),
		"jobs_count":     len(jobList),
		"completed_jobs": completedJobs,
		"failed_jobs":    failedJobs,
		"uptime":         "running", // Simple uptime indicator
	}

	c.JSON(http.StatusOK, models.APIResponse{
		Success: true,
		Data:    stats,
	})
}
