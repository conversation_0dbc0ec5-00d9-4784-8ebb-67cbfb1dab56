package tests

import (
	"bytes"
	"encoding/json"
	"mime/multipart"
	"net/http"
	"net/http/httptest"
	"os"
	"testing"
	"time"

	"github.com/gem-fusion/go-bioserv/internal/api"
	"github.com/gem-fusion/go-bioserv/internal/ipc"
	"github.com/gem-fusion/go-bioserv/internal/jobs"
	"github.com/gem-fusion/go-bioserv/internal/models"
	"github.com/gem-fusion/go-bioserv/internal/storage"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func setupTestServer(t *testing.T) (*httptest.Server, *storage.FileStorage, *jobs.JobQueue) {
	// Create temporary directory for test data
	tempDir, err := os.MkdirTemp("", "gem-fusion-test-*")
	require.NoError(t, err)

	// Initialize storage
	storage, err := storage.NewFileStorage(tempDir)
	require.NoError(t, err)

	// Initialize mock Mojo client
	mojoClient := ipc.NewMockMojoClient(100 * time.Millisecond)

	// Initialize job queue
	jobQueue := jobs.NewJobQueue(storage, 2)
	fbaProcessor := jobs.NewFBAProcessor(storage, mojoClient)
	jobQueue.RegisterProcessor("fba", fbaProcessor)
	jobQueue.Start()

	// Setup API handlers and routes
	handlers := api.NewHandlers(storage, jobQueue)
	router := api.SetupRoutes(handlers)

	// Create test server
	server := httptest.NewServer(router)

	// Cleanup function
	t.Cleanup(func() {
		server.Close()
		jobQueue.Stop()
		os.RemoveAll(tempDir)
	})

	return server, storage, jobQueue
}

func TestHealthCheck(t *testing.T) {
	server, _, _ := setupTestServer(t)

	resp, err := http.Get(server.URL + "/health")
	require.NoError(t, err)
	defer resp.Body.Close()

	assert.Equal(t, http.StatusOK, resp.StatusCode)

	var response models.APIResponse
	err = json.NewDecoder(resp.Body).Decode(&response)
	require.NoError(t, err)

	assert.True(t, response.Success)
	assert.Contains(t, response.Message, "healthy")
}

func TestModelUpload(t *testing.T) {
	server, _, _ := setupTestServer(t)

	// Create a test SBML file
	testSBML := `<?xml version="1.0" encoding="UTF-8"?>
<sbml xmlns="http://www.sbml.org/sbml/level3/version1/core" level="3" version="1">
  <model id="test_model" name="Test Model">
    <listOfCompartments>
      <compartment id="c" name="cytoplasm" spatialDimensions="3" size="1" constant="true"/>
    </listOfCompartments>
    <listOfSpecies>
      <species id="glc_c" name="glucose" compartment="c" hasOnlySubstanceUnits="false" boundaryCondition="false" constant="false"/>
    </listOfSpecies>
    <listOfReactions>
      <reaction id="R1" name="Test Reaction" reversible="true" fast="false">
        <listOfReactants>
          <speciesReference species="glc_c" stoichiometry="1" constant="true"/>
        </listOfReactants>
      </reaction>
    </listOfReactions>
  </model>
</sbml>`

	// Create multipart form
	var buf bytes.Buffer
	writer := multipart.NewWriter(&buf)

	// Add file field
	fileWriter, err := writer.CreateFormFile("file", "test_model.xml")
	require.NoError(t, err)
	_, err = fileWriter.Write([]byte(testSBML))
	require.NoError(t, err)

	// Add form fields
	err = writer.WriteField("name", "Test Model")
	require.NoError(t, err)
	err = writer.WriteField("description", "A test metabolic model")
	require.NoError(t, err)

	err = writer.Close()
	require.NoError(t, err)

	// Make request
	req, err := http.NewRequest("POST", server.URL+"/models", &buf)
	require.NoError(t, err)
	req.Header.Set("Content-Type", writer.FormDataContentType())

	client := &http.Client{}
	resp, err := client.Do(req)
	require.NoError(t, err)
	defer resp.Body.Close()

	assert.Equal(t, http.StatusCreated, resp.StatusCode)

	var response models.APIResponse
	err = json.NewDecoder(resp.Body).Decode(&response)
	require.NoError(t, err)

	assert.True(t, response.Success)
	assert.Contains(t, response.Message, "uploaded successfully")

	// Verify model data
	modelData, ok := response.Data.(map[string]interface{})
	require.True(t, ok)
	assert.Equal(t, "Test Model", modelData["name"])
	assert.Equal(t, "sbml", modelData["format"])
}

func TestGetModel(t *testing.T) {
	server, storage, _ := setupTestServer(t)

	// Create a test model
	model, err := storage.CreateModel("Test Model", "Test Description", "sbml", nil)
	require.NoError(t, err)

	// Get model
	resp, err := http.Get(server.URL + "/models/" + model.ID)
	require.NoError(t, err)
	defer resp.Body.Close()

	assert.Equal(t, http.StatusOK, resp.StatusCode)

	var response models.APIResponse
	err = json.NewDecoder(resp.Body).Decode(&response)
	require.NoError(t, err)

	assert.True(t, response.Success)

	modelData, ok := response.Data.(map[string]interface{})
	require.True(t, ok)
	assert.Equal(t, model.ID, modelData["id"])
	assert.Equal(t, "Test Model", modelData["name"])
}

func TestGetNonexistentModel(t *testing.T) {
	server, _, _ := setupTestServer(t)

	resp, err := http.Get(server.URL + "/models/nonexistent")
	require.NoError(t, err)
	defer resp.Body.Close()

	assert.Equal(t, http.StatusNotFound, resp.StatusCode)

	var response models.APIResponse
	err = json.NewDecoder(resp.Body).Decode(&response)
	require.NoError(t, err)

	assert.False(t, response.Success)
	assert.Contains(t, response.Error, "not found")
}

func TestListModels(t *testing.T) {
	server, storage, _ := setupTestServer(t)

	// Create test models
	_, err := storage.CreateModel("Model 1", "Description 1", "sbml", nil)
	require.NoError(t, err)
	_, err = storage.CreateModel("Model 2", "Description 2", "json", nil)
	require.NoError(t, err)

	// List models
	resp, err := http.Get(server.URL + "/models")
	require.NoError(t, err)
	defer resp.Body.Close()

	assert.Equal(t, http.StatusOK, resp.StatusCode)

	var response models.APIResponse
	err = json.NewDecoder(resp.Body).Decode(&response)
	require.NoError(t, err)

	assert.True(t, response.Success)

	listData, ok := response.Data.(map[string]interface{})
	require.True(t, ok)
	assert.Equal(t, float64(2), listData["total"])

	modelsArray, ok := listData["models"].([]interface{})
	require.True(t, ok)
	assert.Len(t, modelsArray, 2)
}

func TestSubmitFBA(t *testing.T) {
	server, storage, _ := setupTestServer(t)

	// Create a test model
	model, err := storage.CreateModel("Test Model", "Test Description", "sbml", nil)
	require.NoError(t, err)

	// Create FBA request
	fbaRequest := models.FBARequest{
		Objective: "BIOMASS_Ecoli_core_w_GAM",
		Constraints: map[string]float64{
			"EX_glc__D_e": -10.0,
		},
		Options: map[string]any{
			"solver": "glpk",
		},
	}

	requestBody, err := json.Marshal(fbaRequest)
	require.NoError(t, err)

	// Submit FBA job
	resp, err := http.Post(
		server.URL+"/models/"+model.ID+"/fba",
		"application/json",
		bytes.NewBuffer(requestBody),
	)
	require.NoError(t, err)
	defer resp.Body.Close()

	assert.Equal(t, http.StatusAccepted, resp.StatusCode)

	var response models.APIResponse
	err = json.NewDecoder(resp.Body).Decode(&response)
	require.NoError(t, err)

	assert.True(t, response.Success)
	assert.Contains(t, response.Message, "submitted successfully")

	// Verify job data
	jobData, ok := response.Data.(map[string]interface{})
	require.True(t, ok)
	assert.Equal(t, model.ID, jobData["model_id"])
	assert.Equal(t, "fba", jobData["type"])
	assert.Equal(t, "pending", jobData["status"])
}

func TestJobStatusAndResults(t *testing.T) {
	server, storage, jobQueue := setupTestServer(t)

	// Create a test model
	model, err := storage.CreateModel("Test Model", "Test Description", "sbml", nil)
	require.NoError(t, err)

	// Create and submit job
	input := map[string]any{
		"objective": "BIOMASS_Ecoli_core_w_GAM",
	}
	job, err := storage.CreateJob(model.ID, "fba", input)
	require.NoError(t, err)

	err = jobQueue.SubmitJob(job)
	require.NoError(t, err)

	// Wait for job to complete (with timeout)
	timeout := time.After(5 * time.Second)
	ticker := time.NewTicker(100 * time.Millisecond)
	defer ticker.Stop()

	var completedJob *models.Job
	for {
		select {
		case <-timeout:
			t.Fatal("Job did not complete within timeout")
		case <-ticker.C:
			currentJob, err := storage.GetJob(job.ID)
			require.NoError(t, err)
			if currentJob.Status == models.JobStatusCompleted || currentJob.Status == models.JobStatusFailed {
				completedJob = currentJob
				goto jobCompleted
			}
		}
	}

jobCompleted:
	// Check job status
	resp, err := http.Get(server.URL + "/jobs/" + job.ID + "/status")
	require.NoError(t, err)
	defer resp.Body.Close()

	assert.Equal(t, http.StatusOK, resp.StatusCode)

	var statusResponse models.APIResponse
	err = json.NewDecoder(resp.Body).Decode(&statusResponse)
	require.NoError(t, err)

	assert.True(t, statusResponse.Success)

	statusData, ok := statusResponse.Data.(map[string]interface{})
	require.True(t, ok)
	jobInfo, ok := statusData["job"].(map[string]interface{})
	require.True(t, ok)
	assert.Equal(t, string(completedJob.Status), jobInfo["status"])

	// If job completed successfully, check results
	if completedJob.Status == models.JobStatusCompleted {
		resp, err = http.Get(server.URL + "/jobs/" + job.ID + "/results")
		require.NoError(t, err)
		defer resp.Body.Close()

		assert.Equal(t, http.StatusOK, resp.StatusCode)

		var resultsResponse models.APIResponse
		err = json.NewDecoder(resp.Body).Decode(&resultsResponse)
		require.NoError(t, err)

		assert.True(t, resultsResponse.Success)

		resultsData, ok := resultsResponse.Data.(map[string]interface{})
		require.True(t, ok)
		assert.Contains(t, resultsData, "objective_value")
		assert.Contains(t, resultsData, "flux_values")
	}
}

func TestInvalidFBARequest(t *testing.T) {
	server, storage, _ := setupTestServer(t)

	// Create a test model
	model, err := storage.CreateModel("Test Model", "Test Description", "sbml", nil)
	require.NoError(t, err)

	// Submit invalid FBA request (missing objective)
	invalidRequest := map[string]any{
		"constraints": map[string]float64{
			"EX_glc__D_e": -10.0,
		},
	}

	requestBody, err := json.Marshal(invalidRequest)
	require.NoError(t, err)

	resp, err := http.Post(
		server.URL+"/models/"+model.ID+"/fba",
		"application/json",
		bytes.NewBuffer(requestBody),
	)
	require.NoError(t, err)
	defer resp.Body.Close()

	assert.Equal(t, http.StatusBadRequest, resp.StatusCode)

	var response models.APIResponse
	err = json.NewDecoder(resp.Body).Decode(&response)
	require.NoError(t, err)

	assert.False(t, response.Success)
	assert.Contains(t, response.Error, "Invalid request")
}

func TestCORSHeaders(t *testing.T) {
	server, _, _ := setupTestServer(t)

	// Make OPTIONS request
	req, err := http.NewRequest("OPTIONS", server.URL+"/models", nil)
	require.NoError(t, err)

	client := &http.Client{}
	resp, err := client.Do(req)
	require.NoError(t, err)
	defer resp.Body.Close()

	assert.Equal(t, http.StatusNoContent, resp.StatusCode)
	assert.Equal(t, "*", resp.Header.Get("Access-Control-Allow-Origin"))
	assert.Contains(t, resp.Header.Get("Access-Control-Allow-Methods"), "POST")
}

func TestAPIVersioning(t *testing.T) {
	server, storage, _ := setupTestServer(t)

	// Create a test model
	model, err := storage.CreateModel("Test Model", "Test Description", "sbml", nil)
	require.NoError(t, err)

	// Test both legacy and v1 endpoints
	endpoints := []string{
		"/models/" + model.ID,
		"/api/v1/models/" + model.ID,
	}

	for _, endpoint := range endpoints {
		resp, err := http.Get(server.URL + endpoint)
		require.NoError(t, err)
		defer resp.Body.Close()

		assert.Equal(t, http.StatusOK, resp.StatusCode, "Endpoint %s should work", endpoint)
	}
}
