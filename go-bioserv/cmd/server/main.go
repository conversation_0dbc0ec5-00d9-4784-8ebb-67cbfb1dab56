package main

import (
	"flag"
	"log"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/gem-fusion/go-bioserv/internal/api"
	"github.com/gem-fusion/go-bioserv/internal/auth"
	"github.com/gem-fusion/go-bioserv/internal/ipc"
	"github.com/gem-fusion/go-bioserv/internal/jobs"
	"github.com/gem-fusion/go-bioserv/internal/storage"
)

func main() {
	// Command line flags
	var (
		port      = flag.String("port", "8080", "Server port")
		dataDir   = flag.String("data-dir", "./data", "Data directory for storage")
		ipcDir    = flag.String("ipc-dir", "./ipc", "IPC directory for Mojo communication")
		mojoPath  = flag.String("mojo-path", "../mojo-metabolic-core/src/main.mojo", "Path to Mojo compute engine")
		workers   = flag.Int("workers", 4, "Number of job queue workers")
		useMock   = flag.Bool("mock", false, "Use mock Mojo client for testing")
		timeout   = flag.Duration("timeout", 30*time.Second, "Timeout for Mojo operations")
		jwtSecret = flag.String("jwt-secret", "", "JWT secret key (auto-generated if empty)")
		tokenTTL  = flag.Duration("token-ttl", 24*time.Hour, "JWT token time-to-live")
	)
	flag.Parse()

	log.Println("Starting GEM-Fusion Accelerator Go Backend...")

	// Initialize file storage
	storage, err := storage.NewFileStorage(*dataDir)
	if err != nil {
		log.Fatalf("Failed to initialize storage: %v", err)
	}
	log.Printf("Initialized file storage at: %s", *dataDir)

	// Initialize Mojo client
	var mojoClient jobs.MojoClient
	if *useMock {
		log.Println("Using mock Mojo client")
		mojoClient = ipc.NewMockMojoClient(2 * time.Second) // 2 second delay for testing
	} else {
		log.Printf("Initializing Mojo client with path: %s", *mojoPath)
		fileMojoClient, err := ipc.NewFileMojoClient(*ipcDir, *mojoPath, *timeout)
		if err != nil {
			log.Fatalf("Failed to initialize Mojo client: %v", err)
		}

		// Health check
		if err := fileMojoClient.HealthCheck(); err != nil {
			log.Printf("Warning: Mojo health check failed: %v", err)
			log.Println("Falling back to mock client")
			mojoClient = ipc.NewMockMojoClient(1 * time.Second)
		} else {
			mojoClient = fileMojoClient
			log.Println("Mojo client initialized successfully")
		}
	}

	// Initialize job queue
	jobQueue := jobs.NewJobQueue(storage, *workers)

	// Register job processors
	fbaProcessor := jobs.NewFBAProcessor(storage, mojoClient)
	jobQueue.RegisterProcessor("fba", fbaProcessor)

	emaProcessor := jobs.NewEMAProcessor(storage, mojoClient)
	jobQueue.RegisterProcessor("ema", emaProcessor)

	// Start job queue
	jobQueue.Start()
	log.Printf("Started job queue with %d workers", *workers)

	// Initialize authentication service
	authService := auth.NewService(storage, *jwtSecret, *tokenTTL)
	log.Printf("Authentication service initialized with %s token TTL", *tokenTTL)

	// Initialize API handlers
	handlers := api.NewHandlers(storage, jobQueue)
	router := api.SetupRoutes(handlers, authService)

	// Setup graceful shutdown
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)

	// Start server in a goroutine
	go func() {
		log.Printf("Server starting on port %s", *port)
		log.Printf("API endpoints available at: http://localhost:%s", *port)
		log.Printf("Health check: http://localhost:%s/health", *port)
		log.Printf("API documentation: http://localhost:%s/api/v1", *port)

		if err := router.Run(":" + *port); err != nil {
			log.Fatalf("Failed to start server: %v", err)
		}
	}()

	// Wait for shutdown signal
	<-quit
	log.Println("Shutting down server...")

	// Stop job queue
	jobQueue.Stop()

	log.Println("Server stopped")
}
