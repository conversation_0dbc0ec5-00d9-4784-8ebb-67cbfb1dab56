version: '3.8'

services:
  # Go Backend Service
  go-bioserv:
    build:
      context: ./go-bioserv
      dockerfile: Dockerfile
    ports:
      - "8080:8080"
    environment:
      - PORT=8080
      - DATA_DIR=/app/data
      - IPC_DIR=/app/ipc
      - MOJO_PATH=/app/mojo-compute/src/main.mojo
      - WORKERS=4
      - TIMEOUT=30s
    volumes:
      - ./data:/app/data
      - ./ipc:/app/ipc
      - ./mojo-metabolic-core:/app/mojo-compute
    depends_on:
      - mojo-compute
    networks:
      - gem-fusion-net
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Mojo Compute Engine Service
  mojo-compute:
    build:
      context: ./mojo-metabolic-core
      dockerfile: Dockerfile
    environment:
      - PYTHONPATH=/app/src
      - M<PERSON><PERSON><PERSON>_PATH=/app/src
    volumes:
      - ./ipc:/app/ipc
      - ./data:/app/data
    networks:
      - gem-fusion-net
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "python3", "-c", "import sys; print('Mojo compute engine ready')"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  # Optional: Redis for future job queue enhancement
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - gem-fusion-net
    restart: unless-stopped
    command: redis-server --appendonly yes
    profiles:
      - redis

  # Optional: PostgreSQL for future database integration
  postgres:
    image: postgres:15-alpine
    environment:
      - POSTGRES_DB=gem_fusion
      - POSTGRES_USER=gem_user
      - POSTGRES_PASSWORD=gem_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - gem-fusion-net
    restart: unless-stopped
    profiles:
      - database

  # Optional: Monitoring with Prometheus
  prometheus:
    image: prom/prometheus:latest
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    networks:
      - gem-fusion-net
    restart: unless-stopped
    profiles:
      - monitoring

  # Optional: Grafana for visualization
  grafana:
    image: grafana/grafana:latest
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    volumes:
      - grafana_data:/var/lib/grafana
    networks:
      - gem-fusion-net
    restart: unless-stopped
    profiles:
      - monitoring

networks:
  gem-fusion-net:
    driver: bridge

volumes:
  redis_data:
  postgres_data:
  prometheus_data:
  grafana_data:
