{"id": "simple_ecoli", "name": "Simple E. coli Model", "description": "Minimal E. coli model for quick FBA testing", "version": "1.0", "format": "json", "organism": "Escherichia coli", "compartments": {"c": {"name": "cytosol", "description": "Intracellular compartment"}, "e": {"name": "extracellular", "description": "Extracellular environment"}}, "metabolites": {"glc__D_c": {"name": "D-Glucose", "compartment": "c", "formula": "C6H12O6", "charge": 0}, "glc__D_e": {"name": "D-Glucose", "compartment": "e", "formula": "C6H12O6", "charge": 0}, "atp_c": {"name": "ATP", "compartment": "c", "formula": "C10H12N5O13P3", "charge": -4}, "adp_c": {"name": "ADP", "compartment": "c", "formula": "C10H12N5O10P2", "charge": -3}, "pi_c": {"name": "Phosphate", "compartment": "c", "formula": "HO4P", "charge": -2}, "pi_e": {"name": "Phosphate", "compartment": "e", "formula": "HO4P", "charge": -2}, "h2o_c": {"name": "H2O", "compartment": "c", "formula": "H2O", "charge": 0}, "h2o_e": {"name": "H2O", "compartment": "e", "formula": "H2O", "charge": 0}, "h_c": {"name": "H+", "compartment": "c", "formula": "H", "charge": 1}, "h_e": {"name": "H+", "compartment": "e", "formula": "H", "charge": 1}, "o2_c": {"name": "O2", "compartment": "c", "formula": "O2", "charge": 0}, "o2_e": {"name": "O2", "compartment": "e", "formula": "O2", "charge": 0}, "co2_c": {"name": "CO2", "compartment": "c", "formula": "CO2", "charge": 0}, "co2_e": {"name": "CO2", "compartment": "e", "formula": "CO2", "charge": 0}, "biomass_c": {"name": "Biomass", "compartment": "c", "formula": "C1H1.8O0.5N0.2", "charge": 0}}, "reactions": {"EX_glc__D_e": {"name": "D-glucose exchange", "metabolites": {"glc__D_e": -1.0}, "lower_bound": -20.0, "upper_bound": 1000.0, "objective_coefficient": 0.0}, "EX_o2_e": {"name": "O2 exchange", "metabolites": {"o2_e": -1.0}, "lower_bound": -1000.0, "upper_bound": 1000.0, "objective_coefficient": 0.0}, "EX_co2_e": {"name": "CO2 exchange", "metabolites": {"co2_e": -1.0}, "lower_bound": -1000.0, "upper_bound": 1000.0, "objective_coefficient": 0.0}, "EX_h2o_e": {"name": "H2O exchange", "metabolites": {"h2o_e": -1.0}, "lower_bound": -1000.0, "upper_bound": 1000.0, "objective_coefficient": 0.0}, "EX_h_e": {"name": "H+ exchange", "metabolites": {"h_e": -1.0}, "lower_bound": -1000.0, "upper_bound": 1000.0, "objective_coefficient": 0.0}, "EX_pi_e": {"name": "Phosphate exchange", "metabolites": {"pi_e": -1.0}, "lower_bound": -1000.0, "upper_bound": 1000.0, "objective_coefficient": 0.0}, "GLCt": {"name": "Glucose transport", "metabolites": {"glc__D_e": -1.0, "glc__D_c": 1.0}, "lower_bound": 0.0, "upper_bound": 1000.0, "objective_coefficient": 0.0}, "O2t": {"name": "O2 transport", "metabolites": {"o2_e": -1.0, "o2_c": 1.0}, "lower_bound": -1000.0, "upper_bound": 1000.0, "objective_coefficient": 0.0}, "CO2t": {"name": "CO2 transport", "metabolites": {"co2_c": -1.0, "co2_e": 1.0}, "lower_bound": -1000.0, "upper_bound": 1000.0, "objective_coefficient": 0.0}, "H2Ot": {"name": "H2O transport", "metabolites": {"h2o_c": -1.0, "h2o_e": 1.0}, "lower_bound": -1000.0, "upper_bound": 1000.0, "objective_coefficient": 0.0}, "Ht": {"name": "H+ transport", "metabolites": {"h_c": -1.0, "h_e": 1.0}, "lower_bound": -1000.0, "upper_bound": 1000.0, "objective_coefficient": 0.0}, "PIt": {"name": "Phosphate transport", "metabolites": {"pi_e": -1.0, "pi_c": 1.0}, "lower_bound": -1000.0, "upper_bound": 1000.0, "objective_coefficient": 0.0}, "RESP": {"name": "Respiration (simplified)", "metabolites": {"glc__D_c": -1.0, "o2_c": -6.0, "adp_c": -30.0, "pi_c": -30.0, "co2_c": 6.0, "h2o_c": 6.0, "atp_c": 30.0, "h_c": 30.0}, "lower_bound": 0.0, "upper_bound": 1000.0, "objective_coefficient": 0.0}, "ATPM": {"name": "ATP maintenance", "metabolites": {"atp_c": -1.0, "h2o_c": -1.0, "adp_c": 1.0, "pi_c": 1.0, "h_c": 1.0}, "lower_bound": 8.39, "upper_bound": 1000.0, "objective_coefficient": 0.0}, "BIOMASS": {"name": "Biomass production", "metabolites": {"glc__D_c": -1.0, "atp_c": -10.0, "h2o_c": -10.0, "biomass_c": 1.0, "adp_c": 10.0, "pi_c": 10.0, "h_c": 10.0}, "lower_bound": 0.0, "upper_bound": 1000.0, "objective_coefficient": 1.0}}, "objectives": {"BIOMASS": {"direction": "max", "objective_coefficient": 1.0}}, "gene_reaction_rules": {}, "notes": {"created": "2025-05-31", "description": "Simple E. coli model for quick testing of Mojo bioinformatics platform", "author": "Mojo Bioinformatics Team"}}