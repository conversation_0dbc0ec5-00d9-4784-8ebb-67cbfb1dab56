{"id": "ef065b53-0c22-454e-85de-48295658a5a8", "model_id": "ccbed541-a863-473a-87f6-4a8143f64446", "type": "schuster", "status": "completed", "input": {"connection_type": "metabolite", "connectivity_threshold": 1, "min_subnetwork_size": 3, "output_format": "subnetworks"}, "output": {"computation_time": 0.125, "connection_type": "metabolite", "connectivity_threshold": 1, "output_format": "subnetworks", "statistics": {"filtered_edges": 5, "filtered_nodes": 7, "largest_component": 4, "original_edges": 20, "original_nodes": 15, "smallest_component": 3}, "status": "completed", "subnetworks": [{"id": 1, "nodes": ["glucose", "g6p", "f6p", "fbp"], "connections": [{"from": "glucose", "to": "g6p", "weight": 0.8}, {"from": "g6p", "to": "f6p", "weight": 0.7}, {"from": "f6p", "to": "fbp", "weight": 0.6}], "size": 4}, {"id": 2, "nodes": ["pep", "pyruvate", "acetyl_coa"], "connections": [{"from": "pep", "to": "pyruvate", "weight": 0.9}, {"from": "pyruvate", "to": "acetyl_coa", "weight": 0.8}], "size": 3}], "total_subnetworks": 2}, "created_at": "2025-06-01T19:38:01.292105169Z", "updated_at": "2025-06-01T19:38:01.296662419Z", "started_at": "2025-06-01T19:38:01.296662044Z", "ended_at": "2025-06-01T19:38:01.296662044Z"}