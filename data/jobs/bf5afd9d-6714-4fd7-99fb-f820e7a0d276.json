{"id": "bf5afd9d-6714-4fd7-99fb-f820e7a0d276", "model_id": "a68af1e6-1a98-4a1d-a6b1-e3412cc593c1", "type": "schuster", "status": "failed", "input": {"connection_type": "metabolite", "connectivity_threshold": 0.1, "min_subnetwork_size": 3, "output_format": "adjacency"}, "error": "no processor found for job type: schuster", "created_at": "2025-06-01T18:40:29.808335794Z", "updated_at": "2025-06-01T18:40:29.810983419Z", "started_at": "2025-06-01T18:40:29.809872961Z"}