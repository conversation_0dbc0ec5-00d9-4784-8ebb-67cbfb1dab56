#!/bin/bash

# GEM-Fusion Accelerator Integration Test Script
# This script tests the complete workflow from model upload to FBA results

set -e  # Exit on any error

# Configuration
API_BASE="http://localhost:8080"
TEST_DIR="$(dirname "$0")/test_data"
TIMEOUT=30

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if required tools are available
check_dependencies() {
    log_info "Checking dependencies..."
    
    for cmd in curl jq; do
        if ! command -v $cmd &> /dev/null; then
            log_error "$cmd is required but not installed"
            exit 1
        fi
    done
    
    log_info "Dependencies check passed"
}

# Wait for service to be ready
wait_for_service() {
    log_info "Waiting for service to be ready..."
    
    for i in {1..30}; do
        if curl -s "$API_BASE/health" > /dev/null 2>&1; then
            log_info "Service is ready"
            return 0
        fi
        log_warn "Service not ready, waiting... ($i/30)"
        sleep 2
    done
    
    log_error "Service failed to start within timeout"
    exit 1
}

# Create test SBML model
create_test_model() {
    log_info "Creating test SBML model..."
    
    mkdir -p "$TEST_DIR"
    
    cat > "$TEST_DIR/test_model.xml" << 'EOF'
<?xml version="1.0" encoding="UTF-8"?>
<sbml xmlns="http://www.sbml.org/sbml/level3/version1/core" level="3" version="1">
  <model id="test_model" name="Test E.coli Core Model">
    <listOfCompartments>
      <compartment id="c" name="cytoplasm" spatialDimensions="3" size="1" constant="true"/>
      <compartment id="e" name="extracellular" spatialDimensions="3" size="1" constant="true"/>
    </listOfCompartments>
    
    <listOfSpecies>
      <species id="glc__D_e" name="D-Glucose external" compartment="e" hasOnlySubstanceUnits="false" boundaryCondition="false" constant="false"/>
      <species id="glc__D_c" name="D-Glucose cytoplasm" compartment="c" hasOnlySubstanceUnits="false" boundaryCondition="false" constant="false"/>
      <species id="g6p_c" name="Glucose 6-phosphate" compartment="c" hasOnlySubstanceUnits="false" boundaryCondition="false" constant="false"/>
      <species id="atp_c" name="ATP" compartment="c" hasOnlySubstanceUnits="false" boundaryCondition="false" constant="false"/>
      <species id="adp_c" name="ADP" compartment="c" hasOnlySubstanceUnits="false" boundaryCondition="false" constant="false"/>
      <species id="pi_c" name="Phosphate" compartment="c" hasOnlySubstanceUnits="false" boundaryCondition="false" constant="false"/>
      <species id="h_c" name="H+" compartment="c" hasOnlySubstanceUnits="false" boundaryCondition="false" constant="false"/>
      <species id="biomass_c" name="Biomass" compartment="c" hasOnlySubstanceUnits="false" boundaryCondition="false" constant="false"/>
    </listOfSpecies>
    
    <listOfReactions>
      <reaction id="EX_glc__D_e" name="Glucose exchange" reversible="true" fast="false">
        <listOfReactants>
          <speciesReference species="glc__D_e" stoichiometry="1" constant="true"/>
        </listOfReactants>
      </reaction>
      
      <reaction id="GLCpts" name="Glucose transport" reversible="false" fast="false">
        <listOfReactants>
          <speciesReference species="glc__D_e" stoichiometry="1" constant="true"/>
        </listOfReactants>
        <listOfProducts>
          <speciesReference species="glc__D_c" stoichiometry="1" constant="true"/>
        </listOfProducts>
      </reaction>
      
      <reaction id="HEX1" name="Hexokinase" reversible="false" fast="false">
        <listOfReactants>
          <speciesReference species="glc__D_c" stoichiometry="1" constant="true"/>
          <speciesReference species="atp_c" stoichiometry="1" constant="true"/>
        </listOfReactants>
        <listOfProducts>
          <speciesReference species="g6p_c" stoichiometry="1" constant="true"/>
          <speciesReference species="adp_c" stoichiometry="1" constant="true"/>
          <speciesReference species="h_c" stoichiometry="1" constant="true"/>
        </listOfProducts>
      </reaction>
      
      <reaction id="BIOMASS" name="Biomass production" reversible="false" fast="false">
        <listOfReactants>
          <speciesReference species="g6p_c" stoichiometry="1" constant="true"/>
          <speciesReference species="atp_c" stoichiometry="30" constant="true"/>
        </listOfReactants>
        <listOfProducts>
          <speciesReference species="biomass_c" stoichiometry="1" constant="true"/>
          <speciesReference species="adp_c" stoichiometry="30" constant="true"/>
          <speciesReference species="pi_c" stoichiometry="30" constant="true"/>
        </listOfProducts>
      </reaction>
      
      <reaction id="ATPM" name="ATP maintenance" reversible="false" fast="false">
        <listOfReactants>
          <speciesReference species="atp_c" stoichiometry="1" constant="true"/>
        </listOfReactants>
        <listOfProducts>
          <speciesReference species="adp_c" stoichiometry="1" constant="true"/>
          <speciesReference species="pi_c" stoichiometry="1" constant="true"/>
          <speciesReference species="h_c" stoichiometry="1" constant="true"/>
        </listOfProducts>
      </reaction>
    </listOfReactions>
  </model>
</sbml>
EOF
    
    log_info "Test model created at $TEST_DIR/test_model.xml"
}

# Test health check
test_health_check() {
    log_info "Testing health check..."
    
    response=$(curl -s "$API_BASE/health")
    success=$(echo "$response" | jq -r '.success')
    
    if [ "$success" = "true" ]; then
        log_info "✓ Health check passed"
    else
        log_error "✗ Health check failed"
        echo "$response"
        exit 1
    fi
}

# Test model upload
test_model_upload() {
    log_info "Testing model upload..."
    
    response=$(curl -s -X POST "$API_BASE/models" \
        -F "file=@$TEST_DIR/test_model.xml" \
        -F "name=Integration Test Model" \
        -F "description=Model for integration testing")
    
    success=$(echo "$response" | jq -r '.success')
    
    if [ "$success" = "true" ]; then
        MODEL_ID=$(echo "$response" | jq -r '.data.id')
        log_info "✓ Model uploaded successfully (ID: $MODEL_ID)"
    else
        log_error "✗ Model upload failed"
        echo "$response"
        exit 1
    fi
}

# Test model retrieval
test_model_retrieval() {
    log_info "Testing model retrieval..."
    
    response=$(curl -s "$API_BASE/models/$MODEL_ID")
    success=$(echo "$response" | jq -r '.success')
    
    if [ "$success" = "true" ]; then
        model_name=$(echo "$response" | jq -r '.data.name')
        log_info "✓ Model retrieved successfully (Name: $model_name)"
    else
        log_error "✗ Model retrieval failed"
        echo "$response"
        exit 1
    fi
}

# Test FBA submission
test_fba_submission() {
    log_info "Testing FBA submission..."
    
    fba_request='{
        "objective": "BIOMASS",
        "constraints": {
            "EX_glc__D_e": -10.0
        },
        "options": {
            "solver": "glpk"
        }
    }'
    
    response=$(curl -s -X POST "$API_BASE/models/$MODEL_ID/fba" \
        -H "Content-Type: application/json" \
        -d "$fba_request")
    
    success=$(echo "$response" | jq -r '.success')
    
    if [ "$success" = "true" ]; then
        JOB_ID=$(echo "$response" | jq -r '.data.id')
        log_info "✓ FBA job submitted successfully (Job ID: $JOB_ID)"
    else
        log_error "✗ FBA submission failed"
        echo "$response"
        exit 1
    fi
}

# Wait for job completion
wait_for_job_completion() {
    log_info "Waiting for job completion..."
    
    for i in $(seq 1 $TIMEOUT); do
        response=$(curl -s "$API_BASE/jobs/$JOB_ID/status")
        status=$(echo "$response" | jq -r '.data.job.status')
        
        case "$status" in
            "completed")
                log_info "✓ Job completed successfully"
                return 0
                ;;
            "failed")
                error=$(echo "$response" | jq -r '.data.job.error')
                log_error "✗ Job failed: $error"
                exit 1
                ;;
            "running"|"pending")
                log_info "Job status: $status (waiting... $i/$TIMEOUT)"
                sleep 1
                ;;
            *)
                log_error "Unknown job status: $status"
                echo "$response"
                exit 1
                ;;
        esac
    done
    
    log_error "Job did not complete within timeout"
    exit 1
}

# Test results retrieval
test_results_retrieval() {
    log_info "Testing results retrieval..."
    
    response=$(curl -s "$API_BASE/jobs/$JOB_ID/results")
    success=$(echo "$response" | jq -r '.success')
    
    if [ "$success" = "true" ]; then
        objective_value=$(echo "$response" | jq -r '.data.objective_value')
        solver_status=$(echo "$response" | jq -r '.data.status')
        solver_time=$(echo "$response" | jq -r '.data.solver_time')
        
        log_info "✓ Results retrieved successfully"
        log_info "  Objective value: $objective_value"
        log_info "  Solver status: $solver_status"
        log_info "  Solver time: ${solver_time}s"
        
        # Validate results
        if [ "$solver_status" = "optimal" ] && [ "$(echo "$objective_value > 0" | bc -l)" = "1" ]; then
            log_info "✓ Results validation passed"
        else
            log_error "✗ Results validation failed"
            exit 1
        fi
    else
        log_error "✗ Results retrieval failed"
        echo "$response"
        exit 1
    fi
}

# Test statistics endpoint
test_statistics() {
    log_info "Testing statistics endpoint..."
    
    response=$(curl -s "$API_BASE/stats")
    success=$(echo "$response" | jq -r '.success')
    
    if [ "$success" = "true" ]; then
        total_models=$(echo "$response" | jq -r '.data.total_models')
        log_info "✓ Statistics retrieved successfully (Total models: $total_models)"
    else
        log_error "✗ Statistics retrieval failed"
        echo "$response"
        exit 1
    fi
}

# Cleanup test data
cleanup() {
    log_info "Cleaning up test data..."
    rm -rf "$TEST_DIR"
    log_info "Cleanup completed"
}

# Main test execution
main() {
    log_info "Starting GEM-Fusion Accelerator Integration Tests"
    log_info "================================================"
    
    # Setup
    check_dependencies
    wait_for_service
    create_test_model
    
    # Core functionality tests
    test_health_check
    test_model_upload
    test_model_retrieval
    test_fba_submission
    wait_for_job_completion
    test_results_retrieval
    test_statistics
    
    # Cleanup
    cleanup
    
    log_info "================================================"
    log_info "✓ All integration tests passed successfully!"
    log_info "GEM-Fusion Accelerator is working correctly."
}

# Handle script interruption
trap cleanup EXIT

# Run main function
main "$@"
