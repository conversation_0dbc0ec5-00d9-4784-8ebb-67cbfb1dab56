import React, { useState } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  Typography,
  TextField,
  Button,
  Alert,
  CircularProgress,
  Divider,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
} from '@mui/material';
import {
  PlayArrow,
  AccountTree,
} from '@mui/icons-material';
import { useNavigate, useParams } from 'react-router-dom';
import { useForm, Controller } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import api from '../services/api';

interface SchusterFormData {
  connectivity_threshold: number;
  min_subnetwork_size: number;
  connection_type: 'metabolite' | 'reaction' | 'both';
  output_format: 'subnetworks' | 'adjacency' | 'both';
}

const schema = yup.object({
  connectivity_threshold: yup
    .number()
    .required('Connectivity threshold is required')
    .min(0, 'Must be at least 0')
    .max(1, 'Must be at most 1'),
  min_subnetwork_size: yup
    .number()
    .required('Minimum subnetwork size is required')
    .min(2, 'Must be at least 2')
    .max(1000, 'Must be at most 1000')
    .integer('Must be an integer'),
  connection_type: yup
    .mixed<'metabolite' | 'reaction' | 'both'>()
    .required('Connection type is required')
    .oneOf(['metabolite', 'reaction', 'both'], 'Must be metabolite, reaction, or both'),
  output_format: yup
    .mixed<'subnetworks' | 'adjacency' | 'both'>()
    .required('Output format is required')
    .oneOf(['subnetworks', 'adjacency', 'both'], 'Must be subnetworks, adjacency, or both'),
});

const SchusterAnalysis: React.FC = () => {
  const navigate = useNavigate();
  const { modelId } = useParams<{ modelId: string }>();
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const {
    control,
    handleSubmit,
    formState: { errors },
  } = useForm<SchusterFormData>({
    resolver: yupResolver(schema),
    defaultValues: {
      connectivity_threshold: 0.1,
      min_subnetwork_size: 3,
      connection_type: 'metabolite',
      output_format: 'subnetworks',
    },
  });

  const onSubmit = async (data: SchusterFormData) => {
    if (!modelId) return;

    try {
      setIsSubmitting(true);
      setError(null);
      setSuccess(null);

      const response = await api.post(`/models/${modelId}/schuster`, data);
      const responseData = response.data as any;

      if (responseData.success) {
        setSuccess('Schuster Decomposition analysis submitted successfully!');
        setTimeout(() => {
          navigate('/jobs');
        }, 2000);
      } else {
        setError(responseData.error || 'Failed to submit Schuster analysis');
      }
    } catch (err: any) {
      setError(err.response?.data?.error || err.message || 'Failed to submit Schuster analysis');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Box sx={{ maxWidth: 800, mx: 'auto' }}>
      <Card elevation={3}>
        <CardContent sx={{ p: 4 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
            <AccountTree sx={{ mr: 2, fontSize: 32, color: 'primary.main' }} />
            <Box>
              <Typography variant="h4" component="h1" gutterBottom>
                🔬 Network Decomposition Analysis
              </Typography>
              <Typography variant="body1" color="text.secondary">
                Advanced metabolic network decomposition with connectivity-based graph partitioning
              </Typography>
            </Box>
          </Box>

          <Divider sx={{ mb: 3 }} />

          {/* Enhanced Information Cards */}
          <Box sx={{ mb: 3 }}>
            <Box sx={{ 
              background: 'linear-gradient(135deg, rgba(124, 77, 255, 0.1) 0%, rgba(124, 77, 255, 0.05) 100%)',
              border: '1px solid rgba(124, 77, 255, 0.2)',
              borderRadius: 2,
              p: 3,
              mb: 3
            }}>
              <Typography variant="h6" sx={{ mb: 2, color: 'primary.main' }}>
                🧬 What is Network Decomposition?
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                Network decomposition analyzes metabolic networks by identifying connected subnetworks based on
                metabolite and reaction connectivity patterns using threshold-based graph partitioning.
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                • <strong>Connectivity Analysis:</strong> Measures connection strength between metabolites/reactions
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                • <strong>Threshold Filtering:</strong> Removes weak connections below specified threshold
              </Typography>
              <Typography variant="body2" color="text.secondary">
                • <strong>Subnetwork Identification:</strong> Finds connected components in the filtered network
              </Typography>
            </Box>

            <Box sx={{ 
              background: 'linear-gradient(135deg, rgba(255, 171, 0, 0.1) 0%, rgba(255, 171, 0, 0.05) 100%)',
              border: '1px solid rgba(255, 171, 0, 0.2)',
              borderRadius: 2,
              p: 3,
              mb: 3
            }}>
              <Typography variant="h6" sx={{ mb: 2, color: 'warning.main' }}>
                ⚡ Mojo-Accelerated Graph Analysis
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                This analysis leverages <strong>Mojo</strong> for high-performance graph algorithms and network analysis.
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                • <strong>Graph Algorithms:</strong> Optimized connected components and clustering
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                • <strong>Parallel Processing:</strong> Multi-threaded graph traversal and analysis
              </Typography>
              <Typography variant="body2" color="text.secondary">
                • <strong>Memory Efficiency:</strong> Optimized adjacency matrix operations
              </Typography>
            </Box>

            <Box sx={{ 
              background: 'linear-gradient(135deg, rgba(0, 230, 118, 0.1) 0%, rgba(0, 230, 118, 0.05) 100%)',
              border: '1px solid rgba(0, 230, 118, 0.2)',
              borderRadius: 2,
              p: 3
            }}>
              <Typography variant="h6" sx={{ mb: 2, color: 'success.main' }}>
                🎯 Connection Types & Output
              </Typography>
              <Box sx={{ display: 'flex', gap: 2, mb: 2 }}>
                <Chip label="Metabolite Connections" color="primary" variant="outlined" />
                <Chip label="Reaction Connections" color="secondary" variant="outlined" />
              </Box>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                • <strong>Metabolite:</strong> Connections based on shared metabolites between reactions
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                • <strong>Reaction:</strong> Direct reaction-to-reaction connectivity patterns
              </Typography>
              <Typography variant="body2" color="text.secondary">
                • <strong>Output:</strong> Subnetwork lists, adjacency matrices, or both formats
              </Typography>
            </Box>
          </Box>

          {error && (
            <Alert severity="error" sx={{ mb: 3 }}>
              {error}
            </Alert>
          )}

          {success && (
            <Alert severity="success" sx={{ mb: 3 }}>
              {success}
            </Alert>
          )}

          <Box component="form" onSubmit={handleSubmit(onSubmit)}>
            <Box sx={{ display: 'flex', gap: 3, mb: 3 }}>
              <Controller
                name="connectivity_threshold"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    fullWidth
                    label="Connectivity Threshold"
                    type="number"
                    error={!!errors.connectivity_threshold}
                    helperText={
                      errors.connectivity_threshold?.message ||
                      'Minimum connection strength (0.0-1.0)'
                    }
                    disabled={isSubmitting}
                    InputProps={{
                      inputProps: { min: 0, max: 1, step: 0.01 }
                    }}
                  />
                )}
              />

              <Controller
                name="min_subnetwork_size"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    fullWidth
                    label="Min Subnetwork Size"
                    type="number"
                    error={!!errors.min_subnetwork_size}
                    helperText={
                      errors.min_subnetwork_size?.message ||
                      'Minimum nodes per subnetwork (2-1000)'
                    }
                    disabled={isSubmitting}
                    InputProps={{
                      inputProps: { min: 2, max: 1000, step: 1 }
                    }}
                  />
                )}
              />
            </Box>

            <Box sx={{ display: 'flex', gap: 3, mb: 3 }}>
              <Controller
                name="connection_type"
                control={control}
                render={({ field }) => (
                  <FormControl fullWidth>
                    <InputLabel>Connection Type</InputLabel>
                    <Select
                      {...field}
                      label="Connection Type"
                      error={!!errors.connection_type}
                      disabled={isSubmitting}
                    >
                      <MenuItem value="metabolite">Metabolite Connections</MenuItem>
                      <MenuItem value="reaction">Reaction Connections</MenuItem>
                      <MenuItem value="both">Both Types</MenuItem>
                    </Select>
                    {errors.connection_type && (
                      <Typography variant="caption" color="error" sx={{ mt: 1, ml: 2 }}>
                        {errors.connection_type.message}
                      </Typography>
                    )}
                  </FormControl>
                )}
              />

              <Controller
                name="output_format"
                control={control}
                render={({ field }) => (
                  <FormControl fullWidth>
                    <InputLabel>Output Format</InputLabel>
                    <Select
                      {...field}
                      label="Output Format"
                      error={!!errors.output_format}
                      disabled={isSubmitting}
                    >
                      <MenuItem value="subnetworks">Subnetwork Lists</MenuItem>
                      <MenuItem value="adjacency">Adjacency Matrix</MenuItem>
                      <MenuItem value="both">Both Formats</MenuItem>
                    </Select>
                    {errors.output_format && (
                      <Typography variant="caption" color="error" sx={{ mt: 1, ml: 2 }}>
                        {errors.output_format.message}
                      </Typography>
                    )}
                  </FormControl>
                )}
              />
            </Box>

            <Card variant="outlined" sx={{ bgcolor: 'rgba(124, 77, 255, 0.05)', mb: 3 }}>
              <CardContent>
                <Typography variant="subtitle2" gutterBottom>
                  Network Decomposition Parameters Guide
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  • <strong>Connectivity Threshold:</strong> Higher values create fewer, more tightly connected subnetworks
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  • <strong>Min Subnetwork Size:</strong> Filters out small disconnected components
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  • <strong>Connection Type:</strong> Metabolite connections are usually more biologically meaningful
                </Typography>
                <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                  <strong>Recommended:</strong> Start with 0.1 threshold and metabolite connections for most networks
                </Typography>
              </CardContent>
            </Card>

            <Box sx={{ display: 'flex', gap: 2, justifyContent: 'flex-end' }}>
              <Button
                variant="outlined"
                onClick={() => navigate('/models')}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                variant="contained"
                size="large"
                startIcon={
                  isSubmitting ? (
                    <CircularProgress size={20} color="inherit" />
                  ) : (
                    <PlayArrow />
                  )
                }
                disabled={isSubmitting}
                sx={{ minWidth: 200 }}
              >
                {isSubmitting ? 'Submitting...' : 'Run Network Decomposition'}
              </Button>
            </Box>
          </Box>
        </CardContent>
      </Card>
    </Box>
  );
};

export default SchusterAnalysis;
