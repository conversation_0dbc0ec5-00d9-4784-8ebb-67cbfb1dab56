import React, { useState } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  Typography,
  TextField,
  Button,
  Alert,
  CircularProgress,
  Divider,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
} from '@mui/material';
import {
  Science,
  PlayArrow,
  AccountTree,
} from '@mui/icons-material';
import { useNavigate, useParams } from 'react-router-dom';
import { useForm, Controller } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import api from '../services/api';

interface SchusterFormData {
  max_modes: number;
  tolerance: number;
  method: 'canonical' | 'extreme';
}

const schema = yup.object({
  max_modes: yup
    .number()
    .required('Maximum modes is required')
    .min(1, 'Must be at least 1')
    .max(10000, 'Must be at most 10000')
    .integer('Must be an integer'),
  tolerance: yup
    .number()
    .required('Tolerance is required')
    .min(1e-15, 'Must be at least 1e-15')
    .max(1e-3, 'Must be at most 1e-3'),
  method: yup
    .mixed<'canonical' | 'extreme'>()
    .required('Method is required')
    .oneOf(['canonical', 'extreme'], 'Must be either canonical or extreme'),
});

const SchusterAnalysis: React.FC = () => {
  const navigate = useNavigate();
  const { modelId } = useParams<{ modelId: string }>();
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const {
    control,
    handleSubmit,
    formState: { errors },
  } = useForm<SchusterFormData>({
    resolver: yupResolver(schema),
    defaultValues: {
      max_modes: 1000,
      tolerance: 1e-10,
      method: 'canonical',
    },
  });

  const onSubmit = async (data: SchusterFormData) => {
    if (!modelId) return;

    try {
      setIsSubmitting(true);
      setError(null);
      setSuccess(null);

      const response = await api.post(`/models/${modelId}/schuster`, data);
      const responseData = response.data as any;

      if (responseData.success) {
        setSuccess('Schuster Decomposition analysis submitted successfully!');
        setTimeout(() => {
          navigate('/jobs');
        }, 2000);
      } else {
        setError(responseData.error || 'Failed to submit Schuster analysis');
      }
    } catch (err: any) {
      setError(err.response?.data?.error || err.message || 'Failed to submit Schuster analysis');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Box sx={{ maxWidth: 800, mx: 'auto' }}>
      <Card elevation={3}>
        <CardContent sx={{ p: 4 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
            <AccountTree sx={{ mr: 2, fontSize: 32, color: 'primary.main' }} />
            <Box>
              <Typography variant="h4" component="h1" gutterBottom>
                🔬 Schuster Decomposition Analysis
              </Typography>
              <Typography variant="body1" color="text.secondary">
                Advanced metabolic network decomposition with Mojo acceleration
              </Typography>
            </Box>
          </Box>

          <Divider sx={{ mb: 3 }} />

          {/* Enhanced Information Cards */}
          <Box sx={{ mb: 3 }}>
            <Box sx={{ 
              background: 'linear-gradient(135deg, rgba(124, 77, 255, 0.1) 0%, rgba(124, 77, 255, 0.05) 100%)',
              border: '1px solid rgba(124, 77, 255, 0.2)',
              borderRadius: 2,
              p: 3,
              mb: 3
            }}>
              <Typography variant="h6" sx={{ mb: 2, color: 'primary.main' }}>
                🧬 What is Schuster Decomposition?
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                Schuster Decomposition is an advanced algorithm for analyzing metabolic networks by decomposing them into 
                elementary flux modes using sophisticated mathematical techniques developed by Schuster et al.
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                • <strong>Network Splitting:</strong> Decomposes complex networks into simpler, analyzable components
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                • <strong>Canonical vs Extreme:</strong> Two different mathematical approaches for mode computation
              </Typography>
              <Typography variant="body2" color="text.secondary">
                • <strong>Optimized Algorithm:</strong> More efficient than traditional elementary mode analysis for large networks
              </Typography>
            </Box>

            <Box sx={{ 
              background: 'linear-gradient(135deg, rgba(255, 171, 0, 0.1) 0%, rgba(255, 171, 0, 0.05) 100%)',
              border: '1px solid rgba(255, 171, 0, 0.2)',
              borderRadius: 2,
              p: 3,
              mb: 3
            }}>
              <Typography variant="h6" sx={{ mb: 2, color: 'warning.main' }}>
                ⚡ Mojo-Accelerated Computing
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                This analysis leverages <strong>Mojo</strong> for unprecedented performance in metabolic network decomposition.
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                • <strong>Matrix Decomposition:</strong> Highly optimized linear algebra operations
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                • <strong>Parallel Processing:</strong> Multi-core utilization for complex computations
              </Typography>
              <Typography variant="body2" color="text.secondary">
                • <strong>Memory Optimization:</strong> Efficient handling of large stoichiometric matrices
              </Typography>
            </Box>

            <Box sx={{ 
              background: 'linear-gradient(135deg, rgba(0, 230, 118, 0.1) 0%, rgba(0, 230, 118, 0.05) 100%)',
              border: '1px solid rgba(0, 230, 118, 0.2)',
              borderRadius: 2,
              p: 3
            }}>
              <Typography variant="h6" sx={{ mb: 2, color: 'success.main' }}>
                🎯 Method Comparison
              </Typography>
              <Box sx={{ display: 'flex', gap: 2, mb: 2 }}>
                <Chip label="Canonical Method" color="primary" variant="outlined" />
                <Chip label="Extreme Method" color="secondary" variant="outlined" />
              </Box>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                • <strong>Canonical:</strong> Standard approach, more stable for most networks
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                • <strong>Extreme:</strong> Advanced method, better for highly constrained systems
              </Typography>
              <Typography variant="body2" color="text.secondary">
                • <strong>Applications:</strong> Pathway analysis, network robustness, metabolic engineering
              </Typography>
            </Box>
          </Box>

          {error && (
            <Alert severity="error" sx={{ mb: 3 }}>
              {error}
            </Alert>
          )}

          {success && (
            <Alert severity="success" sx={{ mb: 3 }}>
              {success}
            </Alert>
          )}

          <Box component="form" onSubmit={handleSubmit(onSubmit)}>
            <Box sx={{ display: 'flex', gap: 3, mb: 3 }}>
              <Controller
                name="max_modes"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    fullWidth
                    label="Maximum Modes"
                    type="number"
                    error={!!errors.max_modes}
                    helperText={
                      errors.max_modes?.message ||
                      'Maximum number of modes to compute (1-10000)'
                    }
                    disabled={isSubmitting}
                    InputProps={{
                      inputProps: { min: 1, max: 10000, step: 1 }
                    }}
                  />
                )}
              />

              <Controller
                name="tolerance"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    fullWidth
                    label="Tolerance"
                    type="number"
                    error={!!errors.tolerance}
                    helperText={
                      errors.tolerance?.message ||
                      'Numerical tolerance (e.g., 1e-10)'
                    }
                    disabled={isSubmitting}
                    InputProps={{
                      inputProps: { min: 1e-15, max: 1e-3, step: 1e-10 }
                    }}
                  />
                )}
              />
            </Box>

            <Controller
              name="method"
              control={control}
              render={({ field }) => (
                <FormControl fullWidth sx={{ mb: 3 }}>
                  <InputLabel>Decomposition Method</InputLabel>
                  <Select
                    {...field}
                    label="Decomposition Method"
                    error={!!errors.method}
                    disabled={isSubmitting}
                  >
                    <MenuItem value="canonical">Canonical Method</MenuItem>
                    <MenuItem value="extreme">Extreme Method</MenuItem>
                  </Select>
                  {errors.method && (
                    <Typography variant="caption" color="error" sx={{ mt: 1, ml: 2 }}>
                      {errors.method.message}
                    </Typography>
                  )}
                </FormControl>
              )}
            />

            <Card variant="outlined" sx={{ bgcolor: 'rgba(124, 77, 255, 0.05)', mb: 3 }}>
              <CardContent>
                <Typography variant="subtitle2" gutterBottom>
                  Analysis Parameters Guide
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  • <strong>Max Modes:</strong> Higher values provide more complete analysis but increase computation time
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  • <strong>Tolerance:</strong> Lower values increase precision but may cause numerical instability
                </Typography>
                <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                  <strong>Recommended:</strong> Start with default values and adjust based on network complexity
                </Typography>
              </CardContent>
            </Card>

            <Box sx={{ display: 'flex', gap: 2, justifyContent: 'flex-end' }}>
              <Button
                variant="outlined"
                onClick={() => navigate('/models')}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                variant="contained"
                size="large"
                startIcon={
                  isSubmitting ? (
                    <CircularProgress size={20} color="inherit" />
                  ) : (
                    <PlayArrow />
                  )
                }
                disabled={isSubmitting}
                sx={{ minWidth: 200 }}
              >
                {isSubmitting ? 'Submitting...' : 'Run Schuster Analysis'}
              </Button>
            </Box>
          </Box>
        </CardContent>
      </Card>
    </Box>
  );
};

export default SchusterAnalysis;
