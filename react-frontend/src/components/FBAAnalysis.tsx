import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  CardContent,
  Typography,
  TextField,
  Button,
  Box,
  Alert,
  CircularProgress,
  Divider,
  Chip,

  Paper,
} from '@mui/material';
import {
  PlayArrow as PlayArrowIcon,
  Add as AddIcon,
  Delete as DeleteIcon,
} from '@mui/icons-material';
import { useParams, useNavigate } from 'react-router-dom';
import { getModel, submitFBA, Model } from '../services/api';

const FBAAnalysis: React.FC = () => {
  const { modelId } = useParams<{ modelId: string }>();
  const navigate = useNavigate();
  
  const [model, setModel] = useState<Model | null>(null);
  const [objective, setObjective] = useState('BIOMASS');
  const [constraints, setConstraints] = useState<{ [key: string]: number }>({});
  const [newConstraintReaction, setNewConstraintReaction] = useState('');
  const [newConstraintValue, setNewConstraintValue] = useState('');
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (modelId) {
      fetchModel();
    }
  }, [modelId]);

  const fetchModel = async () => {
    if (!modelId) return;

    try {
      setLoading(true);
      setError(null);
      
      const response = await getModel(modelId);
      if (response.success && response.data) {
        setModel(response.data);
      } else {
        setError(response.error || 'Failed to load model');
      }
    } catch (err) {
      setError('Failed to load model');
    } finally {
      setLoading(false);
    }
  };

  const handleAddConstraint = () => {
    if (newConstraintReaction.trim() && newConstraintValue.trim()) {
      const value = parseFloat(newConstraintValue);
      if (!isNaN(value)) {
        setConstraints({
          ...constraints,
          [newConstraintReaction.trim()]: value,
        });
        setNewConstraintReaction('');
        setNewConstraintValue('');
      }
    }
  };

  const handleRemoveConstraint = (reaction: string) => {
    const newConstraints = { ...constraints };
    delete newConstraints[reaction];
    setConstraints(newConstraints);
  };

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();
    
    if (!modelId || !objective.trim()) {
      setError('Please provide an objective function');
      return;
    }

    try {
      setSubmitting(true);
      setError(null);
      
      const response = await submitFBA(modelId, objective.trim(), constraints);
      
      if (response.success && response.data) {
        // Navigate to job results page
        navigate(`/jobs/${response.data.id}/results`);
      } else {
        setError(response.error || 'Failed to submit FBA analysis');
      }
    } catch (err) {
      setError('Failed to submit FBA analysis');
    } finally {
      setSubmitting(false);
    }
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  if (!model) {
    return (
      <Alert severity="error">
        Model not found
      </Alert>
    );
  }

  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        FBA Analysis
      </Typography>
      
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Model Information
          </Typography>
          <Box display="flex" flexWrap="wrap" gap={2}>
            <Box flex="1 1 200px" minWidth="200px">
              <Typography variant="body2" color="textSecondary">Name:</Typography>
              <Typography variant="body1">{model.name}</Typography>
            </Box>
            <Box flex="1 1 200px" minWidth="200px">
              <Typography variant="body2" color="textSecondary">Format:</Typography>
              <Chip label={model.format.toUpperCase()} size="small" />
            </Box>
            <Box flex="1 1 100%" minWidth="100%">
              <Typography variant="body2" color="textSecondary">Description:</Typography>
              <Typography variant="body1">{model.description || 'No description'}</Typography>
            </Box>
          </Box>
        </CardContent>
      </Card>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      <Card>
        <CardContent>
          <form onSubmit={handleSubmit}>
            <Typography variant="h6" gutterBottom>
              Analysis Parameters
            </Typography>

            <TextField
              fullWidth
              label="Objective Function"
              value={objective}
              onChange={(e) => setObjective(e.target.value)}
              required
              sx={{ mb: 3 }}
              placeholder="e.g., BIOMASS, ATPM"
              helperText="Enter the reaction ID to maximize (e.g., BIOMASS for growth rate)"
            />

            <Divider sx={{ my: 3 }} />

            <Typography variant="h6" gutterBottom>
              Constraints
            </Typography>
            
            <Typography variant="body2" color="textSecondary" sx={{ mb: 2 }}>
              Add flux constraints to specific reactions (optional)
            </Typography>

            {/* Existing Constraints */}
            {Object.keys(constraints).length > 0 && (
              <Paper variant="outlined" sx={{ p: 2, mb: 2 }}>
                <Typography variant="subtitle2" gutterBottom>
                  Current Constraints:
                </Typography>
                {Object.entries(constraints).map(([reaction, value]) => (
                  <Box key={reaction} display="flex" alignItems="center" gap={2} mb={1}>
                    <Typography variant="body2" sx={{ minWidth: 120 }}>
                      {reaction}:
                    </Typography>
                    <Typography variant="body2" fontWeight="medium">
                      {value}
                    </Typography>
                    <Button
                      size="small"
                      color="error"
                      onClick={() => handleRemoveConstraint(reaction)}
                      startIcon={<DeleteIcon />}
                    >
                      Remove
                    </Button>
                  </Box>
                ))}
              </Paper>
            )}

            {/* Add New Constraint */}
            <Box display="flex" gap={2} alignItems="center" mb={3}>
              <TextField
                label="Reaction ID"
                value={newConstraintReaction}
                onChange={(e) => setNewConstraintReaction(e.target.value)}
                placeholder="e.g., EX_glc"
                size="small"
                sx={{ minWidth: 150 }}
              />
              <TextField
                label="Flux Value"
                type="number"
                value={newConstraintValue}
                onChange={(e) => setNewConstraintValue(e.target.value)}
                placeholder="e.g., -10.0"
                size="small"
                sx={{ minWidth: 120 }}
              />
              <Button
                variant="outlined"
                onClick={handleAddConstraint}
                disabled={!newConstraintReaction.trim() || !newConstraintValue.trim()}
                startIcon={<AddIcon />}
              >
                Add
              </Button>
            </Box>

            <Divider sx={{ my: 3 }} />

            <Box display="flex" gap={2} justifyContent="flex-end">
              <Button
                variant="outlined"
                onClick={() => navigate('/models')}
                disabled={submitting}
              >
                Cancel
              </Button>
              
              <Button
                type="submit"
                variant="contained"
                disabled={!objective.trim() || submitting}
                startIcon={submitting ? <CircularProgress size={20} /> : <PlayArrowIcon />}
              >
                {submitting ? 'Running Analysis...' : 'Run FBA'}
              </Button>
            </Box>
          </form>
        </CardContent>
      </Card>
    </Box>
  );
};

export default FBAAnalysis;
