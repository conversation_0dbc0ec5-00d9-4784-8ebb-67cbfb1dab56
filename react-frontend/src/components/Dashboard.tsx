import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  Card,
  CardContent,
  Typography,
  Button,
  Box,
  CircularProgress,
  Alert,
  Chip,
  Avatar,
  IconButton,
  Tooltip,
  Paper,
  Divider,
  Stack,
  alpha,
  LinearProgress,
} from '@mui/material';
import {
  Upload as UploadIcon,
  List as ListIcon,
  Analytics as AnalyticsIcon,
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
  Science,
  Biotech,
  Memory,
  Speed,
  Timeline,
  AccountTree,
  Refresh,
  PlayArrow,
  Visibility,
  CloudUpload,
  DataUsage,
  Psychology,
  AutoGraph,
  Work,
  TrendingUp,
  Storage,
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { getSystemStats, checkHealth, SystemStats } from '../services/api';

const Dashboard: React.FC = () => {
  const navigate = useNavigate();
  const [stats, setStats] = useState<SystemStats | null>(null);
  const [healthStatus, setHealthStatus] = useState<'healthy' | 'unhealthy' | 'checking'>('checking');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        
        // Check health
        const healthResponse = await checkHealth();
        setHealthStatus(healthResponse.success ? 'healthy' : 'unhealthy');
        
        // Get stats
        const statsResponse = await getSystemStats();
        if (statsResponse.success && statsResponse.data) {
          setStats(statsResponse.data);
        }
      } catch (err) {
        setError('Failed to load dashboard data');
        setHealthStatus('unhealthy');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
    
    // Refresh every 30 seconds
    const interval = setInterval(fetchData, 30000);
    return () => clearInterval(interval);
  }, []);

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      {/* Hero Section */}
      <Box
        sx={{
          background: 'linear-gradient(135deg, rgba(0, 229, 255, 0.1) 0%, rgba(124, 77, 255, 0.1) 100%)',
          borderRadius: 3,
          p: 4,
          mb: 4,
          border: '1px solid rgba(0, 229, 255, 0.2)',
          position: 'relative',
          overflow: 'hidden',
          '&::before': {
            content: '""',
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            background: 'url("data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%2300e5ff" fill-opacity="0.05"%3E%3Ccircle cx="30" cy="30" r="2"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")',
            opacity: 0.3,
          },
        }}
      >
        <Box display="flex" alignItems="center" justifyContent="space-between" mb={2}>
          <Box display="flex" alignItems="center" gap={2}>
            <Avatar
              sx={{
                background: 'linear-gradient(135deg, #00e5ff 0%, #7c4dff 100%)',
                width: 56,
                height: 56,
              }}
            >
              <Biotech sx={{ fontSize: 32 }} />
            </Avatar>
            <Box>
              <Typography variant="h1" sx={{ mb: 0.5 }}>
                🧬 BioLab Genesis
              </Typography>
              <Typography variant="h6" color="text.secondary">
                Advanced Metabolic Network Analysis Platform
              </Typography>
            </Box>
          </Box>
          <Tooltip title="Refresh System Status">
            <IconButton
              onClick={() => window.location.reload()}
              sx={{
                background: 'rgba(0, 229, 255, 0.1)',
                '&:hover': { background: 'rgba(0, 229, 255, 0.2)' },
              }}
            >
              <Refresh />
            </IconButton>
          </Tooltip>
        </Box>

        {error && (
          <Alert
            severity="error"
            sx={{
              mb: 3,
              background: 'rgba(255, 23, 68, 0.1)',
              border: '1px solid rgba(255, 23, 68, 0.3)',
            }}
          >
            {error}
          </Alert>
        )}

        {/* System Status */}
        <Box display="flex" alignItems="center" gap={2}>
          <Science sx={{ color: 'primary.main' }} />
          <Typography variant="h6" color="text.primary">
            Quantum Computing Engine Status:
          </Typography>
          <Chip
            icon={healthStatus === 'healthy' ? <CheckCircleIcon /> : <ErrorIcon />}
            label={healthStatus === 'healthy' ? 'OPERATIONAL' : 'OFFLINE'}
            color={healthStatus === 'healthy' ? 'success' : 'error'}
            sx={{ fontWeight: 600, letterSpacing: '0.05em' }}
          />
          {healthStatus === 'healthy' && (
            <Box display="flex" alignItems="center" gap={1} ml={2}>
              <Memory sx={{ color: 'success.main', fontSize: 20 }} />
              <Typography variant="body2" color="success.main" sx={{ fontWeight: 500 }}>
                Mojo Acceleration Active
              </Typography>
            </Box>
          )}
        </Box>
      </Box>

      {/* Advanced Analytics Dashboard */}
      {stats && (
        <Grid container spacing={3} sx={{ mb: 4 }}>
          <Grid size={{ xs: 12, sm: 6, lg: 3 }}>
            <Card
              sx={{
                background: 'linear-gradient(135deg, rgba(0, 229, 255, 0.1) 0%, rgba(0, 229, 255, 0.05) 100%)',
                border: '1px solid rgba(0, 229, 255, 0.3)',
                position: 'relative',
                overflow: 'hidden',
              }}
            >
              <CardContent>
                <Box display="flex" alignItems="center" justifyContent="space-between" mb={2}>
                  <Avatar sx={{ background: 'linear-gradient(135deg, #00e5ff 0%, #0091ea 100%)' }}>
                    <Storage />
                  </Avatar>
                  <TrendingUp sx={{ color: 'success.main' }} />
                </Box>
                <Typography variant="caption" color="text.secondary" sx={{ textTransform: 'uppercase', letterSpacing: '0.1em' }}>
                  Metabolic Models
                </Typography>
                <Typography variant="h3" sx={{ fontWeight: 700, mb: 1 }}>
                  {stats.models_count}
                </Typography>
                <LinearProgress
                  variant="determinate"
                  value={Math.min((stats.models_count / 100) * 100, 100)}
                  sx={{
                    height: 6,
                    borderRadius: 3,
                    backgroundColor: 'rgba(0, 229, 255, 0.1)',
                    '& .MuiLinearProgress-bar': {
                      background: 'linear-gradient(90deg, #00e5ff 0%, #0091ea 100%)',
                    },
                  }}
                />
              </CardContent>
            </Card>
          </Grid>

          <Grid size={{ xs: 12, sm: 6, lg: 3 }}>
            <Card
              sx={{
                background: 'linear-gradient(135deg, rgba(124, 77, 255, 0.1) 0%, rgba(124, 77, 255, 0.05) 100%)',
                border: '1px solid rgba(124, 77, 255, 0.3)',
              }}
            >
              <CardContent>
                <Box display="flex" alignItems="center" justifyContent="space-between" mb={2}>
                  <Avatar sx={{ background: 'linear-gradient(135deg, #7c4dff 0%, #3f1dcb 100%)' }}>
                    <Work />
                  </Avatar>
                  <AutoGraph sx={{ color: 'primary.main' }} />
                </Box>
                <Typography variant="caption" color="text.secondary" sx={{ textTransform: 'uppercase', letterSpacing: '0.1em' }}>
                  Analysis Jobs
                </Typography>
                <Typography variant="h3" sx={{ fontWeight: 700, mb: 1 }}>
                  {stats.jobs_count}
                </Typography>
                <LinearProgress
                  variant="determinate"
                  value={Math.min((stats.jobs_count / 50) * 100, 100)}
                  sx={{
                    height: 6,
                    borderRadius: 3,
                    backgroundColor: 'rgba(124, 77, 255, 0.1)',
                    '& .MuiLinearProgress-bar': {
                      background: 'linear-gradient(90deg, #7c4dff 0%, #3f1dcb 100%)',
                    },
                  }}
                />
              </CardContent>
            </Card>
          </Grid>

          <Grid size={{ xs: 12, sm: 6, lg: 3 }}>
            <Card
              sx={{
                background: 'linear-gradient(135deg, rgba(0, 230, 118, 0.1) 0%, rgba(0, 230, 118, 0.05) 100%)',
                border: '1px solid rgba(0, 230, 118, 0.3)',
              }}
            >
              <CardContent>
                <Box display="flex" alignItems="center" justifyContent="space-between" mb={2}>
                  <Avatar sx={{ background: 'linear-gradient(135deg, #00e676 0%, #00b248 100%)' }}>
                    <CheckCircleIcon />
                  </Avatar>
                  <Speed sx={{ color: 'success.main' }} />
                </Box>
                <Typography variant="caption" color="text.secondary" sx={{ textTransform: 'uppercase', letterSpacing: '0.1em' }}>
                  Completed
                </Typography>
                <Typography variant="h3" sx={{ fontWeight: 700, mb: 1, color: 'success.main' }}>
                  {stats.completed_jobs}
                </Typography>
                <LinearProgress
                  variant="determinate"
                  value={stats.jobs_count > 0 ? (stats.completed_jobs / stats.jobs_count) * 100 : 0}
                  sx={{
                    height: 6,
                    borderRadius: 3,
                    backgroundColor: 'rgba(0, 230, 118, 0.1)',
                    '& .MuiLinearProgress-bar': {
                      background: 'linear-gradient(90deg, #00e676 0%, #00b248 100%)',
                    },
                  }}
                />
              </CardContent>
            </Card>
          </Grid>

          <Grid size={{ xs: 12, sm: 6, lg: 3 }}>
            <Card
              sx={{
                background: 'linear-gradient(135deg, rgba(255, 23, 68, 0.1) 0%, rgba(255, 23, 68, 0.05) 100%)',
                border: '1px solid rgba(255, 23, 68, 0.3)',
              }}
            >
              <CardContent>
                <Box display="flex" alignItems="center" justifyContent="space-between" mb={2}>
                  <Avatar sx={{ background: 'linear-gradient(135deg, #ff1744 0%, #c4001d 100%)' }}>
                    <ErrorIcon />
                  </Avatar>
                  <Psychology sx={{ color: 'error.main' }} />
                </Box>
                <Typography variant="caption" color="text.secondary" sx={{ textTransform: 'uppercase', letterSpacing: '0.1em' }}>
                  Failed Jobs
                </Typography>
                <Typography variant="h3" sx={{ fontWeight: 700, mb: 1, color: 'error.main' }}>
                  {stats.failed_jobs}
                </Typography>
                <LinearProgress
                  variant="determinate"
                  value={stats.jobs_count > 0 ? (stats.failed_jobs / stats.jobs_count) * 100 : 0}
                  sx={{
                    height: 6,
                    borderRadius: 3,
                    backgroundColor: 'rgba(255, 23, 68, 0.1)',
                    '& .MuiLinearProgress-bar': {
                      background: 'linear-gradient(90deg, #ff1744 0%, #c4001d 100%)',
                    },
                  }}
                />
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}

      {/* Mission Control Center */}
      <Box mb={3}>
        <Typography variant="h4" sx={{ mb: 1, display: 'flex', alignItems: 'center', gap: 2 }}>
          <Science sx={{ color: 'primary.main' }} />
          Mission Control Center
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Launch advanced bioinformatics analyses with quantum-accelerated computing
        </Typography>
      </Box>

      <Grid container spacing={3}>
        <Grid size={{ xs: 12, md: 4 }}>
          <Card
            sx={{
              height: '100%',
              background: 'linear-gradient(135deg, rgba(255, 107, 53, 0.1) 0%, rgba(255, 107, 53, 0.05) 100%)',
              border: '1px solid rgba(255, 107, 53, 0.3)',
              cursor: 'pointer',
              transition: 'all 0.3s ease',
              '&:hover': {
                transform: 'translateY(-4px)',
                boxShadow: '0 20px 40px rgba(255, 107, 53, 0.2)',
              },
            }}
            onClick={() => navigate('/upload')}
          >
            <CardContent sx={{ p: 4 }}>
              <Box display="flex" flexDirection="column" alignItems="center" textAlign="center">
                <Avatar
                  sx={{
                    background: 'linear-gradient(135deg, #ff6b35 0%, #c73e00 100%)',
                    width: 80,
                    height: 80,
                    mb: 3,
                  }}
                >
                  <CloudUpload sx={{ fontSize: 40 }} />
                </Avatar>
                <Typography variant="h5" gutterBottom sx={{ fontWeight: 600 }}>
                  🧬 Upload Genome
                </Typography>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 3, lineHeight: 1.6 }}>
                  Deploy new metabolic models to the quantum analysis chamber
                </Typography>
                <Button
                  variant="contained"
                  size="large"
                  startIcon={<PlayArrow />}
                  sx={{
                    background: 'linear-gradient(135deg, #ff6b35 0%, #c73e00 100%)',
                    px: 4,
                    py: 1.5,
                  }}
                >
                  Initialize Upload
                </Button>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid size={{ xs: 12, md: 4 }}>
          <Card
            sx={{
              height: '100%',
              background: 'linear-gradient(135deg, rgba(0, 229, 255, 0.1) 0%, rgba(0, 229, 255, 0.05) 100%)',
              border: '1px solid rgba(0, 229, 255, 0.3)',
              cursor: 'pointer',
              transition: 'all 0.3s ease',
              '&:hover': {
                transform: 'translateY(-4px)',
                boxShadow: '0 20px 40px rgba(0, 229, 255, 0.2)',
              },
            }}
            onClick={() => navigate('/models')}
          >
            <CardContent sx={{ p: 4 }}>
              <Box display="flex" flexDirection="column" alignItems="center" textAlign="center">
                <Avatar
                  sx={{
                    background: 'linear-gradient(135deg, #00e5ff 0%, #0091ea 100%)',
                    width: 80,
                    height: 80,
                    mb: 3,
                  }}
                >
                  <DataUsage sx={{ fontSize: 40 }} />
                </Avatar>
                <Typography variant="h5" gutterBottom sx={{ fontWeight: 600 }}>
                  🔬 Model Library
                </Typography>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 3, lineHeight: 1.6 }}>
                  Access your collection of metabolic network models
                </Typography>
                <Button
                  variant="contained"
                  size="large"
                  startIcon={<Visibility />}
                  sx={{
                    background: 'linear-gradient(135deg, #00e5ff 0%, #0091ea 100%)',
                    px: 4,
                    py: 1.5,
                  }}
                >
                  Browse Models
                </Button>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid size={{ xs: 12, md: 4 }}>
          <Card
            sx={{
              height: '100%',
              background: 'linear-gradient(135deg, rgba(124, 77, 255, 0.1) 0%, rgba(124, 77, 255, 0.05) 100%)',
              border: '1px solid rgba(124, 77, 255, 0.3)',
              cursor: 'pointer',
              transition: 'all 0.3s ease',
              '&:hover': {
                transform: 'translateY(-4px)',
                boxShadow: '0 20px 40px rgba(124, 77, 255, 0.2)',
              },
            }}
            onClick={() => navigate('/analysis')}
          >
            <CardContent sx={{ p: 4 }}>
              <Box display="flex" flexDirection="column" alignItems="center" textAlign="center">
                <Avatar
                  sx={{
                    background: 'linear-gradient(135deg, #7c4dff 0%, #3f1dcb 100%)',
                    width: 80,
                    height: 80,
                    mb: 3,
                  }}
                >
                  <Psychology sx={{ fontSize: 40 }} />
                </Avatar>
                <Typography variant="h5" gutterBottom sx={{ fontWeight: 600 }}>
                  ⚡ Quantum Analysis
                </Typography>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 3, lineHeight: 1.6 }}>
                  Execute FBA & EMA with Mojo-accelerated algorithms
                </Typography>
                <Button
                  variant="contained"
                  size="large"
                  startIcon={<Timeline />}
                  sx={{
                    background: 'linear-gradient(135deg, #7c4dff 0%, #3f1dcb 100%)',
                    px: 4,
                    py: 1.5,
                  }}
                >
                  Launch Analysis
                </Button>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid size={{ xs: 12, md: 6 }}>
          <Card
            sx={{
              background: 'linear-gradient(135deg, rgba(0, 230, 118, 0.1) 0%, rgba(0, 230, 118, 0.05) 100%)',
              border: '1px solid rgba(0, 230, 118, 0.3)',
              cursor: 'pointer',
              transition: 'all 0.3s ease',
              '&:hover': {
                transform: 'translateY(-2px)',
                boxShadow: '0 12px 24px rgba(0, 230, 118, 0.2)',
              },
            }}
            onClick={() => navigate('/jobs')}
          >
            <CardContent sx={{ p: 3 }}>
              <Box display="flex" alignItems="center" gap={3}>
                <Avatar
                  sx={{
                    background: 'linear-gradient(135deg, #00e676 0%, #00b248 100%)',
                    width: 60,
                    height: 60,
                  }}
                >
                  <Work sx={{ fontSize: 30 }} />
                </Avatar>
                <Box flex={1}>
                  <Typography variant="h6" sx={{ fontWeight: 600, mb: 1 }}>
                    📊 Job Dashboard
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Monitor real-time analysis progress and results
                  </Typography>
                </Box>
                <Button
                  variant="outlined"
                  startIcon={<Visibility />}
                  sx={{
                    borderColor: 'rgba(0, 230, 118, 0.5)',
                    color: 'success.main',
                    '&:hover': {
                      borderColor: 'success.main',
                      background: 'rgba(0, 230, 118, 0.1)',
                    },
                  }}
                >
                  View Jobs
                </Button>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid size={{ xs: 12, md: 6 }}>
          <Card
            sx={{
              background: 'linear-gradient(135deg, rgba(255, 171, 0, 0.1) 0%, rgba(255, 171, 0, 0.05) 100%)',
              border: '1px solid rgba(255, 171, 0, 0.3)',
            }}
          >
            <CardContent sx={{ p: 3 }}>
              <Box display="flex" alignItems="center" gap={3}>
                <Avatar
                  sx={{
                    background: 'linear-gradient(135deg, #ffab00 0%, #c67c00 100%)',
                    width: 60,
                    height: 60,
                  }}
                >
                  <Memory sx={{ fontSize: 30 }} />
                </Avatar>
                <Box flex={1}>
                  <Typography variant="h6" sx={{ fontWeight: 600, mb: 1 }}>
                    🚀 Mojo Engine
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    High-performance computing for complex analyses
                  </Typography>
                </Box>
                <Chip
                  label="ACTIVE"
                  color="warning"
                  sx={{ fontWeight: 600, letterSpacing: '0.05em' }}
                />
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default Dashboard;
