import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>,
  CardContent,
  Typo<PERSON>,
  Button,
  Box,
  CircularProgress,
  Al<PERSON>,
  Chip,
} from '@mui/material';
import {
  Upload as UploadIcon,
  List as ListIcon,
  Analytics as AnalyticsIcon,
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { getSystemStats, checkHealth, SystemStats } from '../services/api';

const Dashboard: React.FC = () => {
  const navigate = useNavigate();
  const [stats, setStats] = useState<SystemStats | null>(null);
  const [healthStatus, setHealthStatus] = useState<'healthy' | 'unhealthy' | 'checking'>('checking');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        
        // Check health
        const healthResponse = await checkHealth();
        setHealthStatus(healthResponse.success ? 'healthy' : 'unhealthy');
        
        // Get stats
        const statsResponse = await getSystemStats();
        if (statsResponse.success && statsResponse.data) {
          setStats(statsResponse.data);
        }
      } catch (err) {
        setError('Failed to load dashboard data');
        setHealthStatus('unhealthy');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
    
    // Refresh every 30 seconds
    const interval = setInterval(fetchData, 30000);
    return () => clearInterval(interval);
  }, []);

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        Dashboard
      </Typography>
      
      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {/* Health Status */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Box display="flex" alignItems="center" gap={2}>
            <Typography variant="h6">System Status</Typography>
            <Chip
              icon={healthStatus === 'healthy' ? <CheckCircleIcon /> : <ErrorIcon />}
              label={healthStatus === 'healthy' ? 'Healthy' : 'Unhealthy'}
              color={healthStatus === 'healthy' ? 'success' : 'error'}
            />
          </Box>
        </CardContent>
      </Card>

      {/* Stats Cards */}
      {stats && (
        <Box display="flex" flexWrap="wrap" gap={3} sx={{ mb: 3 }}>
          <Box flex="1 1 250px" minWidth="250px">
            <Card>
              <CardContent>
                <Typography color="textSecondary" gutterBottom>
                  Total Models
                </Typography>
                <Typography variant="h4">
                  {stats.models_count}
                </Typography>
              </CardContent>
            </Card>
          </Box>

          <Box flex="1 1 250px" minWidth="250px">
            <Card>
              <CardContent>
                <Typography color="textSecondary" gutterBottom>
                  Total Jobs
                </Typography>
                <Typography variant="h4">
                  {stats.jobs_count}
                </Typography>
              </CardContent>
            </Card>
          </Box>

          <Box flex="1 1 250px" minWidth="250px">
            <Card>
              <CardContent>
                <Typography color="textSecondary" gutterBottom>
                  Completed Jobs
                </Typography>
                <Typography variant="h4" color="success.main">
                  {stats.completed_jobs}
                </Typography>
              </CardContent>
            </Card>
          </Box>

          <Box flex="1 1 250px" minWidth="250px">
            <Card>
              <CardContent>
                <Typography color="textSecondary" gutterBottom>
                  Failed Jobs
                </Typography>
                <Typography variant="h4" color="error.main">
                  {stats.failed_jobs}
                </Typography>
              </CardContent>
            </Card>
          </Box>
        </Box>
      )}

      {/* Quick Actions */}
      <Typography variant="h5" gutterBottom>
        Quick Actions
      </Typography>
      
      <Box display="flex" flexWrap="wrap" gap={3}>
        <Box flex="1 1 300px" minWidth="300px">
          <Card sx={{ height: '100%' }}>
            <CardContent>
              <Box display="flex" flexDirection="column" alignItems="center" textAlign="center">
                <UploadIcon sx={{ fontSize: 48, color: 'primary.main', mb: 2 }} />
                <Typography variant="h6" gutterBottom>
                  Upload Model
                </Typography>
                <Typography variant="body2" color="textSecondary" sx={{ mb: 2 }}>
                  Upload a new metabolic model for analysis
                </Typography>
                <Button
                  variant="contained"
                  onClick={() => navigate('/upload')}
                  fullWidth
                >
                  Upload
                </Button>
              </Box>
            </CardContent>
          </Card>
        </Box>

        <Box flex="1 1 300px" minWidth="300px">
          <Card sx={{ height: '100%' }}>
            <CardContent>
              <Box display="flex" flexDirection="column" alignItems="center" textAlign="center">
                <ListIcon sx={{ fontSize: 48, color: 'primary.main', mb: 2 }} />
                <Typography variant="h6" gutterBottom>
                  View Models
                </Typography>
                <Typography variant="body2" color="textSecondary" sx={{ mb: 2 }}>
                  Browse and manage your metabolic models
                </Typography>
                <Button
                  variant="contained"
                  onClick={() => navigate('/models')}
                  fullWidth
                >
                  View Models
                </Button>
              </Box>
            </CardContent>
          </Card>
        </Box>

        <Box flex="1 1 300px" minWidth="300px">
          <Card sx={{ height: '100%' }}>
            <CardContent>
              <Box display="flex" flexDirection="column" alignItems="center" textAlign="center">
                <AnalyticsIcon sx={{ fontSize: 48, color: 'primary.main', mb: 2 }} />
                <Typography variant="h6" gutterBottom>
                  Run Analysis
                </Typography>
                <Typography variant="body2" color="textSecondary" sx={{ mb: 2 }}>
                  Perform FBA analysis on your models
                </Typography>
                <Button
                  variant="contained"
                  onClick={() => navigate('/models')}
                  fullWidth
                >
                  Analyze
                </Button>
              </Box>
            </CardContent>
          </Card>
        </Box>
      </Box>
    </Box>
  );
};

export default Dashboard;
