import React, { useState } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  Ty<PERSON>graphy,
  TextField,
  Button,
  Alert,
  CircularProgress,
  Chip,
  Divider,
} from '@mui/material';
import { Science, PlayArrow } from '@mui/icons-material';
import { useParams, useNavigate } from 'react-router-dom';
import { useForm, Controller } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import api from '../services/api';

const schema = yup.object().shape({
  max_modes: yup
    .number()
    .required('Max modes is required')
    .min(1, 'Must be at least 1')
    .max(10000, 'Must be less than 10,000'),
  tolerance: yup
    .number()
    .required('Tolerance is required')
    .min(1e-15, 'Must be greater than 1e-15')
    .max(1e-3, 'Must be less than 1e-3'),
});

interface EMAFormData {
  max_modes: number;
  tolerance: number;
}

const EMAAnalysis: React.FC = () => {
  const { modelId } = useParams<{ modelId: string }>();
  const navigate = useNavigate();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  const {
    control,
    handleSubmit,
    formState: { errors },
  } = useForm<EMAFormData>({
    resolver: yupResolver(schema),
    defaultValues: {
      max_modes: 1000,
      tolerance: 1e-10,
    },
  });

  const onSubmit = async (data: EMAFormData) => {
    if (!modelId) {
      setError('Model ID is required');
      return;
    }

    setIsSubmitting(true);
    setError(null);
    setSuccess(null);

    try {
      const response = await api.post(`/api/v1/models/${modelId}/ema`, data);
      const responseData = response.data as any;

      if (responseData.success) {
        const jobId = responseData.data.id;
        setSuccess(`EMA analysis submitted successfully! Job ID: ${jobId}`);

        // Navigate to job results after a short delay
        setTimeout(() => {
          navigate(`/jobs/${jobId}/results`);
        }, 2000);
      } else {
        setError(responseData.error || 'Failed to submit EMA analysis');
      }
    } catch (err: any) {
      setError(err.response?.data?.error || err.message || 'Failed to submit EMA analysis');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Box sx={{ maxWidth: 800, mx: 'auto' }}>
      <Card elevation={3}>
        <CardContent sx={{ p: 4 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
            <Science sx={{ mr: 2, fontSize: 32, color: 'primary.main' }} />
            <Box>
              <Typography variant="h4" component="h1" gutterBottom>
                Elementary Mode Analysis
              </Typography>
              <Typography variant="body1" color="text.secondary">
                Compute elementary modes for metabolic pathway analysis
              </Typography>
            </Box>
          </Box>

          <Divider sx={{ mb: 3 }} />

          {/* Information Card */}
          <Card variant="outlined" sx={{ mb: 3, bgcolor: 'primary.50' }}>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                About Elementary Mode Analysis
              </Typography>
              <Typography variant="body2" color="text.secondary" paragraph>
                Elementary Mode Analysis (EMA) identifies the minimal set of reactions that can 
                operate at steady state. These elementary modes represent the basic pathways 
                through which the metabolic network can function.
              </Typography>
              <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                <Chip label="Pathway Analysis" size="small" />
                <Chip label="Network Decomposition" size="small" />
                <Chip label="Steady State" size="small" />
                <Chip label="Minimal Pathways" size="small" />
              </Box>
            </CardContent>
          </Card>

          {error && (
            <Alert severity="error" sx={{ mb: 3 }}>
              {error}
            </Alert>
          )}

          {success && (
            <Alert severity="success" sx={{ mb: 3 }}>
              {success}
            </Alert>
          )}

          <Box component="form" onSubmit={handleSubmit(onSubmit)}>
            <Box sx={{ display: 'flex', gap: 3, mb: 3 }}>
              <Controller
                name="max_modes"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    fullWidth
                    label="Maximum Modes"
                    type="number"
                    error={!!errors.max_modes}
                    helperText={
                      errors.max_modes?.message ||
                      'Maximum number of elementary modes to compute'
                    }
                    disabled={isSubmitting}
                    InputProps={{
                      inputProps: { min: 1, max: 10000, step: 1 }
                    }}
                  />
                )}
              />

              <Controller
                name="tolerance"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    fullWidth
                    label="Tolerance"
                    type="number"
                    error={!!errors.tolerance}
                    helperText={
                      errors.tolerance?.message ||
                      'Numerical tolerance for computations (e.g., 1e-10)'
                    }
                    disabled={isSubmitting}
                    InputProps={{
                      inputProps: { min: 1e-15, max: 1e-3, step: 1e-10 }
                    }}
                  />
                )}
              />
            </Box>

            <Card variant="outlined" sx={{ bgcolor: 'grey.50', mb: 3 }}>
              <CardContent>
                <Typography variant="subtitle2" gutterBottom>
                  Analysis Parameters
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  • <strong>Max Modes:</strong> Limits computation time and memory usage
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  • <strong>Tolerance:</strong> Controls numerical precision (smaller = more precise)
                </Typography>
                <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                  <strong>Note:</strong> Large networks may require lower max_modes values
                  to complete in reasonable time.
                </Typography>
              </CardContent>
            </Card>

            <Box sx={{ display: 'flex', gap: 2, justifyContent: 'flex-end' }}>
              <Button
                variant="outlined"
                onClick={() => navigate('/models')}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                variant="contained"
                size="large"
                startIcon={
                  isSubmitting ? (
                    <CircularProgress size={20} color="inherit" />
                  ) : (
                    <PlayArrow />
                  )
                }
                disabled={isSubmitting}
                sx={{ minWidth: 160 }}
              >
                {isSubmitting ? 'Submitting...' : 'Run EMA Analysis'}
              </Button>
            </Box>
          </Box>
        </CardContent>
      </Card>
    </Box>
  );
};

export default EMAAnalysis;
