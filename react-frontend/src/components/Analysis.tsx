import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  Grid,
  Card,
  CardContent,
  CardActions,
  Button,
  Chip,
  Alert,
  CircularProgress,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  List,
  ListItem,
  ListItemText,
  ListItemButton,
  Divider,
} from '@mui/material';
import {
  Science,
  Timeline,
  AccountTree,
  PlayArrow,
  Description,
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import api from '../services/api';

interface Model {
  id: string;
  name: string;
  description: string;
  format: string;
  file_path: string;
  metadata: any;
  created_at: string;
  updated_at: string;
}

const Analysis: React.FC = () => {
  const navigate = useNavigate();
  const [models, setModels] = useState<Model[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedModel, setSelectedModel] = useState<Model | null>(null);
  const [modelDialogOpen, setModelDialogOpen] = useState(false);
  const [analysisType, setAnalysisType] = useState<'fba' | 'ema' | null>(null);

  useEffect(() => {
    fetchModels();
  }, []);

  const fetchModels = async () => {
    try {
      setLoading(true);
      const response = await api.get('/models');
      const responseData = response.data as any;
      if (responseData.success) {
        setModels(responseData.data?.models || []);
      } else {
        setError(responseData.error || 'Failed to fetch models');
      }
    } catch (err: any) {
      setError(err.response?.data?.error || err.message || 'Failed to fetch models');
    } finally {
      setLoading(false);
    }
  };

  const handleAnalysisSelect = (type: 'fba' | 'ema') => {
    setAnalysisType(type);
    setModelDialogOpen(true);
  };

  const handleModelSelect = (model: Model) => {
    setSelectedModel(model);
    setModelDialogOpen(false);
    if (analysisType && model) {
      navigate(`/models/${model.id}/${analysisType}`);
    }
  };

  const analysisOptions = [
    {
      id: 'fba',
      title: 'Flux Balance Analysis (FBA)',
      description: 'Predict metabolic flux distributions under steady-state conditions using linear programming optimization.',
      icon: <Timeline sx={{ fontSize: 40 }} />,
      color: 'primary' as const,
      features: [
        'Steady-state flux prediction',
        'Biomass optimization',
        'Gene knockout simulation',
        'Constraint-based modeling',
      ],
    },
    {
      id: 'ema',
      title: 'Elementary Mode Analysis (EMA)',
      description: 'Identify all possible minimal metabolic pathways (elementary modes) in the network using Mojo acceleration.',
      icon: <AccountTree sx={{ fontSize: 40 }} />,
      color: 'secondary' as const,
      features: [
        'Complete pathway enumeration',
        'Mojo-accelerated computation',
        'Minimal pathway identification',
        'Network decomposition',
      ],
    },
  ];

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      <Typography variant="h4" component="h1" gutterBottom sx={{ fontWeight: 'bold', mb: 3 }}>
        🧬 Metabolic Analysis
      </Typography>

      <Typography variant="body1" color="text.secondary" sx={{ mb: 4 }}>
        Choose an analysis method to explore your metabolic models. Each analysis provides unique insights into cellular metabolism.
      </Typography>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      <Grid container spacing={3}>
        {analysisOptions.map((option) => (
          <Grid size={{ xs: 12, md: 6 }} key={option.id}>
            <Card 
              sx={{ 
                height: '100%',
                display: 'flex',
                flexDirection: 'column',
                transition: 'transform 0.2s, box-shadow 0.2s',
                '&:hover': {
                  transform: 'translateY(-4px)',
                  boxShadow: 4,
                },
              }}
            >
              <CardContent sx={{ flexGrow: 1 }}>
                <Box display="flex" alignItems="center" mb={2}>
                  <Box 
                    sx={{ 
                      color: `${option.color}.main`,
                      mr: 2,
                    }}
                  >
                    {option.icon}
                  </Box>
                  <Typography variant="h6" component="h2" sx={{ fontWeight: 'bold' }}>
                    {option.title}
                  </Typography>
                </Box>

                <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                  {option.description}
                </Typography>

                <Typography variant="subtitle2" sx={{ mb: 1, fontWeight: 'bold' }}>
                  Key Features:
                </Typography>
                <Box>
                  {option.features.map((feature, index) => (
                    <Chip
                      key={index}
                      label={feature}
                      size="small"
                      variant="outlined"
                      sx={{ mr: 1, mb: 1 }}
                    />
                  ))}
                </Box>
              </CardContent>

              <CardActions sx={{ p: 2, pt: 0 }}>
                <Button
                  variant="contained"
                  color={option.color}
                  startIcon={<PlayArrow />}
                  fullWidth
                  onClick={() => handleAnalysisSelect(option.id as 'fba' | 'ema')}
                  disabled={!models || models.length === 0}
                >
                  Start {option.id.toUpperCase()} Analysis
                </Button>
              </CardActions>
            </Card>
          </Grid>
        ))}
      </Grid>

      {(!models || models.length === 0) && !loading && (
        <Alert severity="info" sx={{ mt: 3 }}>
          <Typography variant="body2">
            No models available for analysis. Please{' '}
            <Button
              variant="text"
              size="small"
              onClick={() => navigate('/upload')}
              sx={{ textTransform: 'none', p: 0, minWidth: 'auto' }}
            >
              upload a model
            </Button>{' '}
            first.
          </Typography>
        </Alert>
      )}

      {/* Model Selection Dialog */}
      <Dialog
        open={modelDialogOpen}
        onClose={() => setModelDialogOpen(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>
          <Box display="flex" alignItems="center">
            <Description sx={{ mr: 1 }} />
            Select a Model for {analysisType?.toUpperCase()} Analysis
          </Box>
        </DialogTitle>
        <DialogContent>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
            Choose a metabolic model to analyze:
          </Typography>
          <List>
            {models && models.length > 0 ? models.map((model, index) => (
              <React.Fragment key={model.id}>
                <ListItem disablePadding>
                  <ListItemButton onClick={() => handleModelSelect(model)}>
                    <ListItemText
                      primary={model.name}
                      secondary={
                        <Box>
                          <Typography variant="body2" color="text.secondary">
                            {model.description || 'No description available'}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            Format: {model.format} • Created: {new Date(model.created_at).toLocaleDateString()}
                          </Typography>
                        </Box>
                      }
                    />
                  </ListItemButton>
                </ListItem>
                {index < models.length - 1 && <Divider />}
              </React.Fragment>
            )) : (
              <ListItem>
                <ListItemText
                  primary="No models available"
                  secondary="Please upload a model first to perform analysis."
                />
              </ListItem>
            )}
          </List>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setModelDialogOpen(false)}>Cancel</Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default Analysis;
