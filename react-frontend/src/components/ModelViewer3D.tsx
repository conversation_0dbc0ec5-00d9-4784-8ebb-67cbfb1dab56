import React, { useRef, useEffect, useState, useCallback } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Button,
  IconButton,
  Tooltip,
  Slider,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Switch,
  FormControlLabel,
  Paper,
  Chip,
  Tabs,
  Tab,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Divider,
  ButtonGroup,
  SpeedDial,
  SpeedDialAction,
  SpeedDialIcon,
  Alert,
  LinearProgress,
  Badge,
  ToggleButton,
  ToggleButtonGroup,
  Fab
} from '@mui/material';
import {
  FullscreenExit,
  Fullscreen,
  Refresh,
  CameraAlt,
  Settings,
  Visibility,
  VisibilityOff,
  ZoomIn,
  ZoomOut,
  RotateLeft,
  RotateRight,
  ExpandMore,
  Palette,
  Timeline,
  ThreeDRotation,
  FilterList,
  Animation,
  Save,
  Share,
  Info,
  PlayArrow,
  Pause,
  Stop,
  Speed,
  Tune,
  ViewInAr,
  GridOn,
  GridOff,
  CenterFocusStrong,
  AspectRatio,
  Layers,
  ColorLens
} from '@mui/icons-material';

interface ModelViewer3DProps {
  modelId: string;
  modelData?: any;
  width?: number;
  height?: number;
}

interface Node3D {
  id: string;
  name: string;
  type: 'metabolite' | 'reaction' | 'gene';
  position: { x: number; y: number; z: number };
  connections: string[];
  size: number;
  color: string;
}

interface Edge3D {
  id: string;
  source: string;
  target: string;
  weight: number;
  type: 'substrate' | 'product' | 'regulation';
}

const ModelViewer3D: React.FC<ModelViewer3DProps> = ({
  modelId,
  modelData,
  width = 800,
  height = 600,
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const animationRef = useRef<number>(0);

  // Core state
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [nodes, setNodes] = useState<Node3D[]>([]);
  const [edges, setEdges] = useState<Edge3D[]>([]);
  const [selectedNode, setSelectedNode] = useState<Node3D | null>(null);
  const [hoveredNode, setHoveredNode] = useState<Node3D | null>(null);

  // Advanced visualization controls
  const [showMetabolites, setShowMetabolites] = useState(true);
  const [showReactions, setShowReactions] = useState(true);
  const [showGenes, setShowGenes] = useState(false);
  const [showEdges, setShowEdges] = useState(true);
  const [showLabels, setShowLabels] = useState(true);
  const [showGrid, setShowGrid] = useState(false);

  // Rendering controls
  const [nodeSize, setNodeSize] = useState(1.0);
  const [edgeThickness, setEdgeThickness] = useState(1.0);
  const [labelSize, setLabelSize] = useState(1.0);
  const [transparency, setTransparency] = useState(1.0);
  const [zoom, setZoom] = useState(1.0);
  const [rotation, setRotation] = useState({ x: 0, y: 0, z: 0 });
  const [offset, setOffset] = useState({ x: 0, y: 0 });

  // Layout and styling
  const [layoutType, setLayoutType] = useState('force-directed');
  const [colorScheme, setColorScheme] = useState('type-based');
  const [renderMode, setRenderMode] = useState('standard');
  const [animationSpeed, setAnimationSpeed] = useState(1.0);

  // Animation and interaction
  const [isAnimating, setIsAnimating] = useState(false);
  const [animationType, setAnimationType] = useState('rotation');
  const [highlightConnections, setHighlightConnections] = useState(false);
  const [filterByType, setFilterByType] = useState<string[]>(['metabolite', 'reaction', 'gene']);

  // UI state
  const [activeTab, setActiveTab] = useState(0);
  const [speedDialOpen, setSpeedDialOpen] = useState(false);
  const [showAdvancedControls, setShowAdvancedControls] = useState(false);

  useEffect(() => {
    initializeViewer();
    generateMockData();
  }, [modelId]);

  const drawGrid = (ctx: CanvasRenderingContext2D) => {
    ctx.strokeStyle = 'rgba(255, 255, 255, 0.1)';
    ctx.lineWidth = 1;

    const gridSize = 50;
    for (let x = 0; x <= width; x += gridSize) {
      ctx.beginPath();
      ctx.moveTo(x, 0);
      ctx.lineTo(x, height);
      ctx.stroke();
    }

    for (let y = 0; y <= height; y += gridSize) {
      ctx.beginPath();
      ctx.moveTo(0, y);
      ctx.lineTo(width, y);
      ctx.stroke();
    }
  };

  const transform3D = (x: number, y: number, z: number, rotX: number, rotY: number, rotZ: number) => {
    // Simple 3D rotation transformation
    let newX = x;
    let newY = y * Math.cos(rotX) - z * Math.sin(rotX);
    let newZ = y * Math.sin(rotX) + z * Math.cos(rotX);

    const tempX = newX * Math.cos(rotY) + newZ * Math.sin(rotY);
    newZ = -newX * Math.sin(rotY) + newZ * Math.cos(rotY);
    newX = tempX;

    const finalX = newX * Math.cos(rotZ) - newY * Math.sin(rotZ);
    const finalY = newX * Math.sin(rotZ) + newY * Math.cos(rotZ);

    return { x: finalX, y: finalY, z: newZ };
  };

  const initializeViewer = () => {
    if (!canvasRef.current) return;

    // Set canvas size
    const canvas = canvasRef.current;
    canvas.width = width;
    canvas.height = height;

    // Initialize 2D context for now (can be upgraded to WebGL later)
    const ctx = canvas.getContext('2d');
    if (!ctx) {
      console.error('2D context not supported');
      return;
    }

    setIsLoading(false);
  };

  const generateMockData = () => {
    // Generate mock 3D network data for demonstration
    const mockNodes: Node3D[] = [
      {
        id: 'glucose',
        name: 'Glucose',
        type: 'metabolite',
        position: { x: 0, y: 0, z: 0 },
        connections: ['glk', 'pgi'],
        size: 1.2,
        color: '#4CAF50',
      },
      {
        id: 'g6p',
        name: 'Glucose-6-phosphate',
        type: 'metabolite',
        position: { x: 2, y: 1, z: 0 },
        connections: ['glk', 'pgi', 'zwf'],
        size: 1.0,
        color: '#4CAF50',
      },
      {
        id: 'f6p',
        name: 'Fructose-6-phosphate',
        type: 'metabolite',
        position: { x: 4, y: 0, z: 1 },
        connections: ['pgi', 'pfk'],
        size: 1.0,
        color: '#4CAF50',
      },
      {
        id: 'glk',
        name: 'Glucokinase',
        type: 'reaction',
        position: { x: 1, y: 0.5, z: 0 },
        connections: ['glucose', 'g6p'],
        size: 0.8,
        color: '#FF9800',
      },
      {
        id: 'pgi',
        name: 'Phosphoglucose isomerase',
        type: 'reaction',
        position: { x: 3, y: 0.5, z: 0.5 },
        connections: ['g6p', 'f6p'],
        size: 0.8,
        color: '#FF9800',
      },
      {
        id: 'pfk',
        name: 'Phosphofructokinase',
        type: 'reaction',
        position: { x: 5, y: 0.5, z: 1 },
        connections: ['f6p'],
        size: 0.8,
        color: '#FF9800',
      },
      {
        id: 'glk_gene',
        name: 'GLK gene',
        type: 'gene',
        position: { x: 1, y: -1, z: 0 },
        connections: ['glk'],
        size: 0.6,
        color: '#9C27B0',
      },
    ];

    const mockEdges: Edge3D[] = [
      { id: 'e1', source: 'glucose', target: 'glk', weight: 1.0, type: 'substrate' },
      { id: 'e2', source: 'glk', target: 'g6p', weight: 1.0, type: 'product' },
      { id: 'e3', source: 'g6p', target: 'pgi', weight: 0.8, type: 'substrate' },
      { id: 'e4', source: 'pgi', target: 'f6p', weight: 0.8, type: 'product' },
      { id: 'e5', source: 'f6p', target: 'pfk', weight: 0.6, type: 'substrate' },
      { id: 'e6', source: 'glk_gene', target: 'glk', weight: 0.5, type: 'regulation' },
    ];

    setNodes(mockNodes);
    setEdges(mockEdges);
  };

  const renderFrame = useCallback(() => {
    if (!canvasRef.current) return;

    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Clear the canvas with gradient background
    const gradient = ctx.createLinearGradient(0, 0, width, height);
    gradient.addColorStop(0, '#0a0a0a');
    gradient.addColorStop(0.5, '#1a1a2e');
    gradient.addColorStop(1, '#16213e');
    ctx.fillStyle = gradient;
    ctx.fillRect(0, 0, width, height);

    // Draw grid if enabled
    if (showGrid) {
      drawGrid(ctx);
    }

    // Set up rendering parameters with zoom and offset
    const centerX = width / 2 + offset.x;
    const centerY = height / 2 + offset.y;
    const scale = (Math.min(width, height) / 400) * zoom;

    // Apply rotation matrix for 3D effect
    const rotX = rotation.x * Math.PI / 180;
    const rotY = rotation.y * Math.PI / 180;
    const rotZ = rotation.z * Math.PI / 180;
    
    // Draw edges first (behind nodes) with 3D transformation
    if (showEdges) {
      edges.forEach(edge => {
        const sourceNode = nodes.find(n => n.id === edge.source);
        const targetNode = nodes.find(n => n.id === edge.target);

        if (sourceNode && targetNode) {
          // Apply 3D transformation
          const source3D = transform3D(
            sourceNode.position.x * scale * 100,
            sourceNode.position.y * scale * 100,
            sourceNode.position.z * scale * 100,
            rotX, rotY, rotZ
          );
          const target3D = transform3D(
            targetNode.position.x * scale * 100,
            targetNode.position.y * scale * 100,
            targetNode.position.z * scale * 100,
            rotX, rotY, rotZ
          );

          const sourceX = centerX + source3D.x;
          const sourceY = centerY + source3D.y;
          const targetX = centerX + target3D.x;
          const targetY = centerY + target3D.y;

          // Enhanced edge styling
          ctx.beginPath();
          ctx.moveTo(sourceX, sourceY);
          ctx.lineTo(targetX, targetY);

          // Color based on edge type and selection
          let edgeColor = '#4CAF50';
          if (edge.type === 'regulation') edgeColor = '#9C27B0';
          if (edge.type === 'substrate') edgeColor = '#2196F3';
          if (edge.type === 'product') edgeColor = '#FF9800';

          // Highlight if connected to selected node
          if (selectedNode && (edge.source === selectedNode.id || edge.target === selectedNode.id)) {
            edgeColor = '#FFFF00';
            ctx.shadowColor = '#FFFF00';
            ctx.shadowBlur = 10;
          }

          ctx.strokeStyle = edgeColor;
          ctx.globalAlpha = transparency * 0.8;
          ctx.lineWidth = Math.max(1, edge.weight * edgeThickness * 2);
          ctx.stroke();
          ctx.shadowBlur = 0;
          ctx.globalAlpha = 1;
        }
      });
    }

    // Draw nodes on top with 3D transformation and enhanced effects
    const sortedNodes = nodes
      .map(node => {
        const transformed = transform3D(
          node.position.x * scale * 100,
          node.position.y * scale * 100,
          node.position.z * scale * 100,
          rotX, rotY, rotZ
        );
        return { ...node, transformed, depth: transformed.z };
      })
      .sort((a, b) => a.depth - b.depth); // Sort by depth for proper z-ordering

    sortedNodes.forEach(node => {
      if (!showMetabolites && node.type === 'metabolite') return;
      if (!showReactions && node.type === 'reaction') return;
      if (!showGenes && node.type === 'gene') return;
      if (!filterByType.includes(node.type)) return;

      const x = centerX + node.transformed.x;
      const y = centerY + node.transformed.y;
      const depthScale = 1 + (node.transformed.z / 1000); // Depth-based scaling
      const radius = Math.max(8, node.size * nodeSize * 20 * depthScale);

      // Enhanced node styling based on render mode
      ctx.globalAlpha = transparency;

      if (renderMode === 'glow') {
        // Glow effect
        ctx.shadowColor = node.color;
        ctx.shadowBlur = 20;
      }

      // Draw node with gradient
      const gradient = ctx.createRadialGradient(x, y, 0, x, y, radius);
      gradient.addColorStop(0, node.color);
      gradient.addColorStop(1, node.color + '80');

      ctx.beginPath();
      ctx.arc(x, y, radius, 0, 2 * Math.PI);
      ctx.fillStyle = gradient;
      ctx.fill();

      // Enhanced border styling
      let borderColor = '#FFFFFF';
      let borderWidth = 1;

      if (selectedNode?.id === node.id) {
        borderColor = '#FFFF00';
        borderWidth = 4;
        ctx.shadowColor = '#FFFF00';
        ctx.shadowBlur = 15;
      } else if (hoveredNode?.id === node.id) {
        borderColor = '#FF6B6B';
        borderWidth = 2;
      }

      ctx.strokeStyle = borderColor;
      ctx.lineWidth = borderWidth;
      ctx.stroke();
      ctx.shadowBlur = 0;

      // Draw node labels if enabled
      if (showLabels) {
        ctx.fillStyle = '#FFFFFF';
        ctx.font = `${Math.max(10, 12 * labelSize * depthScale)}px Arial`;
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        ctx.shadowColor = '#000000';
        ctx.shadowBlur = 3;
        ctx.fillText(node.name.substring(0, 8), x, y + radius + 25);
        ctx.shadowBlur = 0;
      }

      ctx.globalAlpha = 1;
    });
  }, [nodes, edges, showMetabolites, showReactions, showGenes, showEdges, showLabels, showGrid,
      nodeSize, edgeThickness, labelSize, transparency, zoom, rotation, offset, selectedNode,
      hoveredNode, renderMode, filterByType, highlightConnections]);

  useEffect(() => {
    const animate = () => {
      renderFrame();
      requestAnimationFrame(animate);
    };
    animate();
  }, [nodes, edges, showMetabolites, showReactions, showGenes, nodeSize, edgeThickness, selectedNode]);

  const handleNodeClick = (event: React.MouseEvent<HTMLCanvasElement>) => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const rect = canvas.getBoundingClientRect();
    const clickX = event.clientX - rect.left;
    const clickY = event.clientY - rect.top;

    // Scale click coordinates to canvas coordinates
    const scaleX = width / rect.width;
    const scaleY = height / rect.height;
    const x = clickX * scaleX;
    const y = clickY * scaleY;

    const centerX = width / 2;
    const centerY = height / 2;
    const scale = Math.min(width, height) / 400;

    // Find clicked node
    const clickedNode = nodes.find(node => {
      const nodeX = centerX + (node.position.x * scale * 100);
      const nodeY = centerY + (node.position.y * scale * 100);
      const radius = Math.max(8, node.size * nodeSize * 20);
      const distance = Math.sqrt((x - nodeX) ** 2 + (y - nodeY) ** 2);
      return distance <= radius;
    });

    setSelectedNode(clickedNode || null);
  };

  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
  };

  const resetView = () => {
    setNodeSize(1.0);
    setEdgeThickness(1.0);
    setSelectedNode(null);
  };

  const exportImage = () => {
    if (!canvasRef.current) return;
    
    const link = document.createElement('a');
    link.download = `model_${modelId}_3d_view.png`;
    link.href = canvasRef.current.toDataURL();
    link.click();
  };

  return (
    <Card sx={{ width: '100%', height: '100%' }}>
      <CardContent>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Typography variant="h6">
            🧬 3D Model Viewer
          </Typography>
          <Box sx={{ display: 'flex', gap: 1 }}>
            <Tooltip title="Reset View">
              <IconButton onClick={resetView} size="small">
                <Refresh />
              </IconButton>
            </Tooltip>
            <Tooltip title="Export Image">
              <IconButton onClick={exportImage} size="small">
                <CameraAlt />
              </IconButton>
            </Tooltip>
            <Tooltip title={isFullscreen ? "Exit Fullscreen" : "Fullscreen"}>
              <IconButton onClick={toggleFullscreen} size="small">
                {isFullscreen ? <FullscreenExit /> : <Fullscreen />}
              </IconButton>
            </Tooltip>
          </Box>
        </Box>

        <Box sx={{ display: 'flex', gap: 2, height: isFullscreen ? '90vh' : '600px' }}>
          {/* 3D Canvas */}
          <Box sx={{ flex: 1, position: 'relative', border: '1px solid #ddd', borderRadius: 1 }}>
            <canvas
              ref={canvasRef}
              width={width}
              height={height}
              onClick={handleNodeClick}
              style={{
                width: '100%',
                height: '100%',
                cursor: 'pointer',
                background: 'linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%)',
              }}
            />
            {isLoading && (
              <Box
                sx={{
                  position: 'absolute',
                  top: '50%',
                  left: '50%',
                  transform: 'translate(-50%, -50%)',
                  color: 'white',
                }}
              >
                <Typography>Loading 3D Model...</Typography>
              </Box>
            )}
          </Box>

          {/* Controls Panel */}
          <Paper sx={{ width: 300, p: 2, display: 'flex', flexDirection: 'column', gap: 2 }}>
            <Typography variant="h6">Controls</Typography>
            
            {/* Visibility Controls */}
            <Box>
              <Typography variant="subtitle2" gutterBottom>Visibility</Typography>
              <FormControlLabel
                control={<Switch checked={showMetabolites} onChange={(e) => setShowMetabolites(e.target.checked)} />}
                label="Metabolites"
              />
              <FormControlLabel
                control={<Switch checked={showReactions} onChange={(e) => setShowReactions(e.target.checked)} />}
                label="Reactions"
              />
              <FormControlLabel
                control={<Switch checked={showGenes} onChange={(e) => setShowGenes(e.target.checked)} />}
                label="Genes"
              />
            </Box>

            {/* Size Controls */}
            <Box>
              <Typography variant="subtitle2" gutterBottom>Node Size</Typography>
              <Slider
                value={nodeSize}
                onChange={(_, value) => setNodeSize(value as number)}
                min={0.5}
                max={2.0}
                step={0.1}
                valueLabelDisplay="auto"
              />
            </Box>

            <Box>
              <Typography variant="subtitle2" gutterBottom>Edge Thickness</Typography>
              <Slider
                value={edgeThickness}
                onChange={(_, value) => setEdgeThickness(value as number)}
                min={0.5}
                max={3.0}
                step={0.1}
                valueLabelDisplay="auto"
              />
            </Box>

            {/* Selected Node Info */}
            {selectedNode && (
              <Paper variant="outlined" sx={{ p: 2 }}>
                <Typography variant="subtitle2" gutterBottom>Selected Node</Typography>
                <Typography variant="body2"><strong>Name:</strong> {selectedNode.name}</Typography>
                <Typography variant="body2"><strong>Type:</strong> {selectedNode.type}</Typography>
                <Typography variant="body2"><strong>Connections:</strong> {selectedNode.connections.length}</Typography>
                <Box sx={{ mt: 1 }}>
                  <Chip label={selectedNode.type} size="small" style={{ backgroundColor: selectedNode.color, color: 'white' }} />
                </Box>
              </Paper>
            )}

            {/* Statistics */}
            <Paper variant="outlined" sx={{ p: 2 }}>
              <Typography variant="subtitle2" gutterBottom>Network Statistics</Typography>
              <Typography variant="body2">Metabolites: {nodes.filter(n => n.type === 'metabolite').length}</Typography>
              <Typography variant="body2">Reactions: {nodes.filter(n => n.type === 'reaction').length}</Typography>
              <Typography variant="body2">Genes: {nodes.filter(n => n.type === 'gene').length}</Typography>
              <Typography variant="body2">Connections: {edges.length}</Typography>
            </Paper>
          </Paper>
        </Box>
      </CardContent>
    </Card>
  );
};

export default ModelViewer3D;
