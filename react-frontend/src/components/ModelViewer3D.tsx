import React, { useRef, useEffect, useState, useCallback } from 'react';
import * as THREE from 'three';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Button,
  IconButton,
  Tooltip,
  Slider,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Switch,
  FormControlLabel,
  Paper,
  Chip,
  Tabs,
  Tab,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Divider,
  ButtonGroup,
  SpeedDial,
  SpeedDialAction,
  SpeedDialIcon,
  Alert,
  LinearProgress,
  Badge,
  ToggleButton,
  ToggleButtonGroup,
  Fab,
  Grid
} from '@mui/material';
import {
  FullscreenExit,
  Fullscreen,
  Refresh,
  CameraAlt,
  Settings,
  Visibility,
  VisibilityOff,
  ZoomIn,
  ZoomOut,
  RotateLeft,
  RotateRight,
  ExpandMore,
  Palette,
  Timeline,
  ThreeDRotation,
  FilterList,
  Animation,
  Save,
  Share,
  Info,
  PlayArrow,
  Pause,
  Stop,
  Speed,
  Tune,
  ViewInAr,
  GridOn,
  <PERSON><PERSON><PERSON><PERSON>,
  CenterFocusStrong,
  AspectRatio,
  <PERSON>ers,
  ColorLens,
  AutoAwesome,
  FlashOn,
  Brightness4,
  Brightness7,
  CameraEnhance,
  Movie,
  PhotoCamera
} from '@mui/icons-material';

interface ModelViewer3DProps {
  modelId: string;
  modelData?: any;
  width?: number;
  height?: number;
}

interface Node3D {
  id: string;
  name: string;
  type: 'metabolite' | 'reaction' | 'gene';
  position: { x: number; y: number; z: number };
  connections: string[];
  size: number;
  color: string;
  mesh?: THREE.Mesh;
  label?: THREE.Sprite;
}

interface Edge3D {
  id: string;
  source: string;
  target: string;
  weight: number;
  type: 'substrate' | 'product' | 'regulation';
  line?: THREE.Line;
}

interface ThreeJSScene {
  scene: THREE.Scene;
  camera: THREE.PerspectiveCamera;
  renderer: THREE.WebGLRenderer;
  controls?: any;
  raycaster: THREE.Raycaster;
  mouse: THREE.Vector2;
}

const ModelViewer3D: React.FC<ModelViewer3DProps> = ({
  modelId,
  modelData,
  width = 800,
  height = 600,
}) => {
  const mountRef = useRef<HTMLDivElement>(null);
  const sceneRef = useRef<ThreeJSScene | null>(null);
  const animationRef = useRef<number>(0);

  // Core state
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [nodes, setNodes] = useState<Node3D[]>([]);
  const [edges, setEdges] = useState<Edge3D[]>([]);
  const [selectedNode, setSelectedNode] = useState<Node3D | null>(null);
  const [hoveredNode, setHoveredNode] = useState<Node3D | null>(null);

  // Advanced visualization controls
  const [showMetabolites, setShowMetabolites] = useState(true);
  const [showReactions, setShowReactions] = useState(true);
  const [showGenes, setShowGenes] = useState(true);
  const [showEdges, setShowEdges] = useState(true);
  const [showLabels, setShowLabels] = useState(true);
  const [showGrid, setShowGrid] = useState(true);
  const [showStats, setShowStats] = useState(true);

  // Three.js specific controls
  const [nodeSize, setNodeSize] = useState(1.0);
  const [edgeThickness, setEdgeThickness] = useState(1.0);
  const [labelSize, setLabelSize] = useState(1.0);
  const [transparency, setTransparency] = useState(1.0);
  const [ambientLight, setAmbientLight] = useState(0.4);
  const [directionalLight, setDirectionalLight] = useState(0.8);

  // Advanced rendering
  const [renderMode, setRenderMode] = useState('standard');
  const [materialType, setMaterialType] = useState('standard');
  const [enableShadows, setEnableShadows] = useState(true);
  const [enableFog, setEnableFog] = useState(false);
  const [enableBloom, setEnableBloom] = useState(false);

  // Animation and interaction
  const [isAnimating, setIsAnimating] = useState(false);
  const [animationType, setAnimationType] = useState('rotation');
  const [animationSpeed, setAnimationSpeed] = useState(1.0);
  const [autoRotate, setAutoRotate] = useState(false);
  const [highlightConnections, setHighlightConnections] = useState(true);

  // UI state
  const [activeTab, setActiveTab] = useState(0);
  const [speedDialOpen, setSpeedDialOpen] = useState(false);
  const [showAdvancedControls, setShowAdvancedControls] = useState(false);

  useEffect(() => {
    initializeThreeJS();
    generateMockData();
    return () => {
      cleanup();
    };
  }, [modelId]);

  const initializeThreeJS = () => {
    if (!mountRef.current) return;

    // Create scene
    const scene = new THREE.Scene();
    scene.background = new THREE.Color(0x0a0a0a);

    // Create camera
    const camera = new THREE.PerspectiveCamera(
      75,
      width / height,
      0.1,
      1000
    );
    camera.position.set(10, 10, 10);
    camera.lookAt(0, 0, 0);

    // Create renderer
    const renderer = new THREE.WebGLRenderer({
      antialias: true,
      alpha: true,
      powerPreference: "high-performance"
    });
    renderer.setSize(width, height);
    renderer.setPixelRatio(window.devicePixelRatio);
    renderer.shadowMap.enabled = enableShadows;
    renderer.shadowMap.type = THREE.PCFSoftShadowMap;
    renderer.outputColorSpace = THREE.SRGBColorSpace;
    renderer.toneMapping = THREE.ACESFilmicToneMapping;
    renderer.toneMappingExposure = 1.0;

    // Add renderer to DOM
    mountRef.current.appendChild(renderer.domElement);

    // Create raycaster for mouse interaction
    const raycaster = new THREE.Raycaster();
    const mouse = new THREE.Vector2();

    // Store scene reference
    sceneRef.current = {
      scene,
      camera,
      renderer,
      raycaster,
      mouse
    };

    // Add lights
    setupLighting(scene);

    // Add grid if enabled
    if (showGrid) {
      addGrid(scene);
    }

    // Add fog if enabled
    if (enableFog) {
      scene.fog = new THREE.Fog(0x0a0a0a, 10, 50);
    }

    // Add event listeners
    setupEventListeners();

    // Start animation loop
    animate();

    setIsLoading(false);
  };

  const setupLighting = (scene: THREE.Scene) => {
    // Ambient light
    const ambientLightObj = new THREE.AmbientLight(0x404040, ambientLight);
    scene.add(ambientLightObj);

    // Directional light
    const directionalLightObj = new THREE.DirectionalLight(0xffffff, directionalLight);
    directionalLightObj.position.set(10, 10, 5);
    directionalLightObj.castShadow = enableShadows;
    directionalLightObj.shadow.mapSize.width = 2048;
    directionalLightObj.shadow.mapSize.height = 2048;
    directionalLightObj.shadow.camera.near = 0.5;
    directionalLightObj.shadow.camera.far = 50;
    scene.add(directionalLightObj);

    // Point lights for dramatic effect
    const pointLight1 = new THREE.PointLight(0x4CAF50, 0.5, 30);
    pointLight1.position.set(5, 5, 5);
    scene.add(pointLight1);

    const pointLight2 = new THREE.PointLight(0xFF9800, 0.5, 30);
    pointLight2.position.set(-5, 5, -5);
    scene.add(pointLight2);
  };

  const addGrid = (scene: THREE.Scene) => {
    const gridHelper = new THREE.GridHelper(20, 20, 0x444444, 0x222222);
    scene.add(gridHelper);
  };

  const cleanup = () => {
    if (sceneRef.current) {
      const { renderer } = sceneRef.current;
      if (mountRef.current && renderer.domElement) {
        mountRef.current.removeChild(renderer.domElement);
      }
      renderer.dispose();
    }
    if (animationRef.current) {
      cancelAnimationFrame(animationRef.current);
    }
  };

  const setupEventListeners = () => {
    if (!sceneRef.current || !mountRef.current) return;

    const { renderer, raycaster, mouse } = sceneRef.current;
    const canvas = renderer.domElement;

    // Mouse move for hover effects
    const onMouseMove = (event: MouseEvent) => {
      const rect = canvas.getBoundingClientRect();
      mouse.x = ((event.clientX - rect.left) / rect.width) * 2 - 1;
      mouse.y = -((event.clientY - rect.top) / rect.height) * 2 + 1;
    };

    // Mouse click for selection
    const onMouseClick = (event: MouseEvent) => {
      const rect = canvas.getBoundingClientRect();
      mouse.x = ((event.clientX - rect.left) / rect.width) * 2 - 1;
      mouse.y = -((event.clientY - rect.top) / rect.height) * 2 + 1;

      // Perform raycasting to detect clicked objects
      if (sceneRef.current) {
        raycaster.setFromCamera(mouse, sceneRef.current.camera);
        const intersects = raycaster.intersectObjects(sceneRef.current.scene.children, true);

        if (intersects.length > 0) {
          const clickedObject = intersects[0].object;
          // Find the corresponding node
          const clickedNode = nodes.find(node => node.mesh === clickedObject);
          if (clickedNode) {
            setSelectedNode(clickedNode);
          }
        } else {
          setSelectedNode(null);
        }
      }
    };

    canvas.addEventListener('mousemove', onMouseMove);
    canvas.addEventListener('click', onMouseClick);

    // Store cleanup functions
    return () => {
      canvas.removeEventListener('mousemove', onMouseMove);
      canvas.removeEventListener('click', onMouseClick);
    };
  };

  const animate = () => {
    if (!sceneRef.current) return;

    const { scene, camera, renderer } = sceneRef.current;

    // Auto-rotation if enabled
    if (autoRotate && isAnimating) {
      scene.rotation.y += 0.01 * animationSpeed;
    }

    // Render the scene
    renderer.render(scene, camera);

    // Continue animation loop
    animationRef.current = requestAnimationFrame(animate);
  };

  const generateMockData = () => {
    // Generate mock 3D network data for demonstration
    const mockNodes: Node3D[] = [
      {
        id: 'glucose',
        name: 'Glucose',
        type: 'metabolite',
        position: { x: 0, y: 0, z: 0 },
        connections: ['glk', 'pgi'],
        size: 1.2,
        color: '#4CAF50',
      },
      {
        id: 'g6p',
        name: 'Glucose-6-phosphate',
        type: 'metabolite',
        position: { x: 2, y: 1, z: 0 },
        connections: ['glk', 'pgi', 'zwf'],
        size: 1.0,
        color: '#4CAF50',
      },
      {
        id: 'f6p',
        name: 'Fructose-6-phosphate',
        type: 'metabolite',
        position: { x: 4, y: 0, z: 1 },
        connections: ['pgi', 'pfk'],
        size: 1.0,
        color: '#4CAF50',
      },
      {
        id: 'glk',
        name: 'Glucokinase',
        type: 'reaction',
        position: { x: 1, y: 0.5, z: 0 },
        connections: ['glucose', 'g6p'],
        size: 0.8,
        color: '#FF9800',
      },
      {
        id: 'pgi',
        name: 'Phosphoglucose isomerase',
        type: 'reaction',
        position: { x: 3, y: 0.5, z: 0.5 },
        connections: ['g6p', 'f6p'],
        size: 0.8,
        color: '#FF9800',
      },
      {
        id: 'pfk',
        name: 'Phosphofructokinase',
        type: 'reaction',
        position: { x: 5, y: 0.5, z: 1 },
        connections: ['f6p'],
        size: 0.8,
        color: '#FF9800',
      },
      {
        id: 'glk_gene',
        name: 'GLK gene',
        type: 'gene',
        position: { x: 1, y: -1, z: 0 },
        connections: ['glk'],
        size: 0.6,
        color: '#9C27B0',
      },
    ];

    const mockEdges: Edge3D[] = [
      { id: 'e1', source: 'glucose', target: 'glk', weight: 1.0, type: 'substrate' },
      { id: 'e2', source: 'glk', target: 'g6p', weight: 1.0, type: 'product' },
      { id: 'e3', source: 'g6p', target: 'pgi', weight: 0.8, type: 'substrate' },
      { id: 'e4', source: 'pgi', target: 'f6p', weight: 0.8, type: 'product' },
      { id: 'e5', source: 'f6p', target: 'pfk', weight: 0.6, type: 'substrate' },
      { id: 'e6', source: 'glk_gene', target: 'glk', weight: 0.5, type: 'regulation' },
    ];

    setNodes(mockNodes);
    setEdges(mockEdges);
  };

  const createNode3D = (node: Node3D) => {
    if (!sceneRef.current) return;

    const { scene } = sceneRef.current;

    // Create geometry based on node type
    let geometry: THREE.BufferGeometry;
    switch (node.type) {
      case 'metabolite':
        geometry = new THREE.SphereGeometry(node.size * nodeSize, 16, 16);
        break;
      case 'reaction':
        geometry = new THREE.BoxGeometry(node.size * nodeSize * 2, node.size * nodeSize * 2, node.size * nodeSize * 2);
        break;
      case 'gene':
        geometry = new THREE.CylinderGeometry(node.size * nodeSize, node.size * nodeSize, node.size * nodeSize * 3, 8);
        break;
      default:
        geometry = new THREE.SphereGeometry(node.size * nodeSize, 16, 16);
    }

    // Create material based on material type
    let material: THREE.Material;
    const color = new THREE.Color(node.color);

    switch (materialType) {
      case 'standard':
        material = new THREE.MeshStandardMaterial({
          color,
          transparent: true,
          opacity: transparency,
          roughness: 0.4,
          metalness: 0.1
        });
        break;
      case 'phong':
        material = new THREE.MeshPhongMaterial({
          color,
          transparent: true,
          opacity: transparency,
          shininess: 100
        });
        break;
      default:
        material = new THREE.MeshStandardMaterial({ color });
    }

    // Create mesh
    const mesh = new THREE.Mesh(geometry, material);
    mesh.position.set(node.position.x * 10, node.position.y * 10, node.position.z * 10);
    mesh.castShadow = enableShadows;
    mesh.receiveShadow = enableShadows;

    // Store reference
    node.mesh = mesh;
    scene.add(mesh);

    // Create label if enabled
    if (showLabels) {
      createNodeLabel(node);
    }
  };

  const createNodeLabel = (node: Node3D) => {
    if (!sceneRef.current) return;

    const { scene } = sceneRef.current;

    // Create canvas for text
    const canvas = document.createElement('canvas');
    const context = canvas.getContext('2d');
    if (!context) return;

    canvas.width = 256;
    canvas.height = 64;
    context.font = `${24 * labelSize}px Arial`;
    context.fillStyle = '#ffffff';
    context.textAlign = 'center';
    context.fillText(node.name, 128, 32);

    // Create texture from canvas
    const texture = new THREE.CanvasTexture(canvas);
    const material = new THREE.SpriteMaterial({ map: texture, transparent: true });
    const sprite = new THREE.Sprite(material);

    sprite.position.set(
      node.position.x * 10,
      node.position.y * 10 + 3,
      node.position.z * 10
    );
    sprite.scale.set(4, 1, 1);

    node.label = sprite;
    scene.add(sprite);
  };

  const createEdge3D = (edge: Edge3D) => {
    if (!sceneRef.current) return;

    const { scene } = sceneRef.current;
    const sourceNode = nodes.find(n => n.id === edge.source);
    const targetNode = nodes.find(n => n.id === edge.target);

    if (!sourceNode || !targetNode) return;

    // Create line geometry
    const points = [
      new THREE.Vector3(sourceNode.position.x * 10, sourceNode.position.y * 10, sourceNode.position.z * 10),
      new THREE.Vector3(targetNode.position.x * 10, targetNode.position.y * 10, targetNode.position.z * 10)
    ];
    const geometry = new THREE.BufferGeometry().setFromPoints(points);

    // Create material based on edge type
    let color = '#4CAF50';
    if (edge.type === 'regulation') color = '#9C27B0';
    if (edge.type === 'substrate') color = '#2196F3';
    if (edge.type === 'product') color = '#FF9800';

    const material = new THREE.LineBasicMaterial({
      color,
      transparent: true,
      opacity: transparency * 0.8,
      linewidth: edge.weight * edgeThickness
    });

    const line = new THREE.Line(geometry, material);
    edge.line = line;
    scene.add(line);
  };

  const updateScene = useCallback(() => {
    if (!sceneRef.current) return;

    const { scene } = sceneRef.current;

    // Clear existing meshes
    const objectsToRemove: THREE.Object3D[] = [];
    scene.traverse((child) => {
      if (child instanceof THREE.Mesh || child instanceof THREE.Line || child instanceof THREE.Sprite) {
        objectsToRemove.push(child);
      }
    });
    objectsToRemove.forEach(obj => scene.remove(obj));

    // Add grid if enabled
    if (showGrid) {
      addGrid(scene);
    }

    // Create nodes
    nodes.forEach(node => {
      if (!showMetabolites && node.type === 'metabolite') return;
      if (!showReactions && node.type === 'reaction') return;
      if (!showGenes && node.type === 'gene') return;

      createNode3D(node);
    });

    // Create edges
    if (showEdges) {
      edges.forEach(edge => {
        createEdge3D(edge);
      });
    }
  }, [nodes, edges, showMetabolites, showReactions, showGenes, showEdges, showLabels, showGrid,
      nodeSize, edgeThickness, labelSize, transparency, materialType, enableShadows]);

  useEffect(() => {
    updateScene();
  }, [updateScene]);

  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
  };

  const resetView = () => {
    if (!sceneRef.current) return;

    const { camera } = sceneRef.current;
    camera.position.set(10, 10, 10);
    camera.lookAt(0, 0, 0);

    setNodeSize(1.0);
    setEdgeThickness(1.0);
    setSelectedNode(null);
    setTransparency(1.0);
  };

  const exportImage = () => {
    if (!sceneRef.current) return;

    const { renderer } = sceneRef.current;
    const link = document.createElement('a');
    link.download = `model_${modelId}_3d_view.png`;
    link.href = renderer.domElement.toDataURL();
    link.click();
  };

  const toggleAnimation = () => {
    setIsAnimating(!isAnimating);
  };

  return (
    <Card sx={{ width: '100%', height: '100%' }}>
      <CardContent>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Typography variant="h6">
            🧬 3D Model Viewer
          </Typography>
          <Box sx={{ display: 'flex', gap: 1 }}>
            <Tooltip title="Reset View">
              <IconButton onClick={resetView} size="small">
                <Refresh />
              </IconButton>
            </Tooltip>
            <Tooltip title="Export Image">
              <IconButton onClick={exportImage} size="small">
                <CameraAlt />
              </IconButton>
            </Tooltip>
            <Tooltip title={isFullscreen ? "Exit Fullscreen" : "Fullscreen"}>
              <IconButton onClick={toggleFullscreen} size="small">
                {isFullscreen ? <FullscreenExit /> : <Fullscreen />}
              </IconButton>
            </Tooltip>
          </Box>
        </Box>

        <Box sx={{ display: 'flex', gap: 2, height: isFullscreen ? '90vh' : '600px' }}>
          {/* Three.js 3D Viewer */}
          <Box sx={{ flex: 1, position: 'relative', border: '1px solid #333', borderRadius: 1, overflow: 'hidden' }}>
            <div
              ref={mountRef}
              style={{
                width: '100%',
                height: '100%',
                cursor: 'pointer',
                background: '#0a0a0a',
              }}
            />
            {isLoading && (
              <Box
                sx={{
                  position: 'absolute',
                  top: '50%',
                  left: '50%',
                  transform: 'translate(-50%, -50%)',
                  color: 'white',
                  zIndex: 10,
                  textAlign: 'center'
                }}
              >
                <Typography variant="h6">🚀 Loading State-of-the-Art 3D Model...</Typography>
                <Typography variant="body2" sx={{ mt: 1 }}>Powered by Three.js</Typography>
              </Box>
            )}
          </Box>

          {/* Advanced Controls Panel */}
          <Paper sx={{
            width: 350,
            p: 2,
            display: 'flex',
            flexDirection: 'column',
            gap: 2,
            backgroundColor: '#1a1a1a',
            color: '#ffffff',
            maxHeight: '100%',
            overflow: 'auto'
          }}>
            <Typography variant="h6" sx={{ color: '#ffffff' }}>🎛️ Advanced Controls</Typography>

            {/* Visibility Controls */}
            <Accordion sx={{ backgroundColor: '#2a2a2a' }}>
              <AccordionSummary expandIcon={<ExpandMore sx={{ color: '#ffffff' }} />}>
                <Typography sx={{ color: '#ffffff' }}>👁️ Visibility</Typography>
              </AccordionSummary>
              <AccordionDetails>
                <FormControlLabel
                  control={<Switch checked={showMetabolites} onChange={(e) => setShowMetabolites(e.target.checked)} />}
                  label="Metabolites"
                  sx={{ color: '#ffffff' }}
                />
                <FormControlLabel
                  control={<Switch checked={showReactions} onChange={(e) => setShowReactions(e.target.checked)} />}
                  label="Reactions"
                  sx={{ color: '#ffffff' }}
                />
                <FormControlLabel
                  control={<Switch checked={showGenes} onChange={(e) => setShowGenes(e.target.checked)} />}
                  label="Genes"
                  sx={{ color: '#ffffff' }}
                />
                <FormControlLabel
                  control={<Switch checked={showEdges} onChange={(e) => setShowEdges(e.target.checked)} />}
                  label="Edges"
                  sx={{ color: '#ffffff' }}
                />
                <FormControlLabel
                  control={<Switch checked={showLabels} onChange={(e) => setShowLabels(e.target.checked)} />}
                  label="Labels"
                  sx={{ color: '#ffffff' }}
                />
                <FormControlLabel
                  control={<Switch checked={showGrid} onChange={(e) => setShowGrid(e.target.checked)} />}
                  label="Grid"
                  sx={{ color: '#ffffff' }}
                />
              </AccordionDetails>
            </Accordion>

            {/* Rendering Controls */}
            <Accordion sx={{ backgroundColor: '#2a2a2a' }}>
              <AccordionSummary expandIcon={<ExpandMore sx={{ color: '#ffffff' }} />}>
                <Typography sx={{ color: '#ffffff' }}>🎨 Rendering</Typography>
              </AccordionSummary>
              <AccordionDetails>
                <Box sx={{ mb: 2 }}>
                  <Typography variant="subtitle2" gutterBottom sx={{ color: '#ffffff' }}>Node Size</Typography>
                  <Slider
                    value={nodeSize}
                    onChange={(_, value) => setNodeSize(value as number)}
                    min={0.5}
                    max={3.0}
                    step={0.1}
                    valueLabelDisplay="auto"
                    sx={{ color: '#4CAF50' }}
                  />
                </Box>

                <Box sx={{ mb: 2 }}>
                  <Typography variant="subtitle2" gutterBottom sx={{ color: '#ffffff' }}>Edge Thickness</Typography>
                  <Slider
                    value={edgeThickness}
                    onChange={(_, value) => setEdgeThickness(value as number)}
                    min={0.5}
                    max={5.0}
                    step={0.1}
                    valueLabelDisplay="auto"
                    sx={{ color: '#2196F3' }}
                  />
                </Box>

                <Box sx={{ mb: 2 }}>
                  <Typography variant="subtitle2" gutterBottom sx={{ color: '#ffffff' }}>Transparency</Typography>
                  <Slider
                    value={transparency}
                    onChange={(_, value) => setTransparency(value as number)}
                    min={0.1}
                    max={1.0}
                    step={0.1}
                    valueLabelDisplay="auto"
                    sx={{ color: '#FF9800' }}
                  />
                </Box>

                <Box sx={{ mb: 2 }}>
                  <Typography variant="subtitle2" gutterBottom sx={{ color: '#ffffff' }}>Material Type</Typography>
                  <FormControl fullWidth size="small">
                    <Select
                      value={materialType}
                      onChange={(e) => setMaterialType(e.target.value)}
                      sx={{ color: '#ffffff', backgroundColor: '#333' }}
                    >
                      <MenuItem value="standard">Standard</MenuItem>
                      <MenuItem value="phong">Phong</MenuItem>
                    </Select>
                  </FormControl>
                </Box>
              </AccordionDetails>
            </Accordion>

            {/* Selected Node Info */}
            {selectedNode && (
              <Paper variant="outlined" sx={{ p: 2 }}>
                <Typography variant="subtitle2" gutterBottom>Selected Node</Typography>
                <Typography variant="body2"><strong>Name:</strong> {selectedNode.name}</Typography>
                <Typography variant="body2"><strong>Type:</strong> {selectedNode.type}</Typography>
                <Typography variant="body2"><strong>Connections:</strong> {selectedNode.connections.length}</Typography>
                <Box sx={{ mt: 1 }}>
                  <Chip label={selectedNode.type} size="small" style={{ backgroundColor: selectedNode.color, color: 'white' }} />
                </Box>
              </Paper>
            )}

            {/* Statistics */}
            <Paper variant="outlined" sx={{ p: 2 }}>
              <Typography variant="subtitle2" gutterBottom>Network Statistics</Typography>
              <Typography variant="body2">Metabolites: {nodes.filter(n => n.type === 'metabolite').length}</Typography>
              <Typography variant="body2">Reactions: {nodes.filter(n => n.type === 'reaction').length}</Typography>
              <Typography variant="body2">Genes: {nodes.filter(n => n.type === 'gene').length}</Typography>
              <Typography variant="body2">Connections: {edges.length}</Typography>
            </Paper>
          </Paper>
        </Box>
      </CardContent>
    </Card>
  );
};

export default ModelViewer3D;
