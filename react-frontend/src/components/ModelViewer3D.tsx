import React, { useRef, useEffect, useState } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Button,
  IconButton,
  Tooltip,
  Slider,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Switch,
  FormControlLabel,
  Paper,
  Chip,
} from '@mui/material';
import {
  FullscreenExit,
  Fullscreen,
  Refresh,
  CameraAlt,
  Settings,
  Visibility,
  VisibilityOff,
  ZoomIn,
  ZoomOut,
  RotateLeft,
  RotateRight,
} from '@mui/icons-material';

interface ModelViewer3DProps {
  modelId: string;
  modelData?: any;
  width?: number;
  height?: number;
}

interface Node3D {
  id: string;
  name: string;
  type: 'metabolite' | 'reaction' | 'gene';
  position: { x: number; y: number; z: number };
  connections: string[];
  size: number;
  color: string;
}

interface Edge3D {
  id: string;
  source: string;
  target: string;
  weight: number;
  type: 'substrate' | 'product' | 'regulation';
}

const ModelViewer3D: React.FC<ModelViewer3DProps> = ({
  modelId,
  modelData,
  width = 800,
  height = 600,
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [nodes, setNodes] = useState<Node3D[]>([]);
  const [edges, setEdges] = useState<Edge3D[]>([]);
  const [selectedNode, setSelectedNode] = useState<Node3D | null>(null);
  
  // Visualization controls
  const [showMetabolites, setShowMetabolites] = useState(true);
  const [showReactions, setShowReactions] = useState(true);
  const [showGenes, setShowGenes] = useState(false);
  const [nodeSize, setNodeSize] = useState(1.0);
  const [edgeThickness, setEdgeThickness] = useState(1.0);
  const [layoutType, setLayoutType] = useState('force-directed');
  const [colorScheme, setColorScheme] = useState('type-based');

  useEffect(() => {
    initializeViewer();
    generateMockData();
  }, [modelId]);

  const initializeViewer = () => {
    if (!canvasRef.current) return;

    // Initialize WebGL context
    const canvas = canvasRef.current;
    const gl = canvas.getContext('webgl2');
    
    if (!gl) {
      console.error('WebGL 2 not supported');
      return;
    }

    // Set canvas size
    canvas.width = width;
    canvas.height = height;
    gl.viewport(0, 0, width, height);

    // Basic WebGL setup
    gl.clearColor(0.05, 0.05, 0.1, 1.0);
    gl.enable(gl.DEPTH_TEST);
    gl.enable(gl.BLEND);
    gl.blendFunc(gl.SRC_ALPHA, gl.ONE_MINUS_SRC_ALPHA);

    setIsLoading(false);
  };

  const generateMockData = () => {
    // Generate mock 3D network data for demonstration
    const mockNodes: Node3D[] = [
      {
        id: 'glucose',
        name: 'Glucose',
        type: 'metabolite',
        position: { x: 0, y: 0, z: 0 },
        connections: ['glk', 'pgi'],
        size: 1.2,
        color: '#4CAF50',
      },
      {
        id: 'g6p',
        name: 'Glucose-6-phosphate',
        type: 'metabolite',
        position: { x: 2, y: 1, z: 0 },
        connections: ['glk', 'pgi', 'zwf'],
        size: 1.0,
        color: '#4CAF50',
      },
      {
        id: 'f6p',
        name: 'Fructose-6-phosphate',
        type: 'metabolite',
        position: { x: 4, y: 0, z: 1 },
        connections: ['pgi', 'pfk'],
        size: 1.0,
        color: '#4CAF50',
      },
      {
        id: 'glk',
        name: 'Glucokinase',
        type: 'reaction',
        position: { x: 1, y: 0.5, z: 0 },
        connections: ['glucose', 'g6p'],
        size: 0.8,
        color: '#FF9800',
      },
      {
        id: 'pgi',
        name: 'Phosphoglucose isomerase',
        type: 'reaction',
        position: { x: 3, y: 0.5, z: 0.5 },
        connections: ['g6p', 'f6p'],
        size: 0.8,
        color: '#FF9800',
      },
      {
        id: 'pfk',
        name: 'Phosphofructokinase',
        type: 'reaction',
        position: { x: 5, y: 0.5, z: 1 },
        connections: ['f6p'],
        size: 0.8,
        color: '#FF9800',
      },
      {
        id: 'glk_gene',
        name: 'GLK gene',
        type: 'gene',
        position: { x: 1, y: -1, z: 0 },
        connections: ['glk'],
        size: 0.6,
        color: '#9C27B0',
      },
    ];

    const mockEdges: Edge3D[] = [
      { id: 'e1', source: 'glucose', target: 'glk', weight: 1.0, type: 'substrate' },
      { id: 'e2', source: 'glk', target: 'g6p', weight: 1.0, type: 'product' },
      { id: 'e3', source: 'g6p', target: 'pgi', weight: 0.8, type: 'substrate' },
      { id: 'e4', source: 'pgi', target: 'f6p', weight: 0.8, type: 'product' },
      { id: 'e5', source: 'f6p', target: 'pfk', weight: 0.6, type: 'substrate' },
      { id: 'e6', source: 'glk_gene', target: 'glk', weight: 0.5, type: 'regulation' },
    ];

    setNodes(mockNodes);
    setEdges(mockEdges);
  };

  const renderFrame = () => {
    if (!canvasRef.current) return;

    const canvas = canvasRef.current;
    const gl = canvas.getContext('webgl2');
    if (!gl) return;

    // Clear the canvas
    gl.clear(gl.COLOR_BUFFER_BIT | gl.DEPTH_BUFFER_BIT);

    // Simple 2D rendering for now (would be replaced with proper 3D rendering)
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    ctx.clearRect(0, 0, width, height);
    
    // Draw edges
    edges.forEach(edge => {
      const sourceNode = nodes.find(n => n.id === edge.source);
      const targetNode = nodes.find(n => n.id === edge.target);
      
      if (sourceNode && targetNode) {
        ctx.beginPath();
        ctx.moveTo(
          (sourceNode.position.x + 3) * 80 + 50,
          (sourceNode.position.y + 3) * 80 + 50
        );
        ctx.lineTo(
          (targetNode.position.x + 3) * 80 + 50,
          (targetNode.position.y + 3) * 80 + 50
        );
        ctx.strokeStyle = edge.type === 'regulation' ? '#9C27B0' : '#666';
        ctx.lineWidth = edge.weight * edgeThickness * 2;
        ctx.stroke();
      }
    });

    // Draw nodes
    nodes.forEach(node => {
      if (!showMetabolites && node.type === 'metabolite') return;
      if (!showReactions && node.type === 'reaction') return;
      if (!showGenes && node.type === 'gene') return;

      const x = (node.position.x + 3) * 80 + 50;
      const y = (node.position.y + 3) * 80 + 50;
      const radius = node.size * nodeSize * 15;

      ctx.beginPath();
      ctx.arc(x, y, radius, 0, 2 * Math.PI);
      ctx.fillStyle = node.color;
      ctx.fill();
      ctx.strokeStyle = selectedNode?.id === node.id ? '#FFF' : '#333';
      ctx.lineWidth = selectedNode?.id === node.id ? 3 : 1;
      ctx.stroke();

      // Draw node label
      ctx.fillStyle = '#FFF';
      ctx.font = '12px Arial';
      ctx.textAlign = 'center';
      ctx.fillText(node.name.substring(0, 8), x, y + radius + 15);
    });
  };

  useEffect(() => {
    const animate = () => {
      renderFrame();
      requestAnimationFrame(animate);
    };
    animate();
  }, [nodes, edges, showMetabolites, showReactions, showGenes, nodeSize, edgeThickness, selectedNode]);

  const handleNodeClick = (event: React.MouseEvent<HTMLCanvasElement>) => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const rect = canvas.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;

    // Find clicked node
    const clickedNode = nodes.find(node => {
      const nodeX = (node.position.x + 3) * 80 + 50;
      const nodeY = (node.position.y + 3) * 80 + 50;
      const radius = node.size * nodeSize * 15;
      const distance = Math.sqrt((x - nodeX) ** 2 + (y - nodeY) ** 2);
      return distance <= radius;
    });

    setSelectedNode(clickedNode || null);
  };

  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
  };

  const resetView = () => {
    setNodeSize(1.0);
    setEdgeThickness(1.0);
    setSelectedNode(null);
  };

  const exportImage = () => {
    if (!canvasRef.current) return;
    
    const link = document.createElement('a');
    link.download = `model_${modelId}_3d_view.png`;
    link.href = canvasRef.current.toDataURL();
    link.click();
  };

  return (
    <Card sx={{ width: '100%', height: '100%' }}>
      <CardContent>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Typography variant="h6">
            🧬 3D Model Viewer
          </Typography>
          <Box sx={{ display: 'flex', gap: 1 }}>
            <Tooltip title="Reset View">
              <IconButton onClick={resetView} size="small">
                <Refresh />
              </IconButton>
            </Tooltip>
            <Tooltip title="Export Image">
              <IconButton onClick={exportImage} size="small">
                <CameraAlt />
              </IconButton>
            </Tooltip>
            <Tooltip title={isFullscreen ? "Exit Fullscreen" : "Fullscreen"}>
              <IconButton onClick={toggleFullscreen} size="small">
                {isFullscreen ? <FullscreenExit /> : <Fullscreen />}
              </IconButton>
            </Tooltip>
          </Box>
        </Box>

        <Box sx={{ display: 'flex', gap: 2, height: isFullscreen ? '90vh' : '600px' }}>
          {/* 3D Canvas */}
          <Box sx={{ flex: 1, position: 'relative', border: '1px solid #ddd', borderRadius: 1 }}>
            <canvas
              ref={canvasRef}
              width={width}
              height={height}
              onClick={handleNodeClick}
              style={{
                width: '100%',
                height: '100%',
                cursor: 'pointer',
                background: 'linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%)',
              }}
            />
            {isLoading && (
              <Box
                sx={{
                  position: 'absolute',
                  top: '50%',
                  left: '50%',
                  transform: 'translate(-50%, -50%)',
                  color: 'white',
                }}
              >
                <Typography>Loading 3D Model...</Typography>
              </Box>
            )}
          </Box>

          {/* Controls Panel */}
          <Paper sx={{ width: 300, p: 2, display: 'flex', flexDirection: 'column', gap: 2 }}>
            <Typography variant="h6">Controls</Typography>
            
            {/* Visibility Controls */}
            <Box>
              <Typography variant="subtitle2" gutterBottom>Visibility</Typography>
              <FormControlLabel
                control={<Switch checked={showMetabolites} onChange={(e) => setShowMetabolites(e.target.checked)} />}
                label="Metabolites"
              />
              <FormControlLabel
                control={<Switch checked={showReactions} onChange={(e) => setShowReactions(e.target.checked)} />}
                label="Reactions"
              />
              <FormControlLabel
                control={<Switch checked={showGenes} onChange={(e) => setShowGenes(e.target.checked)} />}
                label="Genes"
              />
            </Box>

            {/* Size Controls */}
            <Box>
              <Typography variant="subtitle2" gutterBottom>Node Size</Typography>
              <Slider
                value={nodeSize}
                onChange={(_, value) => setNodeSize(value as number)}
                min={0.5}
                max={2.0}
                step={0.1}
                valueLabelDisplay="auto"
              />
            </Box>

            <Box>
              <Typography variant="subtitle2" gutterBottom>Edge Thickness</Typography>
              <Slider
                value={edgeThickness}
                onChange={(_, value) => setEdgeThickness(value as number)}
                min={0.5}
                max={3.0}
                step={0.1}
                valueLabelDisplay="auto"
              />
            </Box>

            {/* Selected Node Info */}
            {selectedNode && (
              <Paper variant="outlined" sx={{ p: 2 }}>
                <Typography variant="subtitle2" gutterBottom>Selected Node</Typography>
                <Typography variant="body2"><strong>Name:</strong> {selectedNode.name}</Typography>
                <Typography variant="body2"><strong>Type:</strong> {selectedNode.type}</Typography>
                <Typography variant="body2"><strong>Connections:</strong> {selectedNode.connections.length}</Typography>
                <Box sx={{ mt: 1 }}>
                  <Chip label={selectedNode.type} size="small" style={{ backgroundColor: selectedNode.color, color: 'white' }} />
                </Box>
              </Paper>
            )}

            {/* Statistics */}
            <Paper variant="outlined" sx={{ p: 2 }}>
              <Typography variant="subtitle2" gutterBottom>Network Statistics</Typography>
              <Typography variant="body2">Metabolites: {nodes.filter(n => n.type === 'metabolite').length}</Typography>
              <Typography variant="body2">Reactions: {nodes.filter(n => n.type === 'reaction').length}</Typography>
              <Typography variant="body2">Genes: {nodes.filter(n => n.type === 'gene').length}</Typography>
              <Typography variant="body2">Connections: {edges.length}</Typography>
            </Paper>
          </Paper>
        </Box>
      </CardContent>
    </Card>
  );
};

export default ModelViewer3D;
