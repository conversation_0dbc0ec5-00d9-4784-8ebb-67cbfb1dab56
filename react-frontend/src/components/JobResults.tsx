import React, { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  Typography,
  Box,
  Alert,
  CircularProgress,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Button,

  Divider,
} from '@mui/material';
import {
  CheckCircle as CheckCircleIcon,
  Error as <PERSON>rrorIcon,
  Refresh as RefreshIcon,
  Download as DownloadIcon,
  Download,
} from '@mui/icons-material';
import { useParams, useNavigate } from 'react-router-dom';
import { getJobStatus, getJobResults, Job, JobResult } from '../services/api';

const JobResults: React.FC = () => {
  const { jobId } = useParams<{ jobId: string }>();
  const navigate = useNavigate();
  
  const [job, setJob] = useState<Job | null>(null);
  const [results, setResults] = useState<JobResult | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [polling, setPolling] = useState(false);

  useEffect(() => {
    if (jobId) {
      fetchJobData();
    }
  }, [jobId]);

  useEffect(() => {
    let interval: NodeJS.Timeout;
    
    if (job && (job.status === 'pending' || job.status === 'running')) {
      setPolling(true);
      interval = setInterval(() => {
        fetchJobData();
      }, 2000); // Poll every 2 seconds
    } else {
      setPolling(false);
    }

    return () => {
      if (interval) {
        clearInterval(interval);
      }
    };
  }, [job?.status]);

  const fetchJobData = async () => {
    if (!jobId) return;

    try {
      setLoading(true);
      setError(null);
      
      // Get job status
      const statusResponse = await getJobStatus(jobId);
      if (statusResponse.success && statusResponse.data) {
        setJob(statusResponse.data);

        // If job is completed, get results
        if (statusResponse.data.status === 'completed') {
          // For FBA jobs, fetch results from the results endpoint
          if (statusResponse.data.type === 'fba') {
            const resultsResponse = await getJobResults(jobId);
            if (resultsResponse.success && resultsResponse.data) {
              setResults(resultsResponse.data);
            }
          }
          // For other job types (schuster, ema), results are in job.output
          // No need to fetch separately
        }
      } else {
        setError(statusResponse.error || 'Failed to load job data');
      }
    } catch (err) {
      setError('Failed to load job data');
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'success';
      case 'failed':
        return 'error';
      case 'running':
        return 'warning';
      case 'pending':
        return 'info';
      default:
        return 'default';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircleIcon />;
      case 'failed':
        return <ErrorIcon />;
      default:
        return null;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  const exportResults = () => {
    if (!results?.data) return;

    const dataStr = JSON.stringify(results.data, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `fba_results_${jobId}.json`;
    link.click();
    URL.revokeObjectURL(url);
  };

  const downloadSubmodel = (subnetwork: any, index: number, format: 'json' | 'sbml' = 'json') => {
    const subnetworkId = subnetwork.id || index;

    if (format === 'sbml') {
      // Create proper SBML XML format
      const sbmlContent = `<?xml version="1.0" encoding="UTF-8"?>
<sbml xmlns="http://www.sbml.org/sbml/level3/version1/core" level="3" version="1">
  <model id="subnetwork_${subnetworkId}" name="Subnetwork ${subnetworkId}">
    <notes>
      <body xmlns="http://www.w3.org/1999/xhtml">
        <p>Network decomposition submodel with ${subnetwork.size} nodes</p>
        <p>Original job ID: ${jobId}</p>
        <p>Connectivity threshold: ${job?.output?.connectivity_threshold}</p>
        <p>Extraction date: ${new Date().toISOString()}</p>
      </body>
    </notes>

    <listOfCompartments>
      <compartment id="cytoplasm" name="Cytoplasm" spatialDimensions="3" size="1" constant="true"/>
    </listOfCompartments>

    <listOfSpecies>
${subnetwork.nodes?.map((node: string) => `      <species id="${node}" name="${node}" compartment="cytoplasm" hasOnlySubstanceUnits="false" boundaryCondition="false" constant="false"/>`).join('\n') || ''}
    </listOfSpecies>

    <listOfReactions>
${subnetwork.connections?.map((conn: any, connIndex: number) => `      <reaction id="R_${connIndex}" name="${conn.from}_to_${conn.to}" reversible="true">
        <listOfReactants>
          <speciesReference species="${conn.from}" stoichiometry="1"/>
        </listOfReactants>
        <listOfProducts>
          <speciesReference species="${conn.to}" stoichiometry="1"/>
        </listOfProducts>
      </reaction>`).join('\n') || ''}
    </listOfReactions>
  </model>
</sbml>`;

      const dataBlob = new Blob([sbmlContent], { type: 'application/xml' });
      const url = URL.createObjectURL(dataBlob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `subnetwork_${subnetworkId}_${jobId}.xml`;
      link.click();
      URL.revokeObjectURL(url);
    } else {
      // Create JSON format with SBML-like structure
      const submodel = {
        id: `subnetwork_${subnetworkId}`,
        name: `Subnetwork ${subnetworkId}`,
        description: `Network decomposition submodel with ${subnetwork.size} nodes`,
        nodes: subnetwork.nodes,
        connections: subnetwork.connections,
        metadata: {
          original_job_id: jobId,
          connectivity_threshold: job?.output?.connectivity_threshold,
          extraction_date: new Date().toISOString(),
          node_count: subnetwork.size,
          connection_count: subnetwork.connections?.length || 0,
        }
      };

      const dataStr = JSON.stringify(submodel, null, 2);
      const dataBlob = new Blob([dataStr], { type: 'application/json' });
      const url = URL.createObjectURL(dataBlob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `subnetwork_${subnetworkId}_${jobId}.json`;
      link.click();
      URL.revokeObjectURL(url);
    }
  };

  if (loading && !job) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  if (!job) {
    return (
      <Alert severity="error">
        Job not found
      </Alert>
    );
  }

  return (
    <Box sx={{
      backgroundColor: '#0a0a0a',
      minHeight: '100vh',
      color: '#ffffff',
      p: 3
    }}>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4" sx={{ color: '#ffffff' }}>
          Job Results
        </Typography>
        <Button
          variant="outlined"
          onClick={() => navigate('/models')}
          sx={{ color: '#ffffff', borderColor: '#ffffff' }}
        >
          Back to Models
        </Button>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {/* Job Status Card */}
      <Card sx={{
        mb: 3,
        backgroundColor: '#1a1a1a',
        color: '#ffffff',
        border: '1px solid #333'
      }}>
        <CardContent>
          <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
            <Typography variant="h6" sx={{ color: '#ffffff' }}>Job Status</Typography>
            {polling && <CircularProgress size={20} sx={{ color: '#ffffff' }} />}
          </Box>

          <Box display="flex" flexWrap="wrap" gap={2}>
            <Box flex="1 1 200px" minWidth="200px">
              <Typography variant="body2" sx={{ color: '#cccccc' }}>Status:</Typography>
              <Chip
                icon={getStatusIcon(job.status || '') || undefined}
                label={(job.status || 'unknown').toUpperCase()}
                color={getStatusColor(job.status || '') as any}
                sx={{ mt: 0.5 }}
              />
            </Box>
            <Box flex="1 1 200px" minWidth="200px">
              <Typography variant="body2" sx={{ color: '#cccccc' }}>Job ID:</Typography>
              <Typography variant="body2" fontFamily="monospace" sx={{ color: '#ffffff' }}>
                {job.id}
              </Typography>
            </Box>
            <Box flex="1 1 200px" minWidth="200px">
              <Typography variant="body2" sx={{ color: '#cccccc' }}>Created:</Typography>
              <Typography variant="body2" sx={{ color: '#ffffff' }}>
                {formatDate(job.created_at)}
              </Typography>
            </Box>
            <Box flex="1 1 200px" minWidth="200px">
              <Typography variant="body2" sx={{ color: '#cccccc' }}>Updated:</Typography>
              <Typography variant="body2" sx={{ color: '#ffffff' }}>
                {formatDate(job.updated_at)}
              </Typography>
            </Box>
          </Box>

          {(job.status === 'pending' || job.status === 'running') && (
            <Box mt={2}>
              <Button
                variant="outlined"
                startIcon={<RefreshIcon />}
                onClick={fetchJobData}
                disabled={loading}
              >
                Refresh
              </Button>
            </Box>
          )}
        </CardContent>
      </Card>

      {/* FBA Results Card */}
      {job.type === 'fba' && job.status === 'completed' && results && (
        <Card sx={{
          backgroundColor: '#1a1a1a',
          color: '#ffffff',
          border: '1px solid #333'
        }}>
          <CardContent>
            <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
              <Typography variant="h6" sx={{ color: '#ffffff' }}>FBA Results</Typography>
              <Button
                variant="outlined"
                startIcon={<DownloadIcon />}
                onClick={exportResults}
                sx={{ color: '#ffffff', borderColor: '#ffffff' }}
              >
                Export Results
              </Button>
            </Box>

            {results.success && results.data ? (
              <Box>
                {/* Summary */}
                <Box display="flex" flexWrap="wrap" gap={3} sx={{ mb: 3 }}>
                  <Box flex="1 1 200px" minWidth="200px">
                    <Paper variant="outlined" sx={{
                      p: 2,
                      textAlign: 'center',
                      backgroundColor: '#2a2a2a',
                      borderColor: '#444'
                    }}>
                      <Typography variant="body2" sx={{ color: '#cccccc' }}>
                        Objective Value
                      </Typography>
                      <Typography variant="h5" sx={{ color: '#4CAF50' }}>
                        {results.data.objective_value.toFixed(6)}
                      </Typography>
                    </Paper>
                  </Box>
                  <Box flex="1 1 200px" minWidth="200px">
                    <Paper variant="outlined" sx={{
                      p: 2,
                      textAlign: 'center',
                      backgroundColor: '#2a2a2a',
                      borderColor: '#444'
                    }}>
                      <Typography variant="body2" sx={{ color: '#cccccc' }}>
                        Status
                      </Typography>
                      <Typography variant="h6" sx={{ color: '#ffffff' }}>
                        {results.data.status}
                      </Typography>
                    </Paper>
                  </Box>
                  <Box flex="1 1 200px" minWidth="200px">
                    <Paper variant="outlined" sx={{
                      p: 2,
                      textAlign: 'center',
                      backgroundColor: '#2a2a2a',
                      borderColor: '#444'
                    }}>
                      <Typography variant="body2" sx={{ color: '#cccccc' }}>
                        Solver Time
                      </Typography>
                      <Typography variant="h6" sx={{ color: '#ffffff' }}>
                        {results.data.solver_time.toFixed(3)}s
                      </Typography>
                    </Paper>
                  </Box>
                  <Box flex="1 1 200px" minWidth="200px">
                    <Paper variant="outlined" sx={{
                      p: 2,
                      textAlign: 'center',
                      backgroundColor: '#2a2a2a',
                      borderColor: '#444'
                    }}>
                      <Typography variant="body2" sx={{ color: '#cccccc' }}>
                        Flux Values
                      </Typography>
                      <Typography variant="h6" sx={{ color: '#ffffff' }}>
                        {Object.keys(results.data.flux_values).length}
                      </Typography>
                    </Paper>
                  </Box>
                </Box>

                <Divider sx={{ my: 3, borderColor: '#444' }} />

                {/* Flux Values Table */}
                <Typography variant="h6" gutterBottom sx={{ color: '#ffffff' }}>
                  Flux Values
                </Typography>

                <TableContainer component={Paper} variant="outlined" sx={{
                  backgroundColor: '#2a2a2a',
                  borderColor: '#444'
                }}>
                  <Table size="small">
                    <TableHead>
                      <TableRow>
                        <TableCell sx={{ color: '#ffffff', borderColor: '#444' }}>Reaction ID</TableCell>
                        <TableCell align="right" sx={{ color: '#ffffff', borderColor: '#444' }}>Flux Value</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {Object.entries(results.data.flux_values)
                        .sort(([, a], [, b]) => Math.abs(b) - Math.abs(a))
                        .map(([reaction, flux]) => (
                          <TableRow key={reaction}>
                            <TableCell component="th" scope="row" sx={{ borderColor: '#444' }}>
                              <Typography variant="body2" fontFamily="monospace" sx={{ color: '#ffffff' }}>
                                {reaction}
                              </Typography>
                            </TableCell>
                            <TableCell align="right" sx={{ borderColor: '#444' }}>
                              <Typography
                                variant="body2"
                                fontFamily="monospace"
                                sx={{ color: flux === 0 ? '#888888' : '#ffffff' }}
                              >
                                {flux.toFixed(6)}
                              </Typography>
                            </TableCell>
                          </TableRow>
                        ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              </Box>
            ) : (
              <Alert severity="error">
                Analysis failed: {results.error || 'Unknown error'}
              </Alert>
            )}
          </CardContent>
        </Card>
      )}

      {/* Schuster (Network Decomposition) Results */}
      {job.type === 'schuster' && job.status === 'completed' && job.output && (
        <Card sx={{
          mb: 3,
          backgroundColor: '#1a1a1a',
          color: '#ffffff',
          border: '1px solid #333'
        }}>
          <CardContent>
            <Typography variant="h5" gutterBottom sx={{ color: '#ffffff' }}>
              🧠 Network Decomposition Results
            </Typography>

            {(() => {
              const results = job.output as any;
              return results && results.subnetworks ? (
                <Box>
                  {/* Summary Statistics */}
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2, mb: 3 }}>
                    <Box flex="1 1 200px" minWidth="200px">
                      <Paper variant="outlined" sx={{
                        p: 2,
                        textAlign: 'center',
                        backgroundColor: '#2a2a2a',
                        borderColor: '#444'
                      }}>
                        <Typography variant="body2" sx={{ color: '#cccccc' }}>
                          Total Subnetworks
                        </Typography>
                        <Typography variant="h6" sx={{ color: '#ffffff' }}>
                          {results.total_subnetworks}
                        </Typography>
                      </Paper>
                    </Box>
                    <Box flex="1 1 200px" minWidth="200px">
                      <Paper variant="outlined" sx={{
                        p: 2,
                        textAlign: 'center',
                        backgroundColor: '#2a2a2a',
                        borderColor: '#444'
                      }}>
                        <Typography variant="body2" sx={{ color: '#cccccc' }}>
                          Connectivity Threshold
                        </Typography>
                        <Typography variant="h6" sx={{ color: '#ffffff' }}>
                          {results.connectivity_threshold}
                        </Typography>
                      </Paper>
                    </Box>
                    <Box flex="1 1 200px" minWidth="200px">
                      <Paper variant="outlined" sx={{
                        p: 2,
                        textAlign: 'center',
                        backgroundColor: '#2a2a2a',
                        borderColor: '#444'
                      }}>
                        <Typography variant="body2" sx={{ color: '#cccccc' }}>
                          Computation Time
                        </Typography>
                        <Typography variant="h6" sx={{ color: '#ffffff' }}>
                          {results.computation_time?.toFixed(3)}s
                        </Typography>
                      </Paper>
                    </Box>
                  </Box>

                  {/* Subnetworks List */}
                  <Typography variant="h6" gutterBottom sx={{ color: '#ffffff' }}>
                    Identified Subnetworks
                  </Typography>

                  <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                    {results.subnetworks.map((subnetwork: any, index: number) => (
                      <Paper key={subnetwork.id || index} variant="outlined" sx={{
                        p: 2,
                        backgroundColor: '#2a2a2a',
                        borderColor: '#444'
                      }}>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                          <Typography variant="h6" sx={{ color: '#ffffff' }}>
                            Subnetwork {subnetwork.id} ({subnetwork.size} nodes)
                          </Typography>
                          <Box sx={{ display: 'flex', gap: 1 }}>
                            <Button
                              variant="outlined"
                              size="small"
                              onClick={() => downloadSubmodel(subnetwork, index, 'json')}
                              startIcon={<Download />}
                              sx={{ color: '#ffffff', borderColor: '#ffffff' }}
                            >
                              JSON
                            </Button>
                            <Button
                              variant="outlined"
                              size="small"
                              onClick={() => downloadSubmodel(subnetwork, index, 'sbml')}
                              startIcon={<Download />}
                              sx={{ color: '#4CAF50', borderColor: '#4CAF50' }}
                            >
                              SBML
                            </Button>
                          </Box>
                        </Box>

                        <Typography variant="body2" sx={{ color: '#cccccc' }} gutterBottom>
                          Nodes:
                        </Typography>
                        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 2 }}>
                          {subnetwork.nodes.map((node: string) => (
                            <Chip
                              key={node}
                              label={node}
                              size="small"
                              variant="outlined"
                              sx={{
                                color: '#ffffff',
                                borderColor: '#666',
                                backgroundColor: '#333'
                              }}
                            />
                          ))}
                        </Box>

                        {subnetwork.connections && subnetwork.connections.length > 0 && (
                          <>
                            <Typography variant="body2" sx={{ color: '#cccccc' }} gutterBottom>
                              Connections ({subnetwork.connections.length}):
                            </Typography>
                            <TableContainer component={Paper} variant="outlined" sx={{
                              maxHeight: 200,
                              backgroundColor: '#333333',
                              borderColor: '#555'
                            }}>
                              <Table size="small">
                                <TableHead>
                                  <TableRow>
                                    <TableCell sx={{ color: '#ffffff', borderColor: '#555' }}>From</TableCell>
                                    <TableCell sx={{ color: '#ffffff', borderColor: '#555' }}>To</TableCell>
                                    <TableCell align="right" sx={{ color: '#ffffff', borderColor: '#555' }}>Weight</TableCell>
                                  </TableRow>
                                </TableHead>
                                <TableBody>
                                  {subnetwork.connections.map((conn: any, connIndex: number) => (
                                    <TableRow key={connIndex}>
                                      <TableCell sx={{ borderColor: '#555' }}>
                                        <Typography variant="body2" fontFamily="monospace" sx={{ color: '#ffffff' }}>
                                          {conn.from}
                                        </Typography>
                                      </TableCell>
                                      <TableCell sx={{ borderColor: '#555' }}>
                                        <Typography variant="body2" fontFamily="monospace" sx={{ color: '#ffffff' }}>
                                          {conn.to}
                                        </Typography>
                                      </TableCell>
                                      <TableCell align="right" sx={{ borderColor: '#555' }}>
                                        <Typography variant="body2" fontFamily="monospace" sx={{ color: '#ffffff' }}>
                                          {conn.weight?.toFixed(3)}
                                        </Typography>
                                      </TableCell>
                                    </TableRow>
                                  ))}
                                </TableBody>
                              </Table>
                            </TableContainer>
                          </>
                        )}
                      </Paper>
                    ))}
                  </Box>

                  {/* Statistics */}
                  {results.statistics && (
                    <Box sx={{ mt: 3 }}>
                      <Typography variant="h6" gutterBottom sx={{ color: '#ffffff' }}>
                        Analysis Statistics
                      </Typography>
                      <TableContainer component={Paper} variant="outlined" sx={{
                        backgroundColor: '#333333',
                        borderColor: '#555'
                      }}>
                        <Table size="small">
                          <TableBody>
                            {Object.entries(results.statistics).map(([key, value]) => (
                              <TableRow key={key}>
                                <TableCell component="th" scope="row" sx={{ borderColor: '#555' }}>
                                  <Typography variant="body2" sx={{ color: '#ffffff' }}>
                                    {key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                                  </Typography>
                                </TableCell>
                                <TableCell align="right" sx={{ borderColor: '#555' }}>
                                  <Typography variant="body2" fontFamily="monospace" sx={{ color: '#ffffff' }}>
                                    {typeof value === 'number' ? value.toLocaleString() : String(value)}
                                  </Typography>
                                </TableCell>
                              </TableRow>
                            ))}
                          </TableBody>
                        </Table>
                      </TableContainer>
                    </Box>
                  )}
                </Box>
              ) : (
                <Alert severity="error">
                  Analysis failed: {results.error || 'Unknown error'}
                </Alert>
              );
            })()}
          </CardContent>
        </Card>
      )}

      {/* EMA (Elementary Mode Analysis) Results */}
      {job.type === 'ema' && job.status === 'completed' && job.output && (
        <Card sx={{
          mb: 3,
          backgroundColor: '#1a1a1a',
          color: '#ffffff',
          border: '1px solid #333'
        }}>
          <CardContent>
            <Typography variant="h5" gutterBottom sx={{ color: '#ffffff' }}>
              🔬 Elementary Mode Analysis Results
            </Typography>

            {(() => {
              const results = job.output as any;
              return results && results.modes ? (
                <Box>
                  {/* Summary Statistics */}
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2, mb: 3 }}>
                    <Box flex="1 1 200px" minWidth="200px">
                      <Paper variant="outlined" sx={{
                        p: 2,
                        textAlign: 'center',
                        backgroundColor: '#2a2a2a',
                        borderColor: '#444'
                      }}>
                        <Typography variant="body2" sx={{ color: '#cccccc' }}>
                          Elementary Modes
                        </Typography>
                        <Typography variant="h6" sx={{ color: '#ffffff' }}>
                          {results.modes.length}
                        </Typography>
                      </Paper>
                    </Box>
                    <Box flex="1 1 200px" minWidth="200px">
                      <Paper variant="outlined" sx={{
                        p: 2,
                        textAlign: 'center',
                        backgroundColor: '#2a2a2a',
                        borderColor: '#444'
                      }}>
                        <Typography variant="body2" sx={{ color: '#cccccc' }}>
                          Computation Time
                        </Typography>
                        <Typography variant="h6" sx={{ color: '#ffffff' }}>
                          {results.computation_time?.toFixed(3)}s
                        </Typography>
                      </Paper>
                    </Box>
                    <Box flex="1 1 200px" minWidth="200px">
                      <Paper variant="outlined" sx={{
                        p: 2,
                        textAlign: 'center',
                        backgroundColor: '#2a2a2a',
                        borderColor: '#444'
                      }}>
                        <Typography variant="body2" sx={{ color: '#cccccc' }}>
                          Status
                        </Typography>
                        <Typography variant="h6" sx={{ color: '#ffffff' }}>
                          {results.status}
                        </Typography>
                      </Paper>
                    </Box>
                  </Box>

                  {/* Elementary Modes List */}
                  <Typography variant="h6" gutterBottom sx={{ color: '#ffffff' }}>
                    Elementary Modes
                  </Typography>

                  <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                    {results.modes.map((mode: any, index: number) => (
                      <Paper key={index} variant="outlined" sx={{
                        p: 2,
                        backgroundColor: '#2a2a2a',
                        borderColor: '#444'
                      }}>
                        <Typography variant="h6" sx={{ color: '#ffffff', mb: 2 }}>
                          Mode {index + 1}
                        </Typography>

                        <TableContainer component={Paper} variant="outlined" sx={{
                          maxHeight: 300,
                          backgroundColor: '#333333',
                          borderColor: '#555'
                        }}>
                          <Table size="small">
                            <TableHead>
                              <TableRow>
                                <TableCell sx={{ color: '#ffffff', borderColor: '#555' }}>Reaction</TableCell>
                                <TableCell align="right" sx={{ color: '#ffffff', borderColor: '#555' }}>Flux</TableCell>
                              </TableRow>
                            </TableHead>
                            <TableBody>
                              {Object.entries(mode.fluxes || {})
                                .filter(([, flux]) => Math.abs(flux as number) > 1e-6)
                                .sort(([, a], [, b]) => Math.abs(b as number) - Math.abs(a as number))
                                .map(([reaction, flux]) => (
                                  <TableRow key={reaction}>
                                    <TableCell sx={{ borderColor: '#555' }}>
                                      <Typography variant="body2" fontFamily="monospace" sx={{ color: '#ffffff' }}>
                                        {reaction}
                                      </Typography>
                                    </TableCell>
                                    <TableCell align="right" sx={{ borderColor: '#555' }}>
                                      <Typography variant="body2" fontFamily="monospace" sx={{ color: '#ffffff' }}>
                                        {(flux as number).toFixed(6)}
                                      </Typography>
                                    </TableCell>
                                  </TableRow>
                                ))}
                            </TableBody>
                          </Table>
                        </TableContainer>
                      </Paper>
                    ))}
                  </Box>
                </Box>
              ) : (
                <Alert severity="error">
                  Analysis failed: {results.error || 'Unknown error'}
                </Alert>
              );
            })()}
          </CardContent>
        </Card>
      )}

      {job.status === 'failed' && (
        <Alert severity="error" sx={{
          backgroundColor: '#4a1a1a',
          color: '#ffffff',
          border: '1px solid #ff6b6b',
          '& .MuiAlert-icon': { color: '#ff6b6b' }
        }}>
          <Typography variant="h6" gutterBottom sx={{ color: '#ffffff' }}>
            Job Failed
          </Typography>
          <Typography variant="body2" gutterBottom sx={{ color: '#ffffff' }}>
            {job.error || 'Job failed to complete. Please try running the analysis again.'}
          </Typography>
          {job.error && (
            <Box sx={{ mt: 2 }}>
              <Typography variant="body2" gutterBottom sx={{ color: '#cccccc' }}>
                Error Details:
              </Typography>
              <Typography
                variant="body2"
                fontFamily="monospace"
                sx={{
                  backgroundColor: '#2a2a2a',
                  p: 1,
                  borderRadius: 1,
                  whiteSpace: 'pre-wrap',
                  color: '#ffffff',
                  border: '1px solid #444'
                }}
              >
                {job.error}
              </Typography>
            </Box>
          )}
        </Alert>
      )}
    </Box>
  );
};

export default JobResults;
