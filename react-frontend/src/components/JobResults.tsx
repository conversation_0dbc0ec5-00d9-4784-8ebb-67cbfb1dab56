import React, { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  Typography,
  Box,
  Alert,
  CircularProgress,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Button,

  Divider,
} from '@mui/material';
import {
  CheckCircle as CheckCircleIcon,
  Error as <PERSON>rrorIcon,
  Refresh as RefreshIcon,
  Download as DownloadIcon,
  Download,
} from '@mui/icons-material';
import { useParams, useNavigate } from 'react-router-dom';
import { getJobStatus, getJobResults, Job, JobResult } from '../services/api';

const JobResults: React.FC = () => {
  const { jobId } = useParams<{ jobId: string }>();
  const navigate = useNavigate();
  
  const [job, setJob] = useState<Job | null>(null);
  const [results, setResults] = useState<JobResult | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [polling, setPolling] = useState(false);

  useEffect(() => {
    if (jobId) {
      fetchJobData();
    }
  }, [jobId]);

  useEffect(() => {
    let interval: NodeJS.Timeout;
    
    if (job && (job.status === 'pending' || job.status === 'running')) {
      setPolling(true);
      interval = setInterval(() => {
        fetchJobData();
      }, 2000); // Poll every 2 seconds
    } else {
      setPolling(false);
    }

    return () => {
      if (interval) {
        clearInterval(interval);
      }
    };
  }, [job?.status]);

  const fetchJobData = async () => {
    if (!jobId) return;

    try {
      setLoading(true);
      setError(null);
      
      // Get job status
      const statusResponse = await getJobStatus(jobId);
      if (statusResponse.success && statusResponse.data) {
        setJob(statusResponse.data);

        // If job is completed, get results
        if (statusResponse.data.status === 'completed') {
          // For FBA jobs, fetch results from the results endpoint
          if (statusResponse.data.type === 'fba') {
            const resultsResponse = await getJobResults(jobId);
            if (resultsResponse.success && resultsResponse.data) {
              setResults(resultsResponse.data);
            }
          }
          // For other job types (schuster, ema), results are in job.output
          // No need to fetch separately
        }
      } else {
        setError(statusResponse.error || 'Failed to load job data');
      }
    } catch (err) {
      setError('Failed to load job data');
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'success';
      case 'failed':
        return 'error';
      case 'running':
        return 'warning';
      case 'pending':
        return 'info';
      default:
        return 'default';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircleIcon />;
      case 'failed':
        return <ErrorIcon />;
      default:
        return null;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  const exportResults = () => {
    if (!results?.data) return;

    const dataStr = JSON.stringify(results.data, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `fba_results_${jobId}.json`;
    link.click();
    URL.revokeObjectURL(url);
  };

  const downloadSubmodel = (subnetwork: any, index: number) => {
    // Create a simplified SBML-like structure for the subnetwork
    const submodel = {
      id: `subnetwork_${subnetwork.id || index}`,
      name: `Subnetwork ${subnetwork.id || index}`,
      description: `Network decomposition submodel with ${subnetwork.size} nodes`,
      nodes: subnetwork.nodes,
      connections: subnetwork.connections,
      metadata: {
        original_job_id: jobId,
        connectivity_threshold: job?.output?.connectivity_threshold,
        extraction_date: new Date().toISOString(),
        node_count: subnetwork.size,
        connection_count: subnetwork.connections?.length || 0,
      },
      // Simple SBML-like structure
      sbml: {
        model: {
          id: `subnetwork_${subnetwork.id || index}`,
          name: `Subnetwork ${subnetwork.id || index}`,
          listOfSpecies: subnetwork.nodes.map((node: string) => ({
            id: node,
            name: node,
            compartment: 'cytoplasm',
            hasOnlySubstanceUnits: false,
            boundaryCondition: false,
            constant: false,
          })),
          listOfReactions: subnetwork.connections?.map((conn: any, connIndex: number) => ({
            id: `reaction_${connIndex}`,
            name: `${conn.from} -> ${conn.to}`,
            reversible: true,
            listOfReactants: [{ species: conn.from, stoichiometry: 1 }],
            listOfProducts: [{ species: conn.to, stoichiometry: 1 }],
            weight: conn.weight,
          })) || [],
        },
      },
    };

    // Create downloadable JSON file
    const dataStr = JSON.stringify(submodel, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `subnetwork_${subnetwork.id || index}_${jobId}.json`;
    link.click();
    URL.revokeObjectURL(url);
  };

  if (loading && !job) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  if (!job) {
    return (
      <Alert severity="error">
        Job not found
      </Alert>
    );
  }

  return (
    <Box>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4">
          Job Results
        </Typography>
        <Button
          variant="outlined"
          onClick={() => navigate('/models')}
        >
          Back to Models
        </Button>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {/* Job Status Card */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
            <Typography variant="h6">Job Status</Typography>
            {polling && <CircularProgress size={20} />}
          </Box>
          
          <Box display="flex" flexWrap="wrap" gap={2}>
            <Box flex="1 1 200px" minWidth="200px">
              <Typography variant="body2" color="text.secondary">Status:</Typography>
              <Chip
                icon={getStatusIcon(job.status || '') || undefined}
                label={(job.status || 'unknown').toUpperCase()}
                color={getStatusColor(job.status || '') as any}
                sx={{ mt: 0.5 }}
              />
            </Box>
            <Box flex="1 1 200px" minWidth="200px">
              <Typography variant="body2" color="text.secondary">Job ID:</Typography>
              <Typography variant="body2" fontFamily="monospace" sx={{ color: 'text.primary' }}>
                {job.id}
              </Typography>
            </Box>
            <Box flex="1 1 200px" minWidth="200px">
              <Typography variant="body2" color="text.secondary">Created:</Typography>
              <Typography variant="body2" sx={{ color: 'text.primary' }}>
                {formatDate(job.created_at)}
              </Typography>
            </Box>
            <Box flex="1 1 200px" minWidth="200px">
              <Typography variant="body2" color="text.secondary">Updated:</Typography>
              <Typography variant="body2" sx={{ color: 'text.primary' }}>
                {formatDate(job.updated_at)}
              </Typography>
            </Box>
          </Box>

          {(job.status === 'pending' || job.status === 'running') && (
            <Box mt={2}>
              <Button
                variant="outlined"
                startIcon={<RefreshIcon />}
                onClick={fetchJobData}
                disabled={loading}
              >
                Refresh
              </Button>
            </Box>
          )}
        </CardContent>
      </Card>

      {/* FBA Results Card */}
      {job.type === 'fba' && job.status === 'completed' && results && (
        <Card>
          <CardContent>
            <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
              <Typography variant="h6">FBA Results</Typography>
              <Button
                variant="outlined"
                startIcon={<DownloadIcon />}
                onClick={exportResults}
              >
                Export Results
              </Button>
            </Box>

            {results.success && results.data ? (
              <Box>
                {/* Summary */}
                <Box display="flex" flexWrap="wrap" gap={3} sx={{ mb: 3 }}>
                  <Box flex="1 1 200px" minWidth="200px">
                    <Paper variant="outlined" sx={{ p: 2, textAlign: 'center' }}>
                      <Typography variant="body2" color="text.secondary">
                        Objective Value
                      </Typography>
                      <Typography variant="h5" color="primary">
                        {results.data.objective_value.toFixed(6)}
                      </Typography>
                    </Paper>
                  </Box>
                  <Box flex="1 1 200px" minWidth="200px">
                    <Paper variant="outlined" sx={{ p: 2, textAlign: 'center' }}>
                      <Typography variant="body2" color="text.secondary">
                        Status
                      </Typography>
                      <Typography variant="h6" sx={{ color: 'text.primary' }}>
                        {results.data.status}
                      </Typography>
                    </Paper>
                  </Box>
                  <Box flex="1 1 200px" minWidth="200px">
                    <Paper variant="outlined" sx={{ p: 2, textAlign: 'center' }}>
                      <Typography variant="body2" color="text.secondary">
                        Solver Time
                      </Typography>
                      <Typography variant="h6" sx={{ color: 'text.primary' }}>
                        {results.data.solver_time.toFixed(3)}s
                      </Typography>
                    </Paper>
                  </Box>
                  <Box flex="1 1 200px" minWidth="200px">
                    <Paper variant="outlined" sx={{ p: 2, textAlign: 'center' }}>
                      <Typography variant="body2" color="text.secondary">
                        Flux Values
                      </Typography>
                      <Typography variant="h6" sx={{ color: 'text.primary' }}>
                        {Object.keys(results.data.flux_values).length}
                      </Typography>
                    </Paper>
                  </Box>
                </Box>

                <Divider sx={{ my: 3 }} />

                {/* Flux Values Table */}
                <Typography variant="h6" gutterBottom>
                  Flux Values
                </Typography>
                
                <TableContainer component={Paper} variant="outlined">
                  <Table size="small">
                    <TableHead>
                      <TableRow>
                        <TableCell>Reaction ID</TableCell>
                        <TableCell align="right">Flux Value</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {Object.entries(results.data.flux_values)
                        .sort(([, a], [, b]) => Math.abs(b) - Math.abs(a))
                        .map(([reaction, flux]) => (
                          <TableRow key={reaction}>
                            <TableCell component="th" scope="row">
                              <Typography variant="body2" fontFamily="monospace" sx={{ color: 'text.primary' }}>
                                {reaction}
                              </Typography>
                            </TableCell>
                            <TableCell align="right">
                              <Typography
                                variant="body2"
                                fontFamily="monospace"
                                sx={{ color: flux === 0 ? 'text.secondary' : 'text.primary' }}
                              >
                                {flux.toFixed(6)}
                              </Typography>
                            </TableCell>
                          </TableRow>
                        ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              </Box>
            ) : (
              <Alert severity="error">
                Analysis failed: {results.error || 'Unknown error'}
              </Alert>
            )}
          </CardContent>
        </Card>
      )}

      {/* Schuster (Network Decomposition) Results */}
      {job.type === 'schuster' && job.status === 'completed' && job.output && (
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Typography variant="h5" gutterBottom>
              🧠 Network Decomposition Results
            </Typography>

            {(() => {
              const results = job.output as any;
              return results && results.subnetworks ? (
                <Box>
                  {/* Summary Statistics */}
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2, mb: 3 }}>
                    <Box flex="1 1 200px" minWidth="200px">
                      <Paper variant="outlined" sx={{ p: 2, textAlign: 'center' }}>
                        <Typography variant="body2" color="text.secondary">
                          Total Subnetworks
                        </Typography>
                        <Typography variant="h6" sx={{ color: 'text.primary' }}>
                          {results.total_subnetworks}
                        </Typography>
                      </Paper>
                    </Box>
                    <Box flex="1 1 200px" minWidth="200px">
                      <Paper variant="outlined" sx={{ p: 2, textAlign: 'center' }}>
                        <Typography variant="body2" color="text.secondary">
                          Connectivity Threshold
                        </Typography>
                        <Typography variant="h6" sx={{ color: 'text.primary' }}>
                          {results.connectivity_threshold}
                        </Typography>
                      </Paper>
                    </Box>
                    <Box flex="1 1 200px" minWidth="200px">
                      <Paper variant="outlined" sx={{ p: 2, textAlign: 'center' }}>
                        <Typography variant="body2" color="text.secondary">
                          Computation Time
                        </Typography>
                        <Typography variant="h6" sx={{ color: 'text.primary' }}>
                          {results.computation_time?.toFixed(3)}s
                        </Typography>
                      </Paper>
                    </Box>
                  </Box>

                  {/* Subnetworks List */}
                  <Typography variant="h6" gutterBottom>
                    Identified Subnetworks
                  </Typography>

                  <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                    {results.subnetworks.map((subnetwork: any, index: number) => (
                      <Paper key={subnetwork.id || index} variant="outlined" sx={{ p: 2 }}>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                          <Typography variant="h6" sx={{ color: 'text.primary' }}>
                            Subnetwork {subnetwork.id} ({subnetwork.size} nodes)
                          </Typography>
                          <Button
                            variant="outlined"
                            size="small"
                            onClick={() => downloadSubmodel(subnetwork, index)}
                            startIcon={<Download />}
                          >
                            Download Submodel
                          </Button>
                        </Box>

                        <Typography variant="body2" color="text.secondary" gutterBottom>
                          Nodes:
                        </Typography>
                        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 2 }}>
                          {subnetwork.nodes.map((node: string) => (
                            <Chip
                              key={node}
                              label={node}
                              size="small"
                              variant="outlined"
                            />
                          ))}
                        </Box>

                        {subnetwork.connections && subnetwork.connections.length > 0 && (
                          <>
                            <Typography variant="body2" color="text.secondary" gutterBottom>
                              Connections ({subnetwork.connections.length}):
                            </Typography>
                            <TableContainer component={Paper} variant="outlined" sx={{ maxHeight: 200 }}>
                              <Table size="small">
                                <TableHead>
                                  <TableRow>
                                    <TableCell>From</TableCell>
                                    <TableCell>To</TableCell>
                                    <TableCell align="right">Weight</TableCell>
                                  </TableRow>
                                </TableHead>
                                <TableBody>
                                  {subnetwork.connections.map((conn: any, connIndex: number) => (
                                    <TableRow key={connIndex}>
                                      <TableCell>
                                        <Typography variant="body2" fontFamily="monospace" sx={{ color: 'text.primary' }}>
                                          {conn.from}
                                        </Typography>
                                      </TableCell>
                                      <TableCell>
                                        <Typography variant="body2" fontFamily="monospace" sx={{ color: 'text.primary' }}>
                                          {conn.to}
                                        </Typography>
                                      </TableCell>
                                      <TableCell align="right">
                                        <Typography variant="body2" fontFamily="monospace" sx={{ color: 'text.primary' }}>
                                          {conn.weight?.toFixed(3)}
                                        </Typography>
                                      </TableCell>
                                    </TableRow>
                                  ))}
                                </TableBody>
                              </Table>
                            </TableContainer>
                          </>
                        )}
                      </Paper>
                    ))}
                  </Box>

                  {/* Statistics */}
                  {results.statistics && (
                    <Box sx={{ mt: 3 }}>
                      <Typography variant="h6" gutterBottom>
                        Analysis Statistics
                      </Typography>
                      <TableContainer component={Paper} variant="outlined">
                        <Table size="small">
                          <TableBody>
                            {Object.entries(results.statistics).map(([key, value]) => (
                              <TableRow key={key}>
                                <TableCell component="th" scope="row">
                                  <Typography variant="body2" sx={{ color: 'text.primary' }}>
                                    {key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                                  </Typography>
                                </TableCell>
                                <TableCell align="right">
                                  <Typography variant="body2" fontFamily="monospace" sx={{ color: 'text.primary' }}>
                                    {typeof value === 'number' ? value.toLocaleString() : String(value)}
                                  </Typography>
                                </TableCell>
                              </TableRow>
                            ))}
                          </TableBody>
                        </Table>
                      </TableContainer>
                    </Box>
                  )}
                </Box>
              ) : (
                <Alert severity="error">
                  Analysis failed: {results.error || 'Unknown error'}
                </Alert>
              );
            })()}
          </CardContent>
        </Card>
      )}

      {/* EMA (Elementary Mode Analysis) Results */}
      {job.type === 'ema' && job.status === 'completed' && job.output && (
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Typography variant="h5" gutterBottom>
              🔬 Elementary Mode Analysis Results
            </Typography>

            {(() => {
              const results = job.output as any;
              return results && results.modes ? (
                <Box>
                  {/* Summary Statistics */}
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2, mb: 3 }}>
                    <Box flex="1 1 200px" minWidth="200px">
                      <Paper variant="outlined" sx={{ p: 2, textAlign: 'center' }}>
                        <Typography variant="body2" color="text.secondary">
                          Elementary Modes
                        </Typography>
                        <Typography variant="h6" sx={{ color: 'text.primary' }}>
                          {results.modes.length}
                        </Typography>
                      </Paper>
                    </Box>
                    <Box flex="1 1 200px" minWidth="200px">
                      <Paper variant="outlined" sx={{ p: 2, textAlign: 'center' }}>
                        <Typography variant="body2" color="text.secondary">
                          Computation Time
                        </Typography>
                        <Typography variant="h6" sx={{ color: 'text.primary' }}>
                          {results.computation_time?.toFixed(3)}s
                        </Typography>
                      </Paper>
                    </Box>
                    <Box flex="1 1 200px" minWidth="200px">
                      <Paper variant="outlined" sx={{ p: 2, textAlign: 'center' }}>
                        <Typography variant="body2" color="text.secondary">
                          Status
                        </Typography>
                        <Typography variant="h6" sx={{ color: 'text.primary' }}>
                          {results.status}
                        </Typography>
                      </Paper>
                    </Box>
                  </Box>

                  {/* Elementary Modes List */}
                  <Typography variant="h6" gutterBottom>
                    Elementary Modes
                  </Typography>

                  <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                    {results.modes.map((mode: any, index: number) => (
                      <Paper key={index} variant="outlined" sx={{ p: 2 }}>
                        <Typography variant="h6" sx={{ color: 'text.primary', mb: 2 }}>
                          Mode {index + 1}
                        </Typography>

                        <TableContainer component={Paper} variant="outlined" sx={{ maxHeight: 300 }}>
                          <Table size="small">
                            <TableHead>
                              <TableRow>
                                <TableCell>Reaction</TableCell>
                                <TableCell align="right">Flux</TableCell>
                              </TableRow>
                            </TableHead>
                            <TableBody>
                              {Object.entries(mode.fluxes || {})
                                .filter(([, flux]) => Math.abs(flux as number) > 1e-6)
                                .sort(([, a], [, b]) => Math.abs(b as number) - Math.abs(a as number))
                                .map(([reaction, flux]) => (
                                  <TableRow key={reaction}>
                                    <TableCell>
                                      <Typography variant="body2" fontFamily="monospace" sx={{ color: 'text.primary' }}>
                                        {reaction}
                                      </Typography>
                                    </TableCell>
                                    <TableCell align="right">
                                      <Typography variant="body2" fontFamily="monospace" sx={{ color: 'text.primary' }}>
                                        {(flux as number).toFixed(6)}
                                      </Typography>
                                    </TableCell>
                                  </TableRow>
                                ))}
                            </TableBody>
                          </Table>
                        </TableContainer>
                      </Paper>
                    ))}
                  </Box>
                </Box>
              ) : (
                <Alert severity="error">
                  Analysis failed: {results.error || 'Unknown error'}
                </Alert>
              );
            })()}
          </CardContent>
        </Card>
      )}

      {job.status === 'failed' && (
        <Alert severity="error">
          <Typography variant="h6" gutterBottom>
            Job Failed
          </Typography>
          <Typography variant="body2" gutterBottom>
            {job.error || 'Job failed to complete. Please try running the analysis again.'}
          </Typography>
          {job.error && (
            <Box sx={{ mt: 2 }}>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                Error Details:
              </Typography>
              <Typography
                variant="body2"
                fontFamily="monospace"
                sx={{
                  backgroundColor: 'rgba(0,0,0,0.1)',
                  p: 1,
                  borderRadius: 1,
                  whiteSpace: 'pre-wrap',
                  color: 'text.primary'
                }}
              >
                {job.error}
              </Typography>
            </Box>
          )}
        </Alert>
      )}
    </Box>
  );
};

export default JobResults;
