import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  CardContent,
  <PERSON><PERSON><PERSON>,
  Button,
  Box,
  Alert,
  CircularProgress,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
} from '@mui/material';
import {
  Analytics as AnalyticsIcon,
  Delete as DeleteIcon,
  Add as AddIcon,
  ThreeDRotation as ThreeDIcon,
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { getModels, deleteModel, Model } from '../services/api';
import ModelViewer3D from './ModelViewer3D';

const ModelList: React.FC = () => {
  const navigate = useNavigate();
  const [models, setModels] = useState<Model[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [modelToDelete, setModelToDelete] = useState<Model | null>(null);
  const [deleting, setDeleting] = useState(false);
  const [viewerDialogOpen, setViewerDialogOpen] = useState(false);
  const [modelToView, setModelToView] = useState<Model | null>(null);

  useEffect(() => {
    fetchModels();
  }, []);

  const fetchModels = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await getModels();
      if (response.success && response.data) {
        setModels(response.data);
      } else {
        setError(response.error || 'Failed to load models');
      }
    } catch (err) {
      setError('Failed to load models');
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteClick = (model: Model) => {
    setModelToDelete(model);
    setDeleteDialogOpen(true);
  };

  const handleDeleteConfirm = async () => {
    if (!modelToDelete) return;

    try {
      setDeleting(true);
      const response = await deleteModel(modelToDelete.id);
      
      if (response.success) {
        setModels(models.filter(m => m.id !== modelToDelete.id));
        setDeleteDialogOpen(false);
        setModelToDelete(null);
      } else {
        setError(response.error || 'Failed to delete model');
      }
    } catch (err) {
      setError('Failed to delete model');
    } finally {
      setDeleting(false);
    }
  };

  const handleDeleteCancel = () => {
    setDeleteDialogOpen(false);
    setModelToDelete(null);
  };

  const handleView3D = (model: Model) => {
    setModelToView(model);
    setViewerDialogOpen(true);
  };

  const handleViewerClose = () => {
    setViewerDialogOpen(false);
    setModelToView(null);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4">
          Metabolic Models
        </Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => navigate('/upload')}
        >
          Upload Model
        </Button>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {models.length === 0 ? (
        <Card>
          <CardContent>
            <Box textAlign="center" py={4}>
              <Typography variant="h6" gutterBottom>
                No models found
              </Typography>
              <Typography variant="body2" color="textSecondary" sx={{ mb: 3 }}>
                Upload your first metabolic model to get started with analysis.
              </Typography>
              <Button
                variant="contained"
                startIcon={<AddIcon />}
                onClick={() => navigate('/upload')}
              >
                Upload Model
              </Button>
            </Box>
          </CardContent>
        </Card>
      ) : (
        <TableContainer component={Paper}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Name</TableCell>
                <TableCell>Description</TableCell>
                <TableCell>Format</TableCell>
                <TableCell>Created</TableCell>
                <TableCell align="right">Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {models.map((model) => (
                <TableRow key={model.id} hover>
                  <TableCell>
                    <Typography variant="subtitle1" fontWeight="medium">
                      {model.name}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2" color="textSecondary">
                      {model.description || 'No description'}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Chip
                      label={model.format.toUpperCase()}
                      size="small"
                      variant="outlined"
                    />
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2">
                      {formatDate(model.created_at)}
                    </Typography>
                  </TableCell>
                  <TableCell align="right">
                    <Box display="flex" gap={1} justifyContent="flex-end">
                      <Button
                        variant="outlined"
                        size="small"
                        startIcon={<ThreeDIcon />}
                        onClick={() => handleView3D(model)}
                      >
                        3D View
                      </Button>
                      <Button
                        variant="contained"
                        size="small"
                        startIcon={<AnalyticsIcon />}
                        onClick={() => navigate(`/models/${model.id}/fba`)}
                      >
                        Analyze
                      </Button>
                      <IconButton
                        color="error"
                        onClick={() => handleDeleteClick(model)}
                        size="small"
                      >
                        <DeleteIcon />
                      </IconButton>
                    </Box>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      )}

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialogOpen} onClose={handleDeleteCancel}>
        <DialogTitle>Delete Model</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to delete the model "{modelToDelete?.name}"?
            This action cannot be undone.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleDeleteCancel}>Cancel</Button>
          <Button
            onClick={handleDeleteConfirm}
            color="error"
            disabled={deleting}
            startIcon={deleting ? <CircularProgress size={16} /> : undefined}
          >
            {deleting ? 'Deleting...' : 'Delete'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* 3D Model Viewer Dialog */}
      <Dialog
        open={viewerDialogOpen}
        onClose={handleViewerClose}
        maxWidth="xl"
        fullWidth
        PaperProps={{
          sx: { height: '90vh' }
        }}
      >
        <DialogTitle>
          3D Model Viewer - {modelToView?.name}
        </DialogTitle>
        <DialogContent sx={{ p: 0, height: '100%' }}>
          {modelToView && (
            <ModelViewer3D
              modelId={modelToView.id}
              modelData={modelToView}
            />
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleViewerClose}>Close</Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default ModelList;
