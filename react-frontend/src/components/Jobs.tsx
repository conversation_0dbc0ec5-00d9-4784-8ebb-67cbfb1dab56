import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  Grid,
  Card,
  CardContent,
  CardActions,
  Button,
  Chip,
  Alert,
  CircularProgress,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Tabs,
  Tab,
  IconButton,
  Tooltip,
  LinearProgress,
} from '@mui/material';
import {
  Work,
  Timeline,
  AccountTree,
  CheckCircle,
  Error,
  Schedule,
  Visibility,
  Refresh,
  GetApp,
  Analytics,
  Psychology,
  Science,
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import api from '../services/api';

interface Job {
  id: string;
  model_id: string;
  type: 'fba' | 'ema' | 'schuster';
  status: 'pending' | 'running' | 'completed' | 'failed';
  input: any;
  output?: any;
  created_at: string;
  updated_at: string;
  started_at?: string;
  ended_at?: string;
}

interface JobStats {
  total: number;
  completed: number;
  running: number;
  failed: number;
  pending: number;
}

const Jobs: React.FC = () => {
  const navigate = useNavigate();
  const [jobs, setJobs] = useState<Job[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedJob, setSelectedJob] = useState<Job | null>(null);
  const [detailsOpen, setDetailsOpen] = useState(false);
  const [tabValue, setTabValue] = useState(0);
  const [stats, setStats] = useState<JobStats>({
    total: 0,
    completed: 0,
    running: 0,
    failed: 0,
    pending: 0,
  });

  useEffect(() => {
    fetchJobs();
    const interval = setInterval(fetchJobs, 5000); // Refresh every 5 seconds
    return () => clearInterval(interval);
  }, []);

  const fetchJobs = async () => {
    try {
      setLoading(true);
      const response = await api.get('/jobs');
      const responseData = response.data as any;
      if (responseData.success) {
        const jobList = responseData.data?.jobs || [];
        setJobs(jobList);
        calculateStats(jobList);
      } else {
        setError(responseData.error || 'Failed to fetch jobs');
      }
    } catch (err: any) {
      setError(err.response?.data?.error || err.message || 'Failed to fetch jobs');
    } finally {
      setLoading(false);
    }
  };

  const calculateStats = (jobList: Job[]) => {
    const stats = {
      total: jobList.length,
      completed: jobList.filter(job => job.status === 'completed').length,
      running: jobList.filter(job => job.status === 'running').length,
      failed: jobList.filter(job => job.status === 'failed').length,
      pending: jobList.filter(job => job.status === 'pending').length,
    };
    setStats(stats);
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle color="success" />;
      case 'failed':
        return <Error color="error" />;
      case 'running':
        return <CircularProgress size={20} />;
      case 'pending':
        return <Schedule color="warning" />;
      default:
        return <Schedule />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'success';
      case 'failed':
        return 'error';
      case 'running':
        return 'info';
      case 'pending':
        return 'warning';
      default:
        return 'default';
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'fba':
        return <Timeline />;
      case 'ema':
        return <AccountTree />;
      case 'schuster':
        return <Psychology />;
      default:
        return <Science />;
    }
  };

  const handleViewDetails = (job: Job) => {
    setSelectedJob(job);
    setDetailsOpen(true);
  };

  const formatDuration = (startTime?: string, endTime?: string) => {
    if (!startTime || !endTime) return 'N/A';
    const start = new Date(startTime);
    const end = new Date(endTime);
    const duration = end.getTime() - start.getTime();
    return `${(duration / 1000).toFixed(2)}s`;
  };

  const filteredJobs = tabValue === 0 ? jobs : jobs.filter(job => {
    if (tabValue === 1) return job.type === 'fba';
    if (tabValue === 2) return job.type === 'ema';
    if (tabValue === 3) return job.type === 'schuster';
    return true;
  });

  const statsCards = [
    { title: 'Total Jobs', value: stats.total, color: 'primary', icon: <Work /> },
    { title: 'Completed', value: stats.completed, color: 'success', icon: <CheckCircle /> },
    { title: 'Running', value: stats.running, color: 'info', icon: <CircularProgress size={20} /> },
    { title: 'Failed', value: stats.failed, color: 'error', icon: <Error /> },
  ];

  if (loading && jobs.length === 0) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4" component="h1" sx={{ fontWeight: 'bold' }}>
          🔬 Job Dashboard
        </Typography>
        <Tooltip title="Refresh Jobs">
          <IconButton onClick={fetchJobs} disabled={loading}>
            <Refresh />
          </IconButton>
        </Tooltip>
      </Box>

      <Typography variant="body1" color="text.secondary" sx={{ mb: 4 }}>
        Monitor all analysis jobs and view their results. Jobs are automatically refreshed every 5 seconds.
      </Typography>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {/* Statistics Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        {statsCards.map((stat, index) => (
          <Grid size={{ xs: 12, sm: 6, md: 3 }} key={index}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center" justifyContent="space-between">
                  <Box>
                    <Typography variant="h4" component="div" color={`${stat.color}.main`}>
                      {stat.value}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {stat.title}
                    </Typography>
                  </Box>
                  <Box color={`${stat.color}.main`}>
                    {stat.icon}
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>

      {/* Job Filters */}
      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
        <Tabs value={tabValue} onChange={(_, newValue) => setTabValue(newValue)}>
          <Tab label={`All Jobs (${jobs.length})`} />
          <Tab label={`FBA Jobs (${jobs.filter(j => j.type === 'fba').length})`} />
          <Tab label={`EMA Jobs (${jobs.filter(j => j.type === 'ema').length})`} />
          <Tab label={`Schuster Jobs (${jobs.filter(j => j.type === 'schuster').length})`} />
        </Tabs>
      </Box>

      {/* Jobs Table */}
      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>Type</TableCell>
              <TableCell>Status</TableCell>
              <TableCell>Model ID</TableCell>
              <TableCell>Created</TableCell>
              <TableCell>Duration</TableCell>
              <TableCell>Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {filteredJobs.map((job) => (
              <TableRow key={job.id} hover>
                <TableCell>
                  <Box display="flex" alignItems="center">
                    {getTypeIcon(job.type)}
                    <Typography variant="body2" sx={{ ml: 1, textTransform: 'uppercase' }}>
                      {job.type}
                    </Typography>
                  </Box>
                </TableCell>
                <TableCell>
                  <Chip
                    icon={getStatusIcon(job.status)}
                    label={job.status}
                    color={getStatusColor(job.status) as any}
                    size="small"
                  />
                </TableCell>
                <TableCell>
                  <Typography variant="body2" sx={{ fontFamily: 'monospace' }}>
                    {job.model_id.substring(0, 8)}...
                  </Typography>
                </TableCell>
                <TableCell>
                  <Typography variant="body2">
                    {new Date(job.created_at).toLocaleString()}
                  </Typography>
                </TableCell>
                <TableCell>
                  <Typography variant="body2">
                    {formatDuration(job.started_at, job.ended_at)}
                  </Typography>
                </TableCell>
                <TableCell>
                  <Button
                    size="small"
                    startIcon={<Visibility />}
                    onClick={() => handleViewDetails(job)}
                  >
                    View
                  </Button>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      {filteredJobs.length === 0 && !loading && (
        <Box textAlign="center" py={4}>
          <Typography variant="h6" color="text.secondary">
            No jobs found
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
            Start an analysis to see jobs here.
          </Typography>
          <Button
            variant="contained"
            sx={{ mt: 2 }}
            onClick={() => navigate('/analysis')}
          >
            Start Analysis
          </Button>
        </Box>
      )}

      {/* Job Details Dialog */}
      <Dialog
        open={detailsOpen}
        onClose={() => setDetailsOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          <Box display="flex" alignItems="center">
            <Analytics sx={{ mr: 1 }} />
            Job Details - {selectedJob?.type.toUpperCase()}
          </Box>
        </DialogTitle>
        <DialogContent>
          {selectedJob && (
            <Box>
              <Grid container spacing={2} sx={{ mb: 3 }}>
                <Grid size={{ xs: 12, sm: 6 }}>
                  <Typography variant="subtitle2" color="text.secondary">
                    Job ID
                  </Typography>
                  <Typography variant="body2" sx={{ fontFamily: 'monospace' }}>
                    {selectedJob.id}
                  </Typography>
                </Grid>
                <Grid size={{ xs: 12, sm: 6 }}>
                  <Typography variant="subtitle2" color="text.secondary">
                    Model ID
                  </Typography>
                  <Typography variant="body2" sx={{ fontFamily: 'monospace' }}>
                    {selectedJob.model_id}
                  </Typography>
                </Grid>
                <Grid size={{ xs: 12, sm: 6 }}>
                  <Typography variant="subtitle2" color="text.secondary">
                    Status
                  </Typography>
                  <Chip
                    icon={getStatusIcon(selectedJob.status)}
                    label={selectedJob.status}
                    color={getStatusColor(selectedJob.status) as any}
                    size="small"
                  />
                </Grid>
                <Grid size={{ xs: 12, sm: 6 }}>
                  <Typography variant="subtitle2" color="text.secondary">
                    Duration
                  </Typography>
                  <Typography variant="body2">
                    {formatDuration(selectedJob.started_at, selectedJob.ended_at)}
                  </Typography>
                </Grid>
              </Grid>

              {/* Input Parameters */}
              <Typography variant="h6" sx={{ mb: 2 }}>
                Input Parameters
              </Typography>
              <Paper sx={{ p: 2, mb: 3, bgcolor: 'grey.50' }}>
                <pre style={{ margin: 0, fontSize: '0.875rem' }}>
                  {JSON.stringify(selectedJob.input, null, 2)}
                </pre>
              </Paper>

              {/* Output Results */}
              {selectedJob.output && (
                <>
                  <Typography variant="h6" sx={{ mb: 2 }}>
                    Results
                  </Typography>
                  <Paper sx={{ p: 2, bgcolor: 'grey.50' }}>
                    <pre style={{ margin: 0, fontSize: '0.875rem' }}>
                      {JSON.stringify(selectedJob.output, null, 2)}
                    </pre>
                  </Paper>
                </>
              )}
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDetailsOpen(false)}>Close</Button>
          {selectedJob?.output && (
            <Button
              startIcon={<GetApp />}
              onClick={() => {
                const dataStr = JSON.stringify(selectedJob.output, null, 2);
                const dataBlob = new Blob([dataStr], { type: 'application/json' });
                const url = URL.createObjectURL(dataBlob);
                const link = document.createElement('a');
                link.href = url;
                link.download = `job-${selectedJob.id}-results.json`;
                link.click();
                URL.revokeObjectURL(url);
              }}
            >
              Download Results
            </Button>
          )}
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default Jobs;
