import axios from 'axios';

const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8080';

const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000,
});

export interface Model {
  id: string;
  name: string;
  description: string;
  format: string;
  created_at: string;
  updated_at: string;
}

export interface Job {
  id: string;
  model_id: string;
  type?: string;
  status: 'pending' | 'running' | 'completed' | 'failed';
  created_at: string;
  updated_at: string;
  parameters?: any;
  input?: any;
  output?: any;
  error?: string;
}

export interface FBAResult {
  objective_value: number;
  flux_values: { [key: string]: number };
  status: string;
  solver_time: number;
}

export interface JobResult {
  success: boolean;
  data?: FBAResult;
  error?: string;
}

export interface SystemStats {
  models_count: number;
  jobs_count: number;
  completed_jobs: number;
  failed_jobs: number;
  uptime: string;
}

export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
}

// Health check
export const checkHealth = async (): Promise<ApiResponse<{ status: string }>> => {
  const response = await api.get<ApiResponse<{ status: string }>>('/health');
  return response.data;
};

// Models API
export const getModels = async (): Promise<ApiResponse<Model[]>> => {
  const response = await api.get<ApiResponse<{ models: Model[], total: number }>>('/api/v1/models');
  // Extract models from nested structure
  if (response.data.success && response.data.data) {
    return {
      success: true,
      data: response.data.data.models,
      error: undefined
    };
  }
  return {
    success: false,
    data: undefined,
    error: response.data.error || 'Failed to fetch models'
  };
};

export const getModel = async (id: string): Promise<ApiResponse<Model>> => {
  const response = await api.get<ApiResponse<Model>>(`/models/${id}`);
  return response.data;
};

export const uploadModel = async (
  file: File,
  name: string,
  description: string
): Promise<ApiResponse<Model>> => {
  const formData = new FormData();
  formData.append('file', file);
  formData.append('name', name);
  formData.append('description', description);

  const response = await api.post<ApiResponse<Model>>('/models', formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
  return response.data;
};

export const deleteModel = async (id: string): Promise<ApiResponse<null>> => {
  const response = await api.delete<ApiResponse<null>>(`/models/${id}`);
  return response.data;
};

// FBA Analysis API
export const submitFBA = async (
  modelId: string,
  objective: string,
  constraints: { [key: string]: number } = {}
): Promise<ApiResponse<Job>> => {
  const response = await api.post<ApiResponse<Job>>(`/models/${modelId}/fba`, {
    objective,
    constraints,
  });
  return response.data;
};

// Jobs API
export const getJobStatus = async (jobId: string): Promise<ApiResponse<Job>> => {
  const response = await api.get<ApiResponse<{job: Job, progress: number}>>(`/api/v1/jobs/${jobId}/status`);
  if (response.data.success && response.data.data) {
    return {
      success: true,
      data: response.data.data.job,
      error: undefined
    };
  }
  return {
    success: false,
    data: undefined,
    error: response.data.error || 'Failed to fetch job status'
  };
};

export const getJobResults = async (jobId: string): Promise<ApiResponse<JobResult>> => {
  const response = await api.get<ApiResponse<FBAResult>>(`/api/v1/jobs/${jobId}/results`);
  if (response.data.success && response.data.data) {
    return {
      success: true,
      data: {
        success: true,
        data: response.data.data,
        error: undefined
      },
      error: undefined
    };
  }
  return {
    success: false,
    data: {
      success: false,
      data: undefined,
      error: response.data.error || 'Failed to fetch job results'
    },
    error: response.data.error || 'Failed to fetch job results'
  };
};

export const getJobs = async (): Promise<ApiResponse<Job[]>> => {
  const response = await api.get<ApiResponse<{ jobs: Job[], total: number }>>('/api/v1/jobs');
  // Extract jobs from nested structure
  if (response.data.success && response.data.data) {
    return {
      success: true,
      data: response.data.data.jobs,
      error: undefined
    };
  }
  return {
    success: false,
    data: undefined,
    error: response.data.error || 'Failed to fetch jobs'
  };
};

// System Stats API
export const getSystemStats = async (): Promise<ApiResponse<SystemStats>> => {
  const response = await api.get<ApiResponse<SystemStats>>('/stats');
  return response.data;
};

// Authentication API
export const authAPI = {
  login: async (credentials: { username: string; password: string }) => {
    return await api.post('/auth/login', credentials);
  },

  register: async (data: {
    username: string;
    email: string;
    password: string;
    first_name: string;
    last_name: string;
    role?: string;
  }) => {
    return await api.post('/auth/register', data);
  },

  logout: async (token: string) => {
    return await api.post('/auth/logout', {}, {
      headers: { Authorization: `Bearer ${token}` }
    });
  },

  refreshToken: async (token: string) => {
    return await api.post('/auth/refresh', {}, {
      headers: { Authorization: `Bearer ${token}` }
    });
  },

  getProfile: async (token: string) => {
    return await api.get('/api/v1/users/profile', {
      headers: { Authorization: `Bearer ${token}` }
    });
  },

  updateProfile: async (token: string, data: any) => {
    return await api.put('/api/v1/users/profile', data, {
      headers: { Authorization: `Bearer ${token}` }
    });
  },

  changePassword: async (token: string, data: {
    current_password: string;
    new_password: string;
  }) => {
    return await api.post('/api/v1/users/change-password', data, {
      headers: { Authorization: `Bearer ${token}` }
    });
  },
};

// Add request interceptor to include auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('auth_token');
    if (token && config.headers) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Add response interceptor to handle auth errors
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Token expired or invalid
      localStorage.removeItem('auth_token');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

export default api;
