import axios from 'axios';

const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8080';

const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000,
});

export interface Model {
  id: string;
  name: string;
  description: string;
  format: string;
  created_at: string;
  updated_at: string;
}

export interface Job {
  id: string;
  model_id: string;
  status: 'pending' | 'running' | 'completed' | 'failed';
  created_at: string;
  updated_at: string;
  parameters?: any;
}

export interface FBAResult {
  objective_value: number;
  flux_values: { [key: string]: number };
  status: string;
  solver_time: number;
}

export interface JobResult {
  success: boolean;
  data?: FBAResult;
  error?: string;
}

export interface SystemStats {
  models_count: number;
  jobs_count: number;
  completed_jobs: number;
  failed_jobs: number;
  uptime: string;
}

export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
}

// Health check
export const checkHealth = async (): Promise<ApiResponse<{ status: string }>> => {
  const response = await api.get<ApiResponse<{ status: string }>>('/health');
  return response.data;
};

// Models API
export const getModels = async (): Promise<ApiResponse<Model[]>> => {
  const response = await api.get<ApiResponse<Model[]>>('/models');
  return response.data;
};

export const getModel = async (id: string): Promise<ApiResponse<Model>> => {
  const response = await api.get<ApiResponse<Model>>(`/models/${id}`);
  return response.data;
};

export const uploadModel = async (
  file: File,
  name: string,
  description: string
): Promise<ApiResponse<Model>> => {
  const formData = new FormData();
  formData.append('file', file);
  formData.append('name', name);
  formData.append('description', description);

  const response = await api.post<ApiResponse<Model>>('/models', formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
  return response.data;
};

export const deleteModel = async (id: string): Promise<ApiResponse<null>> => {
  const response = await api.delete<ApiResponse<null>>(`/models/${id}`);
  return response.data;
};

// FBA Analysis API
export const submitFBA = async (
  modelId: string,
  objective: string,
  constraints: { [key: string]: number } = {}
): Promise<ApiResponse<Job>> => {
  const response = await api.post<ApiResponse<Job>>(`/models/${modelId}/fba`, {
    objective,
    constraints,
  });
  return response.data;
};

// Jobs API
export const getJobStatus = async (jobId: string): Promise<ApiResponse<Job>> => {
  const response = await api.get<ApiResponse<Job>>(`/jobs/${jobId}/status`);
  return response.data;
};

export const getJobResults = async (jobId: string): Promise<ApiResponse<JobResult>> => {
  const response = await api.get<ApiResponse<JobResult>>(`/jobs/${jobId}/results`);
  return response.data;
};

export const getJobs = async (): Promise<ApiResponse<Job[]>> => {
  const response = await api.get<ApiResponse<Job[]>>('/jobs');
  return response.data;
};

// System Stats API
export const getSystemStats = async (): Promise<ApiResponse<SystemStats>> => {
  const response = await api.get<ApiResponse<SystemStats>>('/stats');
  return response.data;
};

export default api;
