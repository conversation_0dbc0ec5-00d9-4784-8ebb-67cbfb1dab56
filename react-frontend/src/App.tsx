import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { ThemeProvider } from '@mui/material/styles';
import CssBaseline from '@mui/material/CssBaseline';
import { Container, Box } from '@mui/material';
import { SnackbarProvider } from 'notistack';
import { AuthProvider } from './contexts/AuthContext';
import ProtectedRoute from './components/auth/ProtectedRoute';
import Navbar from './components/layout/Navbar';
import Dashboard from './components/Dashboard';
import ModelUpload from './components/ModelUpload';
import ModelList from './components/ModelList';
import Analysis from './components/Analysis';
import Jobs from './components/Jobs';
import FBAAnalysis from './components/FBAAnalysis';
import EMAAnalysis from './components/EMAAnalysis';
import JobResults from './components/JobResults';
import theme from './theme';
import './App.css';

function App() {
  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <SnackbarProvider
        maxSnack={3}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'right',
        }}
      >
        <AuthProvider>
          <Router>
            <Box sx={{ flexGrow: 1, minHeight: '100vh' }}>
              <ProtectedRoute>
                <Navbar />
                <Container maxWidth="xl" sx={{ mt: 4, mb: 4, px: { xs: 2, sm: 3 } }}>
                  <Routes>
                    <Route path="/" element={<Dashboard />} />
                    <Route
                      path="/upload"
                      element={
                        <ProtectedRoute requiredRole="student">
                          <ModelUpload />
                        </ProtectedRoute>
                      }
                    />
                    <Route path="/models" element={<ModelList />} />
                    <Route path="/analysis" element={<Analysis />} />
                    <Route path="/jobs" element={<Jobs />} />
                    <Route
                      path="/models/:modelId/fba"
                      element={
                        <ProtectedRoute requiredRole="student">
                          <FBAAnalysis />
                        </ProtectedRoute>
                      }
                    />
                    <Route
                      path="/models/:modelId/ema"
                      element={
                        <ProtectedRoute requiredRole="student">
                          <EMAAnalysis />
                        </ProtectedRoute>
                      }
                    />
                    <Route
                      path="/jobs/:jobId/results"
                      element={
                        <ProtectedRoute requiredRole="student">
                          <JobResults />
                        </ProtectedRoute>
                      }
                    />
                  </Routes>
                </Container>
              </ProtectedRoute>
            </Box>
          </Router>
        </AuthProvider>
      </SnackbarProvider>
    </ThemeProvider>
  );
}

export default App;
