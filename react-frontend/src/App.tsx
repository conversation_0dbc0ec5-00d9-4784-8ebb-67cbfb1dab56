import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import CssBaseline from '@mui/material/CssBaseline';
import { Container, Box } from '@mui/material';
import { SnackbarProvider } from 'notistack';
import { AuthProvider } from './contexts/AuthContext';
import ProtectedRoute from './components/auth/ProtectedRoute';
import Navbar from './components/layout/Navbar';
import Dashboard from './components/Dashboard';
import ModelUpload from './components/ModelUpload';
import ModelList from './components/ModelList';
import FBAAnalysis from './components/FBAAnalysis';
import EMAAnalysis from './components/EMAAnalysis';
import JobResults from './components/JobResults';
import './App.css';

const theme = createTheme({
  palette: {
    mode: 'light',
    primary: {
      main: '#667eea',
      light: '#8fa4f3',
      dark: '#4c63d2',
    },
    secondary: {
      main: '#764ba2',
      light: '#9575cd',
      dark: '#512da8',
    },
    background: {
      default: '#f8fafc',
      paper: '#ffffff',
    },
    text: {
      primary: '#1a202c',
      secondary: '#4a5568',
    },
  },
  typography: {
    fontFamily: '"Inter", "Roboto", "Helvetica", "Arial", sans-serif',
    h1: {
      fontWeight: 700,
    },
    h2: {
      fontWeight: 600,
    },
    h3: {
      fontWeight: 600,
    },
    h4: {
      fontWeight: 600,
    },
    h5: {
      fontWeight: 500,
    },
    h6: {
      fontWeight: 500,
    },
  },
  shape: {
    borderRadius: 12,
  },
  components: {
    MuiCard: {
      styleOverrides: {
        root: {
          boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
          borderRadius: 12,
        },
      },
    },
    MuiButton: {
      styleOverrides: {
        root: {
          textTransform: 'none',
          fontWeight: 500,
          borderRadius: 8,
        },
      },
    },
  },
});

function App() {
  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <SnackbarProvider
        maxSnack={3}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'right',
        }}
      >
        <AuthProvider>
          <Router>
            <Box sx={{ flexGrow: 1, minHeight: '100vh' }}>
              <ProtectedRoute>
                <Navbar />
                <Container maxWidth="xl" sx={{ mt: 4, mb: 4, px: { xs: 2, sm: 3 } }}>
                  <Routes>
                    <Route path="/" element={<Dashboard />} />
                    <Route
                      path="/upload"
                      element={
                        <ProtectedRoute requiredRole="student">
                          <ModelUpload />
                        </ProtectedRoute>
                      }
                    />
                    <Route path="/models" element={<ModelList />} />
                    <Route
                      path="/models/:modelId/fba"
                      element={
                        <ProtectedRoute requiredRole="student">
                          <FBAAnalysis />
                        </ProtectedRoute>
                      }
                    />
                    <Route
                      path="/models/:modelId/ema"
                      element={
                        <ProtectedRoute requiredRole="student">
                          <EMAAnalysis />
                        </ProtectedRoute>
                      }
                    />
                    <Route
                      path="/jobs/:jobId/results"
                      element={
                        <ProtectedRoute requiredRole="student">
                          <JobResults />
                        </ProtectedRoute>
                      }
                    />
                  </Routes>
                </Container>
              </ProtectedRoute>
            </Box>
          </Router>
        </AuthProvider>
      </SnackbarProvider>
    </ThemeProvider>
  );
}

export default App;
