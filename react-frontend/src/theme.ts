import { createTheme } from '@mui/material/styles';

const theme = createTheme({
  palette: {
    mode: 'dark',
    primary: {
      main: '#00e5ff', // Cyan - DNA/molecular color
      light: '#62efff',
      dark: '#00b2cc',
      contrastText: '#000000',
    },
    secondary: {
      main: '#ff6b35', // Orange - enzyme/protein color
      light: '#ff9d64',
      dark: '#c73e00',
      contrastText: '#ffffff',
    },
    tertiary: {
      main: '#7c4dff', // Purple - analysis color
      light: '#b47cff',
      dark: '#3f1dcb',
      contrastText: '#ffffff',
    },
    background: {
      default: '#0a0e1a', // Deep space blue
      paper: '#1a1f2e', // Darker blue-gray
    },
    surface: {
      main: '#242938', // Medium surface
      light: '#2d3142',
      dark: '#1a1f2e',
    },
    text: {
      primary: '#ffffff',
      secondary: '#b0bec5',
    },
    success: {
      main: '#00e676', // Bright green for completed
      light: '#66ffa6',
      dark: '#00b248',
    },
    warning: {
      main: '#ffab00', // Amber for pending
      light: '#ffdd4b',
      dark: '#c67c00',
    },
    error: {
      main: '#ff1744', // Red for failed
      light: '#ff6b74',
      dark: '#c4001d',
    },
    info: {
      main: '#00b0ff', // Blue for running
      light: '#62c5ff',
      dark: '#0081cb',
    },
  },
  typography: {
    fontFamily: '"Inter", "Roboto Mono", "SF Pro Display", -apple-system, BlinkMacSystemFont, sans-serif',
    h1: {
      fontSize: '2.5rem',
      fontWeight: 700,
      letterSpacing: '-0.02em',
      background: 'linear-gradient(135deg, #00e5ff 0%, #7c4dff 100%)',
      WebkitBackgroundClip: 'text',
      WebkitTextFillColor: 'transparent',
    },
    h2: {
      fontSize: '2rem',
      fontWeight: 600,
      letterSpacing: '-0.01em',
    },
    h3: {
      fontSize: '1.75rem',
      fontWeight: 600,
    },
    h4: {
      fontSize: '1.5rem',
      fontWeight: 600,
      background: 'linear-gradient(135deg, #00e5ff 0%, #ff6b35 100%)',
      WebkitBackgroundClip: 'text',
      WebkitTextFillColor: 'transparent',
    },
    h5: {
      fontSize: '1.25rem',
      fontWeight: 500,
    },
    h6: {
      fontSize: '1.125rem',
      fontWeight: 500,
    },
    body1: {
      fontSize: '1rem',
      lineHeight: 1.6,
    },
    body2: {
      fontSize: '0.875rem',
      lineHeight: 1.5,
    },
    caption: {
      fontFamily: '"Roboto Mono", monospace',
      fontSize: '0.75rem',
      letterSpacing: '0.05em',
    },
    button: {
      fontWeight: 600,
      letterSpacing: '0.02em',
      textTransform: 'none',
    },
  },
  shape: {
    borderRadius: 12,
  },
  components: {
    MuiCssBaseline: {
      styleOverrides: {
        body: {
          background: 'linear-gradient(135deg, #0a0e1a 0%, #1a1f2e 50%, #242938 100%)',
          backgroundAttachment: 'fixed',
          '&::-webkit-scrollbar': {
            width: '8px',
          },
          '&::-webkit-scrollbar-track': {
            background: '#1a1f2e',
          },
          '&::-webkit-scrollbar-thumb': {
            background: '#00e5ff',
            borderRadius: '4px',
          },
          '&::-webkit-scrollbar-thumb:hover': {
            background: '#62efff',
          },
        },
      },
    },
    MuiCard: {
      styleOverrides: {
        root: {
          background: 'linear-gradient(135deg, rgba(26, 31, 46, 0.9) 0%, rgba(36, 41, 56, 0.9) 100%)',
          backdropFilter: 'blur(20px)',
          border: '1px solid rgba(0, 229, 255, 0.1)',
          boxShadow: '0 8px 32px rgba(0, 229, 255, 0.1)',
          transition: 'all 0.3s ease',
          '&:hover': {
            border: '1px solid rgba(0, 229, 255, 0.3)',
            boxShadow: '0 12px 48px rgba(0, 229, 255, 0.2)',
            transform: 'translateY(-2px)',
          },
        },
      },
    },
    MuiButton: {
      styleOverrides: {
        root: {
          borderRadius: '8px',
          padding: '10px 24px',
          boxShadow: 'none',
          transition: 'all 0.3s ease',
        },
        contained: {
          background: 'linear-gradient(135deg, #00e5ff 0%, #7c4dff 100%)',
          '&:hover': {
            background: 'linear-gradient(135deg, #62efff 0%, #b47cff 100%)',
            boxShadow: '0 8px 24px rgba(0, 229, 255, 0.3)',
            transform: 'translateY(-1px)',
          },
        },
        outlined: {
          border: '1px solid rgba(0, 229, 255, 0.5)',
          color: '#00e5ff',
          '&:hover': {
            border: '1px solid #00e5ff',
            background: 'rgba(0, 229, 255, 0.1)',
          },
        },
      },
    },
    MuiChip: {
      styleOverrides: {
        root: {
          borderRadius: '6px',
          fontWeight: 500,
        },
        filled: {
          '&.MuiChip-colorSuccess': {
            background: 'linear-gradient(135deg, #00e676 0%, #00c853 100%)',
            color: '#000000',
          },
          '&.MuiChip-colorError': {
            background: 'linear-gradient(135deg, #ff1744 0%, #d50000 100%)',
          },
          '&.MuiChip-colorWarning': {
            background: 'linear-gradient(135deg, #ffab00 0%, #ff8f00 100%)',
            color: '#000000',
          },
          '&.MuiChip-colorInfo': {
            background: 'linear-gradient(135deg, #00b0ff 0%, #0091ea 100%)',
          },
        },
      },
    },
    MuiPaper: {
      styleOverrides: {
        root: {
          background: 'rgba(26, 31, 46, 0.9)',
          backdropFilter: 'blur(20px)',
          border: '1px solid rgba(0, 229, 255, 0.1)',
        },
      },
    },
    MuiAppBar: {
      styleOverrides: {
        root: {
          background: 'rgba(10, 14, 26, 0.95)',
          backdropFilter: 'blur(20px)',
          borderBottom: '1px solid rgba(0, 229, 255, 0.2)',
          boxShadow: '0 4px 20px rgba(0, 229, 255, 0.1)',
        },
      },
    },
    MuiTableHead: {
      styleOverrides: {
        root: {
          background: 'linear-gradient(135deg, rgba(0, 229, 255, 0.1) 0%, rgba(124, 77, 255, 0.1) 100%)',
          '& .MuiTableCell-head': {
            fontWeight: 600,
            color: '#00e5ff',
            borderBottom: '2px solid rgba(0, 229, 255, 0.3)',
          },
        },
      },
    },
    MuiTableRow: {
      styleOverrides: {
        root: {
          '&:hover': {
            background: 'rgba(0, 229, 255, 0.05)',
          },
        },
      },
    },
  },
});

// Add custom color types to theme
declare module '@mui/material/styles' {
  interface Palette {
    tertiary: Palette['primary'];
    surface: Palette['primary'];
  }

  interface PaletteOptions {
    tertiary?: PaletteOptions['primary'];
    surface?: PaletteOptions['primary'];
  }
}

export default theme;
