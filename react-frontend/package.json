{"name": "react-frontend", "version": "0.1.0", "private": true, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@hookform/resolvers": "^5.0.1", "@mui/icons-material": "^7.1.0", "@mui/material": "^7.1.0", "@mui/x-charts": "^8.5.0", "@mui/x-data-grid": "^8.5.0", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^13.5.0", "@types/axios": "^0.9.36", "@types/jest": "^27.5.2", "@types/lodash": "^4.17.17", "@types/node": "^16.18.126", "@types/react": "^19.1.6", "@types/react-dom": "^19.1.5", "@types/react-router-dom": "^5.3.3", "@types/three": "^0.158.3", "axios": "^1.9.0", "date-fns": "^4.1.0", "lodash": "^4.17.21", "notistack": "^3.0.2", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.56.4", "react-router-dom": "^7.6.1", "react-scripts": "5.0.1", "recharts": "^2.15.3", "three": "^0.158.0", "typescript": "^4.9.5", "web-vitals": "^2.1.4", "yup": "^1.6.1"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}