# 🧬 Test Models for Mojo Bioinformatics Platform

This directory contains three metabolic models designed to test your Mojo-powered bioinformatics application.

## 📁 Available Models

### 1. **ecoli_core_extended.json** 
**🔬 Advanced E. coli Core Model**
- **Size**: 665 lines, comprehensive
- **Pathways**: Glycolysis, TCA cycle, respiratory chain
- **Reactions**: 25+ reactions including biomass
- **Best for**: Comprehensive FBA testing, performance benchmarking
- **Expected Growth Rate**: ~0.87 h⁻¹

### 2. **simple_ecoli.json**
**⚡ Simple E. coli Model**
- **Size**: 300 lines, minimal
- **Pathways**: Simplified respiration
- **Reactions**: 15 reactions
- **Best for**: Quick testing, debugging, learning
- **Expected Growth Rate**: Variable based on constraints

### 3. **yeast_glycolysis.json**
**🍺 Yeast Fermentation Model**
- **Size**: 400 lines, specialized
- **Pathways**: Glycolysis, alcoholic fermentation
- **Reactions**: 20+ reactions
- **Best for**: Testing fermentation pathways, ethanol production
- **Products**: Ethanol, CO₂, biomass

## 🧪 How to Test These Models

### **Method 1: Web Interface (Recommended)**
1. Open http://localhost:3000
2. Navigate to "Models" section
3. Click "Upload Model"
4. Select one of the JSON files
5. Run FBA analysis with different constraints

### **Method 2: API Testing**

**Upload a model:**
```bash
curl -X POST http://localhost:8080/api/v1/models \
  -H "Content-Type: application/json" \
  -d @test_models/simple_ecoli.json
```

**Run FBA analysis:**
```bash
curl -X POST http://localhost:8080/api/v1/models/{MODEL_ID}/fba \
  -H "Content-Type: application/json" \
  -d '{
    "objective": "BIOMASS",
    "constraints": {
      "EX_glc__D_e": -10.0
    }
  }'
```

## 🎯 Suggested Test Scenarios

### **Scenario 1: Basic Growth**
- **Model**: simple_ecoli.json
- **Objective**: BIOMASS
- **Constraints**: 
  - `EX_glc__D_e`: -10.0 (glucose uptake)
  - `EX_o2_e`: -20.0 (oxygen uptake)
- **Expected**: Positive growth rate

### **Scenario 2: Glucose Limitation**
- **Model**: ecoli_core_extended.json
- **Objective**: BIOMASS_Ecoli_core_w_GAM
- **Constraints**:
  - `EX_glc__D_e`: -5.0 (limited glucose)
  - `EX_o2_e`: unlimited
- **Expected**: Lower growth rate

### **Scenario 3: Anaerobic Conditions**
- **Model**: ecoli_core_extended.json
- **Objective**: BIOMASS_Ecoli_core_w_GAM
- **Constraints**:
  - `EX_glc__D_e`: -10.0
  - `EX_o2_e`: 0.0 (no oxygen)
- **Expected**: Reduced or zero growth

### **Scenario 4: Yeast Fermentation**
- **Model**: yeast_glycolysis.json
- **Objective**: BIOMASS
- **Constraints**:
  - `EX_glc__D_e`: -20.0
- **Expected**: Ethanol and CO₂ production

### **Scenario 5: Ethanol Production Optimization**
- **Model**: yeast_glycolysis.json
- **Objective**: EX_etoh_e (maximize ethanol)
- **Constraints**:
  - `EX_glc__D_e`: -15.0
  - `BIOMASS`: 0.1 (minimal growth)
- **Expected**: High ethanol flux

## 📊 Expected Results

### **E. coli Models**
```json
{
  "objective_value": 0.8739,
  "status": "optimal",
  "flux_values": {
    "BIOMASS_Ecoli_core_w_GAM": 0.8739,
    "EX_glc__D_e": -10.0,
    "EX_o2_e": -21.8,
    "EX_co2_e": 22.8
  }
}
```

### **Yeast Model**
```json
{
  "objective_value": 0.2156,
  "status": "optimal",
  "flux_values": {
    "BIOMASS": 0.2156,
    "EX_etoh_e": 15.6,
    "EX_co2_e": 18.2
  }
}
```

## 🔧 Troubleshooting

### **Model Upload Issues**
- Ensure JSON is valid (use jsonlint.com)
- Check file size < 10MB
- Verify all required fields are present

### **FBA Solver Issues**
- Check constraint feasibility
- Ensure objective reaction exists
- Verify metabolite IDs match exactly

### **Performance Testing**
- Use simple_ecoli.json for quick tests
- Use ecoli_core_extended.json for benchmarking
- Monitor solve times in results

## 🚀 Advanced Testing

### **Flux Variability Analysis (FVA)**
Test the range of possible flux values:
```bash
curl -X POST http://localhost:8080/api/v1/models/{MODEL_ID}/fva \
  -H "Content-Type: application/json" \
  -d '{
    "reactions": ["BIOMASS", "EX_glc__D_e"],
    "fraction_of_optimum": 0.9
  }'
```

### **Knockout Studies**
Test gene/reaction knockouts:
```bash
curl -X POST http://localhost:8080/api/v1/models/{MODEL_ID}/fba \
  -H "Content-Type: application/json" \
  -d '{
    "objective": "BIOMASS",
    "constraints": {
      "EX_glc__D_e": -10.0,
      "PFK": 0.0
    }
  }'
```

## 📈 Performance Benchmarks

**Target Performance (Mojo vs Python):**
- Simple model: < 0.01s vs < 0.05s
- Extended model: < 0.05s vs < 0.2s
- Yeast model: < 0.03s vs < 0.1s

**Memory Usage:**
- Simple: < 10MB
- Extended: < 50MB
- Yeast: < 30MB

---

**Happy Testing! 🧬⚡**

These models will help you validate that your Mojo bioinformatics platform is working correctly and performing well.
