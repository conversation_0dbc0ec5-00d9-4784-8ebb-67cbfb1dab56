# GEM-Fusion Accelerator Setup Guide

## Prerequisites

### System Requirements
- **Operating System**: Linux, macOS, or Windows with WSL2
- **Memory**: Minimum 4GB RAM, recommended 8GB+
- **Storage**: At least 2GB free space
- **Network**: Internet connection for downloading dependencies

### Software Dependencies

#### Required
- **Go 1.21+**: [Download Go](https://golang.org/dl/)
- **Mojo SDK**: [Install Mojo](https://docs.modular.com/mojo/manual/get-started/)
- **Docker & Docker Compose**: [Install Docker](https://docs.docker.com/get-docker/)
- **Python 3.9+**: For Mojo Python interop

#### Optional (for development)
- **Git**: Version control
- **Make**: Build automation
- **curl**: API testing
- **jq**: JSON processing

## Installation Methods

### Method 1: Docker Compose (Recommended)

This is the easiest way to get started with all services.

```bash
# Clone the repository
git clone <repository-url>
cd Bioinfo

# Start all services
docker-compose up --build

# The API will be available at http://localhost:8080
```

**Verify Installation:**
```bash
curl http://localhost:8080/health
```

### Method 2: Manual Setup

For development or custom configurations.

#### Step 1: Setup Go Backend

```bash
cd go-bioserv

# Install dependencies
go mod tidy

# Run tests
go test ./...

# Start the server
go run cmd/server/main.go
```

#### Step 2: Setup Mojo Compute Engine

```bash
cd mojo-metabolic-core

# Install Python dependencies
pip install -r requirements.txt

# Test Mojo installation
mojo --version

# Run tests
mojo run tests/test_fba.mojo

# Start compute engine (for IPC mode)
mojo run src/main.mojo test
```

#### Step 3: Start Services

```bash
# Terminal 1: Start Go backend
cd go-bioserv
go run cmd/server/main.go

# Terminal 2: Mojo compute engine runs on-demand via IPC
# No separate process needed
```

### Method 3: Development Setup

For active development with hot reloading.

#### Go Backend Development

```bash
cd go-bioserv

# Install development tools
go install github.com/cosmtrek/air@latest

# Start with hot reload
air
```

#### Mojo Development

```bash
cd mojo-metabolic-core

# Run specific tests
mojo run tests/test_fba.mojo

# Benchmark performance
mojo run src/main.mojo benchmark

# Test with sample model
mojo run src/main.mojo solve sample_models/ecoli_core.xml
```

## Configuration

### Environment Variables

#### Go Backend (`go-bioserv`)

| Variable | Default | Description |
|----------|---------|-------------|
| `PORT` | `8080` | Server port |
| `DATA_DIR` | `./data` | Data storage directory |
| `IPC_DIR` | `./ipc` | IPC communication directory |
| `MOJO_PATH` | `../mojo-metabolic-core/src/main.mojo` | Path to Mojo compute engine |
| `WORKERS` | `4` | Number of job queue workers |
| `TIMEOUT` | `30s` | Timeout for Mojo operations |

#### Mojo Compute Engine

| Variable | Default | Description |
|----------|---------|-------------|
| `PYTHONPATH` | `/app/src` | Python path for imports |
| `MOJO_PATH` | `/app/src` | Mojo source path |

### Configuration Files

#### Docker Compose Override

Create `docker-compose.override.yml` for custom settings:

```yaml
version: '3.8'
services:
  go-bioserv:
    environment:
      - WORKERS=8
      - TIMEOUT=60s
    ports:
      - "8081:8080"  # Use different port
```

#### Go Backend Configuration

Create `go-bioserv/config.yaml`:

```yaml
server:
  port: 8080
  timeout: 30s

storage:
  type: file
  path: ./data

compute:
  engine: mojo
  workers: 4
  timeout: 30s

logging:
  level: info
  format: json
```

## Verification

### Health Checks

```bash
# Check Go backend
curl http://localhost:8080/health

# Check service statistics
curl http://localhost:8080/stats

# Test Mojo compute engine
cd mojo-metabolic-core
mojo run src/main.mojo test
```

### End-to-End Test

```bash
# Upload a test model
curl -X POST http://localhost:8080/models \
  -F "file=@sample_models/ecoli_core.xml" \
  -F "name=Test Model"

# Submit FBA job
curl -X POST http://localhost:8080/models/{model_id}/fba \
  -H "Content-Type: application/json" \
  -d '{"objective": "BIOMASS_Ecoli_core_w_GAM"}'

# Check job status
curl http://localhost:8080/jobs/{job_id}/status

# Get results
curl http://localhost:8080/jobs/{job_id}/results
```

## Troubleshooting

### Common Issues

#### 1. Mojo Not Found

**Error:** `mojo: command not found`

**Solution:**
```bash
# Install Mojo SDK
curl -s https://get.modular.com | sh -
modular install mojo

# Add to PATH
export PATH="$HOME/.modular/pkg/packages.modular.com_mojo/bin:$PATH"
```

#### 2. Go Module Issues

**Error:** `go: module not found`

**Solution:**
```bash
cd go-bioserv
go mod tidy
go mod download
```

#### 3. Python Dependencies

**Error:** `ModuleNotFoundError: No module named 'scipy'`

**Solution:**
```bash
cd mojo-metabolic-core
pip install -r requirements.txt

# For system-wide installation
sudo apt-get install python3-scipy python3-numpy
```

#### 4. Docker Permission Issues

**Error:** `permission denied while trying to connect to Docker`

**Solution:**
```bash
# Add user to docker group
sudo usermod -aG docker $USER
newgrp docker

# Or use sudo
sudo docker-compose up --build
```

#### 5. Port Already in Use

**Error:** `bind: address already in use`

**Solution:**
```bash
# Find process using port 8080
lsof -i :8080

# Kill process or use different port
export PORT=8081
go run cmd/server/main.go
```

### Performance Issues

#### 1. Slow FBA Solving

**Symptoms:** FBA jobs take longer than expected

**Solutions:**
- Install optimized linear programming solvers (GLPK, Gurobi)
- Increase worker count: `WORKERS=8`
- Use SSD storage for data directory
- Ensure sufficient RAM (8GB+)

#### 2. High Memory Usage

**Symptoms:** System runs out of memory

**Solutions:**
- Reduce worker count: `WORKERS=2`
- Limit concurrent jobs
- Use swap space
- Monitor with `docker stats`

### Debugging

#### Enable Verbose Logging

```bash
# Go backend
go run cmd/server/main.go --verbose

# Mojo compute engine
mojo run src/main.mojo ipc request.json response.json --verbose

# Docker Compose
docker-compose up --build --verbose
```

#### Check Logs

```bash
# Docker logs
docker-compose logs go-bioserv
docker-compose logs mojo-compute

# Follow logs
docker-compose logs -f
```

#### Debug IPC Communication

```bash
# Check IPC directory
ls -la ./ipc/

# Monitor IPC files
watch -n 1 'ls -la ./ipc/'

# Test IPC manually
echo '{"model_path": "test.xml", "objective": "BIOMASS"}' > request.json
mojo run src/main.mojo ipc request.json response.json --verbose
cat response.json
```

## Development Workflow

### Code Organization

```
Bioinfo/
├── go-bioserv/           # Go backend
│   ├── cmd/              # Main applications
│   ├── internal/         # Private application code
│   ├── pkg/              # Public library code
│   └── tests/            # Test files
├── mojo-metabolic-core/  # Mojo compute engine
│   ├── src/              # Source code
│   └── tests/            # Test files
└── docs/                 # Documentation
```

### Testing

```bash
# Run all Go tests
cd go-bioserv && go test ./...

# Run specific test
go test ./internal/api -v

# Run Mojo tests
cd mojo-metabolic-core
mojo run tests/test_fba.mojo

# Integration tests
./scripts/integration_test.sh
```

### Building

```bash
# Build Go binary
cd go-bioserv
go build -o bin/server cmd/server/main.go

# Build Docker images
docker-compose build

# Build for production
docker-compose -f docker-compose.prod.yml build
```

## Production Deployment

### Security Considerations

1. **API Authentication**: Implement API keys or JWT tokens
2. **HTTPS**: Use TLS certificates
3. **Firewall**: Restrict access to necessary ports
4. **User Permissions**: Run services as non-root users
5. **Input Validation**: Validate all uploaded files

### Scaling

1. **Horizontal Scaling**: Deploy multiple instances behind load balancer
2. **Database**: Migrate from file storage to PostgreSQL
3. **Queue**: Use Redis for job queue
4. **Monitoring**: Add Prometheus and Grafana
5. **Caching**: Implement result caching

### Monitoring

```bash
# Enable monitoring stack
docker-compose --profile monitoring up -d

# Access Grafana
open http://localhost:3000

# Access Prometheus
open http://localhost:9090
```

## Support

### Getting Help

1. **Documentation**: Check this guide and API specification
2. **Issues**: Report bugs on GitHub
3. **Discussions**: Join community discussions
4. **Email**: Contact support team

### Contributing

1. Fork the repository
2. Create feature branch
3. Add tests for new functionality
4. Submit pull request
5. Follow code review process

### License

This project is licensed under the MIT License. See LICENSE file for details.
