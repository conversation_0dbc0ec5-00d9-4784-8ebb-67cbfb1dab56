# GEM-Fusion Accelerator API Specification

## Overview

The GEM-Fusion Accelerator provides a RESTful API for genome-scale metabolic model analysis. This document describes all available endpoints, request/response formats, and usage examples.

## Base URL

```
http://localhost:8080
```

## Authentication

Currently, no authentication is required. Future versions will implement API key-based authentication.

## Content Types

- Request: `application/json` or `multipart/form-data` (for file uploads)
- Response: `application/json`

## Standard Response Format

All API responses follow this standard format:

```json
{
  "success": true,
  "message": "Optional success message",
  "data": {}, 
  "error": "Optional error message"
}
```

## Endpoints

### Health Check

#### GET /health

Check service health status.

**Response:**
```json
{
  "success": true,
  "message": "Service is healthy",
  "data": {
    "service": "go-bioserv",
    "version": "1.0.0",
    "status": "running"
  }
}
```

### Statistics

#### GET /stats

Get service statistics.

**Response:**
```json
{
  "success": true,
  "data": {
    "total_models": 5,
    "formats": {
      "sbml": 3,
      "json": 2
    }
  }
}
```

## Model Management

### Upload Model

#### POST /models

Upload a new metabolic model.

**Request:** `multipart/form-data`
- `file`: Model file (SBML or JSON format)
- `name`: Model name (optional, defaults to filename)
- `description`: Model description (optional)

**Example:**
```bash
curl -X POST http://localhost:8080/models \
  -F "file=@ecoli_core.xml" \
  -F "name=E.coli Core Model" \
  -F "description=Core metabolic model of E. coli"
```

**Response:**
```json
{
  "success": true,
  "message": "Model uploaded successfully",
  "data": {
    "id": "550e8400-e29b-41d4-a716-446655440000",
    "name": "E.coli Core Model",
    "description": "Core metabolic model of E. coli",
    "format": "sbml",
    "file_path": "/app/data/models/550e8400-e29b-41d4-a716-446655440000/ecoli_core.xml",
    "created_at": "2024-01-15T10:30:00Z",
    "updated_at": "2024-01-15T10:30:00Z"
  }
}
```

### Get Model

#### GET /models/{model_id}

Retrieve model metadata by ID.

**Parameters:**
- `model_id`: Unique model identifier

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "550e8400-e29b-41d4-a716-446655440000",
    "name": "E.coli Core Model",
    "description": "Core metabolic model of E. coli",
    "format": "sbml",
    "file_path": "/app/data/models/550e8400-e29b-41d4-a716-446655440000/ecoli_core.xml",
    "created_at": "2024-01-15T10:30:00Z",
    "updated_at": "2024-01-15T10:30:00Z"
  }
}
```

### List Models

#### GET /models

List all uploaded models.

**Response:**
```json
{
  "success": true,
  "data": {
    "models": [
      {
        "id": "550e8400-e29b-41d4-a716-446655440000",
        "name": "E.coli Core Model",
        "description": "Core metabolic model of E. coli",
        "format": "sbml",
        "created_at": "2024-01-15T10:30:00Z"
      }
    ],
    "total": 1
  }
}
```

## Analysis

### Submit FBA Job

#### POST /models/{model_id}/fba

Submit a Flux Balance Analysis job.

**Parameters:**
- `model_id`: Model identifier

**Request Body:**
```json
{
  "objective": "BIOMASS_Ecoli_core_w_GAM",
  "constraints": {
    "EX_glc__D_e": -10.0,
    "EX_o2_e": -20.0
  },
  "options": {
    "solver": "glpk",
    "tolerance": 1e-9
  }
}
```

**Response:**
```json
{
  "success": true,
  "message": "FBA job submitted successfully",
  "data": {
    "id": "job_123456",
    "model_id": "550e8400-e29b-41d4-a716-446655440000",
    "type": "fba",
    "status": "pending",
    "input": {
      "objective": "BIOMASS_Ecoli_core_w_GAM",
      "constraints": {
        "EX_glc__D_e": -10.0,
        "EX_o2_e": -20.0
      }
    },
    "created_at": "2024-01-15T10:35:00Z"
  }
}
```

## Job Management

### Get Job Status

#### GET /jobs/{job_id}/status

Check the status of a submitted job.

**Parameters:**
- `job_id`: Job identifier

**Response:**
```json
{
  "success": true,
  "data": {
    "job": {
      "id": "job_123456",
      "model_id": "550e8400-e29b-41d4-a716-446655440000",
      "type": "fba",
      "status": "completed",
      "created_at": "2024-01-15T10:35:00Z",
      "started_at": "2024-01-15T10:35:05Z",
      "ended_at": "2024-01-15T10:35:08Z"
    },
    "progress": 1.0
  }
}
```

**Job Status Values:**
- `pending`: Job is queued for processing
- `running`: Job is currently being processed
- `completed`: Job finished successfully
- `failed`: Job failed with an error

### Get Job Results

#### GET /jobs/{job_id}/results

Retrieve results from a completed job.

**Parameters:**
- `job_id`: Job identifier

**Response:**
```json
{
  "success": true,
  "data": {
    "objective_value": 0.8739215,
    "flux_values": {
      "BIOMASS_Ecoli_core_w_GAM": 0.8739215,
      "EX_glc__D_e": -10.0,
      "EX_o2_e": -21.799493,
      "EX_co2_e": 22.809833,
      "EX_h2o_e": 29.175827,
      "EX_h_e": 17.530865,
      "EX_pi_e": -3.2149
    },
    "status": "optimal",
    "solver_time": 0.045
  }
}
```

## Error Handling

### Error Response Format

```json
{
  "success": false,
  "error": "Detailed error message",
  "data": null
}
```

### HTTP Status Codes

- `200 OK`: Request successful
- `201 Created`: Resource created successfully
- `202 Accepted`: Request accepted for processing
- `400 Bad Request`: Invalid request format or parameters
- `404 Not Found`: Resource not found
- `500 Internal Server Error`: Server error

### Common Error Scenarios

#### Model Not Found
```json
{
  "success": false,
  "error": "Model not found: invalid_model_id"
}
```

#### Job Not Completed
```json
{
  "success": false,
  "error": "Job not completed, current status: running"
}
```

#### Invalid File Format
```json
{
  "success": false,
  "error": "Unsupported file format. Please upload SBML (.xml) or JSON (.json) files."
}
```

## Rate Limiting

Currently, no rate limiting is implemented. Future versions will include:
- 100 requests per minute per IP
- 10 concurrent FBA jobs per user

## Examples

### Complete Workflow Example

```bash
# 1. Upload a model
MODEL_RESPONSE=$(curl -s -X POST http://localhost:8080/models \
  -F "file=@ecoli_core.xml" \
  -F "name=E.coli Core Model")

MODEL_ID=$(echo $MODEL_RESPONSE | jq -r '.data.id')

# 2. Submit FBA job
JOB_RESPONSE=$(curl -s -X POST http://localhost:8080/models/$MODEL_ID/fba \
  -H "Content-Type: application/json" \
  -d '{
    "objective": "BIOMASS_Ecoli_core_w_GAM",
    "constraints": {
      "EX_glc__D_e": -10.0
    }
  }')

JOB_ID=$(echo $JOB_RESPONSE | jq -r '.data.id')

# 3. Check job status
while true; do
  STATUS_RESPONSE=$(curl -s http://localhost:8080/jobs/$JOB_ID/status)
  STATUS=$(echo $STATUS_RESPONSE | jq -r '.data.job.status')
  
  if [ "$STATUS" = "completed" ]; then
    break
  elif [ "$STATUS" = "failed" ]; then
    echo "Job failed"
    exit 1
  fi
  
  sleep 1
done

# 4. Get results
curl -s http://localhost:8080/jobs/$JOB_ID/results | jq '.data'
```

### Python Client Example

```python
import requests
import time
import json

# Upload model
with open('ecoli_core.xml', 'rb') as f:
    files = {'file': f}
    data = {'name': 'E.coli Core Model'}
    response = requests.post('http://localhost:8080/models', files=files, data=data)
    model_id = response.json()['data']['id']

# Submit FBA job
fba_request = {
    'objective': 'BIOMASS_Ecoli_core_w_GAM',
    'constraints': {
        'EX_glc__D_e': -10.0
    }
}
response = requests.post(f'http://localhost:8080/models/{model_id}/fba', json=fba_request)
job_id = response.json()['data']['id']

# Wait for completion
while True:
    response = requests.get(f'http://localhost:8080/jobs/{job_id}/status')
    status = response.json()['data']['job']['status']
    
    if status == 'completed':
        break
    elif status == 'failed':
        raise Exception('Job failed')
    
    time.sleep(1)

# Get results
response = requests.get(f'http://localhost:8080/jobs/{job_id}/results')
results = response.json()['data']
print(f"Objective value: {results['objective_value']}")
```

## Versioning

The API uses URL-based versioning. Current version is v1:
- Legacy endpoints: `/models`, `/jobs`
- Versioned endpoints: `/api/v1/models`, `/api/v1/jobs`

Both formats are currently supported for backward compatibility.
